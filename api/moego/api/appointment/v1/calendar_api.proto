// @since 2024-05-09 11:44:04
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.appointment.v1;

import "google/type/date.proto";
import "google/type/latlng.proto";
import "moego/models/appointment/v1/appointment_defs.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/appointment/v1/calendar_enums.proto";
import "moego/models/appointment/v1/pet_detail_defs.proto";
import "moego/models/business_customer/v1/business_customer_address_models.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/membership/v1/subscription_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/online_booking/v1/grooming_auto_assign_models.proto";
import "moego/models/order/v1/invoice_models.proto";
import "moego/models/order/v1/order_models.proto";
import "moego/models/organization/v1/staff_availability_models.proto";
import "moego/models/payment/v1/deposit_enums.proto";
import "moego/models/payment/v1/pre_auth_models.proto";
import "moego/models/payment/v1/pre_pay_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// list day cards params
message ListDayCardsParams {
  // the start date
  google.type.Date start_date = 1 [(validate.rules).message = {required: true}];

  // the end date
  google.type.Date end_date = 2 [(validate.rules).message = {required: true}];

  // selected the business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];

  // filter no start time
  bool filter_no_start_time = 6;

  // filter no staff
  bool filter_no_staff = 7;
}

// list day cards result
message ListDayCardsResult {
  // the list of calendar cards
  repeated CalendarCardCompositeView cards = 1;

  // calendar card
  message CalendarCardCompositeView {
    // Globally unique ID, type + date + id, eg: APPOINTMENT_2024-05-09_5686153
    string card_id = 1;

    // The unique ID corresponding to each card type
    int64 id = 2;

    // the card type
    moego.models.appointment.v1.CalendarCardType card_type = 3;

    // appointment status
    optional moego.models.appointment.v1.AppointmentStatus appointment_status = 4;

    // appointment id
    int64 appointment_id = 5;

    // staff id, If there is no staff id, it means no staff assigned
    optional int64 staff_id = 6;

    // the start date
    string date = 7;

    // the end date
    string end_date = 8;

    // the start time, in minutes
    int64 start_time = 9;

    // the end time, in minutes
    int64 end_time = 10;

    // appointment payment status
    optional moego.models.appointment.v1.AppointmentPaymentStatus payment_status = 11;

    // pre-auth
    optional moego.models.payment.v1.PreAuthCalendarView pre_auth_info = 12;

    // required sign agreement
    optional bool required_sign = 13;

    // alert notes
    optional string alert_notes = 14;

    // ticket comments
    optional string ticket_comments = 15;

    // repeat id, used for repeat appointment
    optional int64 repeat_id = 16;

    // color code
    string color_code = 17;

    // booking request id
    optional int64 booking_request_id = 18;

    // is auto accept, used for online booking auto accept
    optional bool is_auto_accept = 19;

    // auto assign, staff and start time for auto assign
    optional moego.models.online_booking.v1.GroomingAutoAssignView auto_assign = 20;

    // client info
    optional CalendarCardClientInfo client_info = 21;

    // pet info
    repeated CalendarCardPetInfo pet_list = 22;

    // estimated total price
    optional double estimated_total_price = 23;

    // paid amount
    optional double paid_amount = 24;

    // prepaid amount
    optional double prepaid_amount = 25;

    // prepay status, used for prepaid booking request
    optional moego.models.payment.v1.DepositStatus prepay_status = 26;

    // pet detail ids, used for reschedule card
    repeated int32 pet_detail_ids = 27;

    // draggable info, used for reschedule card
    CalendarCardDraggableInfo draggable_info = 28;

    // service item types
    repeated moego.models.offering.v1.ServiceItemType service_item_types = 29;

    // memberships
    moego.models.membership.v1.MembershipSubscriptionListModel membership_subscriptions = 30;

    // invoice. please use orders instead
    models.order.v1.InvoiceCalendarView invoice = 31 [deprecated = true];

    // orders
    repeated models.order.v1.OrderModelAppointmentView orders = 32;
  }

  // the client info for calendar card view
  message CalendarCardClientInfo {
    // the customer id
    int64 client_id = 1;
    // the customer last name
    string customer_last_name = 2;
    // the customer first name
    string customer_first_name = 3;
    // the color code
    string client_color = 4;
    // new flag
    bool is_new_client = 5;
    // full address name
    string full_address = 6;
    // certain areas
    repeated CalendarCardCertainArea areas = 7;
    // address line1
    string address1 = 8;
    // address line2
    string address2 = 9;
    // country
    string country = 10;
    // address state
    string state = 11;
    // address city
    string city = 12;
    // address zipcode
    string zipcode = 13;
    // lat lng
    google.type.LatLng coordinate = 14;
  }

  // the certain area for calendar card view
  message CalendarCardCertainArea {
    // area id
    int64 area_id = 1;
    // area name
    string area_name = 2;
    // color code
    string color_code = 3;
  }

  // the pet info for calendar card view
  message CalendarCardPetInfo {
    // the pet id
    int32 pet_id = 1;
    // the pet name
    string pet_name = 2;
    // the pet breed name
    string pet_breed_name = 3;
    // the pet code list
    repeated CalendarCardPetCodeInfo pet_code_list = 4;
    // the service info list
    repeated CalendarCardServiceInfo service_list = 5;
    // the vaccine alert info list, deprecated
    repeated CalendarCardVaccineAlertInfo vaccine_alerts = 6 [deprecated = true];
    // the evaluation detail list
    repeated CalendarCardEvaluationInfo evaluations = 7;
    // pet type
    moego.models.customer.v1.PetType pet_type = 8;
    // enable vaccine expiry notification, ture is enable, false is disable
    bool enable_vaccine_expiry_notification = 9;
    // the vaccine list
    repeated CalendarCardVaccineInfo vaccines = 10;
    // pet size id
    int64 pet_size_id = 11;
  }

  // the pet code info for calendar card view
  message CalendarCardPetCodeInfo {
    // the pet id
    int32 pet_id = 1;
    // the pet code id
    int32 pet_code_id = 2;
    // the pet code number
    string code_number = 3;
    // the pet code description
    string description = 4;
    // the pet code color
    string color = 5;
  }

  // the evaluation service detail
  message CalendarCardEvaluationInfo {
    // the evaluation detail id
    int64 evaluation_detail_id = 1;
    // the service id
    int64 service_id = 2;
    // the service name
    string service_name = 3;
    // the service color code
    string color_code = 4;
    // the service time, in minutes
    int32 service_time = 5;
    // the service price
    double service_price = 6;
  }

  // the service info for calendar card view
  message CalendarCardServiceInfo {
    // the pet detail id
    int32 pet_detail_id = 1;
    // the service id
    int64 service_id = 2;
    // the service name
    string service_name = 3;
    // the service color code
    string color_code = 4;
    // the service time, in minutes
    int32 service_time = 5;
    // the service price
    double service_price = 6;
  }

  // the pet vaccine alert for calendar card view
  message CalendarCardVaccineAlertInfo {
    // the vaccine id
    int32 vaccine_id = 1;
    // the vaccine name
    string vaccine_name = 2;
    // the expiration date
    string expiration_date = 3;
  }

  // the pet vaccine info for calendar card view
  message CalendarCardVaccineInfo {
    // the vaccine id
    int32 vaccine_id = 1;
    // the vaccine name
    string vaccine_name = 2;
    // the expiration date
    string expiration_date = 3;
  }

  // the draggable info for calendar card view
  message CalendarCardDraggableInfo {
    // draggable flag
    bool draggable = 1;
    // draggable earliest date
    optional string earliest_date = 2;
    // draggable latest date
    optional string latest_date = 3;
    // unavailable draggable staff ids
    repeated int64 unavailable_staff_list = 4;
    // allowed to no assigned staff
    bool allowed_no_assigned_staff = 5;
  }
}

// list month cards params
message ListMonthCardsParams {
  // the start date
  google.type.Date start_date = 1 [(validate.rules).message = {required: true}];

  // the end date
  google.type.Date end_date = 2 [(validate.rules).message = {required: true}];

  // selected the business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];

  // is waiting list
  bool is_waiting_list = 5;

  // filter no start time
  bool filter_no_start_time = 6;

  // filter no staff
  bool filter_no_staff = 7;

  // predicate
  optional moego.utils.v2.Predicate predicate = 8;
}

// list month cards result
message ListMonthCardsResult {
  // the list of month cards
  repeated CalendarCardSimpleView cards = 1;

  // calendar card simple view
  message CalendarCardSimpleView {
    // Globally unique ID, type + date + id, eg: APPOINTMENT_2024-05-09_5686153
    string card_id = 1;

    // The unique ID corresponding to each card type
    int64 id = 2;

    // the card type
    moego.models.appointment.v1.CalendarCardType card_type = 3;

    // appointment status
    optional moego.models.appointment.v1.AppointmentStatus appointment_status = 4;

    // appointment id
    int64 appointment_id = 5;

    // staff id
    optional int64 staff_id = 6;

    // booking request id
    optional int64 booking_request_id = 7;

    // the appointment start date
    string appointment_date = 8;

    // the start time, in minutes
    int64 start_time = 9;

    // the end time, in minutes
    int64 end_time = 10;

    // repeat id, used for repeat appointment
    optional int64 repeat_id = 11;

    // color code
    string color_code = 12;

    // is auto accept, used for online booking auto accept
    bool is_auto_accept = 13;

    // auto assign, staff and start time for auto assign
    optional moego.models.online_booking.v1.GroomingAutoAssignView auto_assign = 14;

    // pet detail ids
    repeated int32 pet_detail_ids = 15;

    // pet detail id
    int32 pet_detail_id = 16;

    // the customer id
    optional int64 customer_id = 17;

    // the customer last name
    optional string customer_last_name = 18;

    // the customer first name
    optional string customer_first_name = 19;

    // the color code
    optional string customer_color = 20;

    // the service color code
    string service_color_code = 21;

    // no start time flag
    bool no_start_time = 22;

    // is block time flag
    bool is_block = 23;

    // the block time description
    string desc = 24;

    // service item types
    repeated moego.models.offering.v1.ServiceItemType service_item_types = 29;
  }
}

// The params for preview calendar cards
message PreviewCalendarCardsParams {
  // Selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected customer id
  int64 customer_id = 2 [(validate.rules).int64.gt = 0];

  // Selected appointment id
  // Used to edit schedule
  optional int64 appointment_id = 3 [(validate.rules).int64.gt = 0];

  // Multi pets start at the same time, default to false
  bool all_pets_start_at_same_time = 4;

  // Appointment calendar schedule
  moego.models.appointment.v1.AppointmentCalendarScheduleDef appointment_schedule = 5 [(validate.rules).message = {required: true}];

  // Selected pet and services
  repeated models.appointment.v1.PetServiceCalendarDef pet_services = 6 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Original pet id
  // Apply to existing appointment modify directly replace pet scenario
  optional int64 original_pet_id = 8 [(validate.rules).int64 = {gt: 0}];
}

// The result for preview calendar cards
message PreviewCalendarCardsResult {
  // Selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected customer id
  int64 customer_id = 2 [(validate.rules).int64.gt = 0];

  // Selected appointment id
  // Used to edit schedule
  optional int64 appointment_id = 3 [(validate.rules).int64.gt = 0];

  // Multi pets start at the same time, default to false
  bool all_pets_start_at_same_time = 4;

  // Appointment calendar schedule
  moego.models.appointment.v1.AppointmentScheduleDef appointment_schedule = 5 [(validate.rules).message = {required: true}];

  // Selected pet and service schedules
  repeated models.appointment.v1.PetServiceCalendarScheduleDef pet_service_schedules = 6 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // The list of calendar cards
  // Used to preview the calendar cards
  repeated CalendarCardView cards = 7;
}

// Calendar card view
message CalendarCardView {
  // UUID, Back-end memory generation for front-end rendering components
  string card_id = 1;

  // Has related split cards
  // If true, a pop-up window is required to indicate whether all cards are affected.
  bool has_related_split_cards = 2;

  // the card type
  moego.models.appointment.v1.CalendarCardType card_type = 3;

  // appointment status
  optional moego.models.appointment.v1.AppointmentStatus appointment_status = 4;

  // appointment id
  int64 appointment_id = 5;

  // staff id, If there is no staff id, it means no staff assigned
  optional int64 staff_id = 6;

  // the start date
  string date = 7;

  // the end date
  string end_date = 8;

  // the start time, in minutes
  int32 start_time = 9;

  // the end time, in minutes
  int32 end_time = 10;

  // appointment payment status
  optional moego.models.appointment.v1.AppointmentPaymentStatus payment_status = 11;

  // pre-auth
  optional moego.models.payment.v1.PreAuthCalendarView pre_auth_info = 12;

  // required sign agreement
  optional bool required_sign = 13;

  // alert notes
  optional string alert_notes = 14;

  // ticket comments
  optional string ticket_comments = 15;

  // repeat id, used for repeat appointment
  optional int64 repeat_id = 16;

  // appointment color code
  string appointment_color = 17;

  // booking request id
  optional int64 booking_request_id = 18;

  // is auto accept, used for online booking auto accept
  optional bool is_auto_accept = 19;

  // auto assign, staff and start time for auto assign
  optional moego.models.online_booking.v1.GroomingAutoAssignView auto_assign = 20;

  // customer info
  optional CalendarCardCustomerInfo customer_info = 21;

  // pet info
  repeated CalendarCardPetInfo pets = 22;

  // estimated total price
  optional double estimated_total_price = 23;

  // paid amount
  optional double paid_amount = 24;

  // prepaid amount
  optional models.payment.v1.PrePayCalendarView pre_pay_info = 25;

  // pet detail ids, used for reschedule card
  repeated int64 pet_detail_ids = 27;

  // draggable info, used for reschedule card
  CalendarCardDraggableInfo draggable_info = 28;

  // service item types
  repeated moego.models.offering.v1.ServiceItemType service_item_types = 29;

  // memberships
  optional moego.models.membership.v1.MembershipSubscriptionListModel membership_subscriptions = 30;

  // invoice
  models.order.v1.InvoiceCalendarView invoice = 31;

  // hit predicate
  bool hit_predicate = 32;
}

// the customer info for calendar card view
message CalendarCardCustomerInfo {
  // the customer id
  int64 customer_id = 1;
  // the customer last name
  string last_name = 2;
  // the customer first name
  string first_name = 3;
  // the color code
  string client_color = 4;
  // new flag
  bool is_new_client = 5;
  // primary address
  models.business_customer.v1.BusinessCustomerAddressView primary_address = 6;
  // certain areas
  repeated CalendarCardCertainArea areas = 7;
}

// the certain area for calendar card view
message CalendarCardCertainArea {
  // area id
  int64 area_id = 1;
  // area name
  string area_name = 2;
  // color code
  string color_code = 3;
}

// the pet info for calendar card view
message CalendarCardPetInfo {
  // the pet id
  int64 pet_id = 1;
  // the pet name
  string pet_name = 2;
  // the pet breed name
  string breed = 3;
  // the pet code list
  repeated CalendarCardPetCodeInfo pet_codes = 4;
  // the service info list
  repeated CalendarCardServiceInfo services = 5;
  // the vaccine list
  repeated CalendarCardVaccineInfo vaccines = 6;
  // the evaluation detail list
  repeated CalendarCardEvaluationInfo evaluations = 7;
  // pet type
  moego.models.customer.v1.PetType pet_type = 8;
  // enable vaccine expiry notification, ture is enable, false is disable
  bool enable_vaccine_expiry_notification = 9;
  // pet size id
  int64 pet_size_id = 10;
}

// the pet code info for calendar card view
message CalendarCardPetCodeInfo {
  // the pet code id
  int32 pet_code_id = 1;
  // the pet code abbreviation
  string abbreviation = 2;
  // the pet code description
  string description = 3;
  // the pet code color
  string color = 4;
}

// the evaluation service detail
message CalendarCardEvaluationInfo {
  // the evaluation detail id
  int64 evaluation_detail_id = 1;
  // the service id
  int64 service_id = 2;
  // the service name
  string service_name = 3;
  // the service color code
  string color_code = 4;
  // the service time, in minutes
  int32 service_time = 5;
  // the service price
  double service_price = 6;
}

// the service info for calendar card view
message CalendarCardServiceInfo {
  // the pet detail id
  int64 pet_detail_id = 1;
  // the service id
  int64 service_id = 2;
  // the service name
  string service_name = 3;
  // the service color code
  string color_code = 4;
  // the service time, in minutes
  int32 service_time = 5;
  // the service price
  double service_price = 6;
  // The start time of the service, unit minute, 540 means 09:00
  optional int32 start_time = 7;
  // The end time of the service, unit minute, 540 means 09:00
  optional int32 end_time = 8;
}

// the pet vaccine for calendar card view
message CalendarCardVaccineInfo {
  // the vaccine id
  int64 vaccine_id = 1;
  // the vaccine name
  string vaccine_name = 2;
  // the expiration date
  string expiration_date = 3;
}

// the draggable info for calendar card view
message CalendarCardDraggableInfo {
  // draggable flag
  bool draggable = 1;
  // draggable earliest date
  optional string earliest_date = 2;
  // draggable latest date
  optional string latest_date = 3;
  // unavailable draggable staff ids
  repeated int64 unavailable_staff_list = 4;
  // allowed to no assigned staff
  bool allowed_no_assigned_staff = 5;
  // resize flag, elongation or shortening
  bool resize = 6;
  // Not be resize reason
  NotResizeReason not_resize_reason = 7;

  // Not be resize reasons
  enum NotResizeReason {
    // unspecified
    NOT_RESIZE_REASON_UNSPECIFIED = 0;
    // Appointment finished
    APPOINTMENT_FINISHED = 1;
    // Include multiple services
    INCLUDE_MULTIPLE_SERVICES = 2;
    // Is a booking request card
    IS_BOOKING_REQUEST = 3;
  }
}

// The params of list day cards with mix type
message ListDayCardsWithMixTypeParams {
  // the start date
  google.type.Date start_date = 1 [(validate.rules).message = {required: true}];

  // the end date
  google.type.Date end_date = 2 [(validate.rules).message = {required: true}];

  // selected the business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];

  // filter no start time
  bool filter_no_start_time = 6;

  // filter no staff
  bool filter_no_staff = 7;

  // predicate
  optional moego.utils.v2.Predicate predicate = 8;

  // view type
  optional moego.models.appointment.v1.ViewType view_type = 9 [(validate.rules).enum = {defined_only: true}];
}

// The result of list day cards with mix type
message ListDayCardsWithMixTypeResult {
  // the list of calendar cards
  repeated CalendarCardView cards = 1;
}

// list day slot infos result
message ListDaySlotInfosParams {
  // the start date
  string start_date = 1 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // the end date
  string end_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // selected the business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// list day slot infos result
message ListDaySlotInfosResult {
  // the list of day slot infos
  repeated DaySlotInfo slot_infos = 1;
  // the list of setting day slot infos
  repeated SettingDaySlotInfo setting_slot_infos = 2;

  // day slot info
  message DaySlotInfo {
    // the start date
    string date = 1;
    // the staff id
    int64 staff_id = 2;
    // the start time, in minutes
    int32 start_time = 3;
    // the end time, in minutes
    int32 end_time = 4;
    // pet/family capacity
    int32 pet_capacity = 5;
    // used capacity
    int32 used_pet_capacity = 6;
    // used family capacity
    int32 used_family_capacity = 7;
    // limitation groups
    repeated moego.models.organization.v1.LimitationGroup limitation_groups = 8;
    // include slot free services
    bool include_slot_free_services = 9;
    // limitation note
    string note = 10;
    // limitation group hit views
    repeated moego.models.organization.v1.LimitationGroupHitView limitation_group_hit_views = 11;
    // hit limit
    bool hit_limit = 12;
  }

  // setting day slot info
  message SettingDaySlotInfo {
    // the start date
    string date = 1;
    // the staff id
    int64 staff_id = 2;
    // pet/family capacity
    int32 pet_capacity = 5;
    // used capacity
    int32 used_pet_capacity = 6;
    // appointment count
    int32 appointment_count = 7;
    // limitation groups
    repeated moego.models.organization.v1.LimitationGroup limitation_groups = 8;
  }
}

// the calendar service
service CalendarService {
  // List day or week calendar view cards
  // Only the following specific types of cards are returned
  //  - APPOINTMENT
  //  - SERVICE (one)
  //  - OPERATION (one)
  //  - BLOCK
  //  - BOOKING_REQUEST
  rpc ListDayCards(ListDayCardsParams) returns (ListDayCardsResult);

  // List month calendar view cards
  // fields: start_date, start_time, client_first_name, color_code, booking_type(pending, scheduled)
  rpc ListMonthCards(ListMonthCardsParams) returns (ListMonthCardsResult);

  // Preview calendar view cards
  rpc PreviewCalendarCards(PreviewCalendarCardsParams) returns (PreviewCalendarCardsResult);

  // List day or week calendar view cards with mix type
  // Compared to the @see [ListDayCards](#moego.api.appointment.v1.CalendarService.ListDayCards)
  // There are more of the following types of cards
  //  - APPOINTMENT
  //  - SERVICE (one or more services)
  //  - OPERATION (one or more operations)
  //  - SERVICE_AND_OPERATION (one or more services and operations)
  //  - BLOCK
  //  - BOOKING_REQUEST
  rpc ListDayCardsWithMixType(ListDayCardsWithMixTypeParams) returns (ListDayCardsWithMixTypeResult);

  // List day slot infos
  rpc ListDaySlotInfos(ListDaySlotInfosParams) returns (ListDaySlotInfosResult);
}
