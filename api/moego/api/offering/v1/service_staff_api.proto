// @since 2024-08-21 00:05:46
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.api.offering.v1;

import "moego/models/offering/v1/service_enum.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.offering.v1";

// list staff for service
message ListServiceStaffsParams {
  // service id
  int64 service_id = 1 [(validate.rules).int64.gt = 0];
  // business id, if empty, will use the company setting
  // 这里需要考虑 staff working location，需要传 business_id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // pet id, if not empty, will use the customized price by pet (if exist customized price)
  optional int64 pet_id = 3 [(validate.rules).int64.gt = 0];
  // pagination
  utils.v2.PaginationRequest pagination = 5 [(validate.rules).message = {required: true}];
  // customer id
  optional int64 customer_id = 6 [(validate.rules).int64.gt = 0];
}

// list staff for service result
message ListServiceStaffsResult {
  // staff with service price and duration
  message StaffWithServicePriceAndDurationView {
    // staff id
    int64 staff_id = 1;
    // first name
    string first_name = 2;
    // last name
    string last_name = 3;
    // staff avatar
    string avatar_path = 4;
    // service price
    double service_price = 5;
    // price override type, if not set, means no override
    optional models.offering.v1.ServiceOverrideType price_override_type = 6;
    // service duration
    int32 service_duration = 7;
    // duration override type, if not set, means no override
    optional models.offering.v1.ServiceOverrideType duration_override_type = 8;
    // is available
    bool is_available = 9;
  }
  // staff list
  repeated StaffWithServicePriceAndDurationView staffs = 1;
  // pagination
  utils.v2.PaginationResponse pagination = 2;
}

// list staff for evaluation service
message ListEvaluationStaffsParams {
  // evaluation id
  int64 evaluation_id = 1 [(validate.rules).int64.gt = 0];
  // 这里需要考虑 staff working location，需要传 business_id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // pagination
  utils.v2.PaginationRequest pagination = 3 [(validate.rules).message = {required: true}];
}

// list staff for evaluation service result
message ListEvaluationStaffsResult {
  // staff view
  message StaffView {
    // staff id
    int64 id = 1;
    // first name
    string first_name = 2;
    // last name
    string last_name = 3;
    // staff avatar
    string avatar_path = 4;
    // is available
    bool is_available = 5;
  }
  // staff list
  repeated StaffView staffs = 1;
  // pagination
  utils.v2.PaginationResponse pagination = 2;
}

// the service_staff service
service ServiceStaffService {
  // list service_staff
  rpc ListServiceStaffs(ListServiceStaffsParams) returns (ListServiceStaffsResult);
  // list evaluation_staff
  rpc ListEvaluationStaffs(ListEvaluationStaffsParams) returns (ListEvaluationStaffsResult);
}
