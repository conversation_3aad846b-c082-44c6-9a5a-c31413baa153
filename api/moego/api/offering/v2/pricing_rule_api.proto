// @since 2025-02-26 16:46:24
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.offering.v2;

import "moego/models/offering/v2/pricing_rule_defs.proto";
import "moego/models/offering/v2/pricing_rule_enums.proto";
import "moego/models/offering/v2/pricing_rule_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v2;offeringapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.offering.v2";

// upsert pricing rule params
message UpsertPricingRuleParams {
  // pricing_rule def
  moego.models.offering.v2.PricingRuleUpsertDef pricing_rule_def = 1 [(validate.rules).message = {required: true}];
  // apply to upcoming appointments
  bool apply_to_upcoming_appointments = 2;
}

// upsert pricing rule result
message UpsertPricingRuleResult {
  // the created pricing_rule
  moego.models.offering.v2.PricingRule pricing_rule = 1;
}

// get pricing rule params
message GetPricingRuleParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get pricing rule result
message GetPricingRuleResult {
  // the pricing rule
  moego.models.offering.v2.PricingRule pricing_rule = 1;
}

// list pricing rule params
message ListPricingRulesParams {
  // filter
  moego.models.offering.v2.ListPricingRuleFilter filter = 1;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
}

// list pricing rule result
message ListPricingRulesResult {
  // the pricing rule list
  repeated moego.models.offering.v2.PricingRule pricing_rules = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// calculate pricing rule params
message CalculatePricingRuleParams {
  // pet detail list
  repeated moego.models.offering.v2.PetDetailCalculateDef pet_details = 1 [(validate.rules).repeated = {min_items: 1}];
}

// calculate pricing rule result
message CalculatePricingRuleResult {
  // pet detail list
  repeated moego.models.offering.v2.PetDetailCalculateResultDef pet_details = 1;
  // used pricing rule list
  repeated moego.models.offering.v2.PricingRule pricing_rules = 2;
}

// preview pricing rule params
message PreviewPricingRuleParams {
  // pet detail list
  repeated moego.models.offering.v2.PreviewPetDetailCalculateDef pet_details = 1 [(validate.rules).repeated = {min_items: 1}];
  // discount setting
  moego.models.offering.v2.DiscountSettingDef setting = 2;
}

// preview pricing rule result
message PreviewPricingRuleResult {
  // pet detail list
  repeated moego.models.offering.v2.PreviewPetDetailCalculateResultDef pet_details = 1;
  // formula for preview calculation
  string formula = 2;
  // no result
  bool no_result = 3;
}

// delete pricing rule params
message DeletePricingRuleParams {
  // the unique id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // apply to upcoming appointments
  bool apply_to_upcoming_appointments = 2;
}

// delete pricing rule result
message DeletePricingRuleResult {}

// get associated services params
message ListAssociatedServicesParams {
  // rule type
  moego.models.offering.v2.RuleType type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // exclude pricing rule id
  optional int64 exclude_pricing_rule_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get associated services result
message ListAssociatedServicesResult {
  // all boarding associated
  bool all_boarding_associated = 1;
  // associated boarding service ids
  repeated int64 associated_boarding_service_ids = 2;
  // all daycare associated
  bool all_daycare_associated = 3;
  // associated daycare service ids
  repeated int64 associated_daycare_service_ids = 4;
  // all grooming associated
  bool all_grooming_associated = 5;
  // associated grooming service ids
  repeated int64 associated_grooming_service_ids = 6;
  // all addon associated
  bool all_addon_associated = 7;
  // associated addon service ids
  repeated int64 associated_addon_service_ids = 8;
}

// check configuration params
message CheckConfigurationParams {
  // pricing_rule def
  moego.models.offering.v2.PricingRuleUpsertDef pricing_rule_def = 1 [(validate.rules).message = {required: true}];
}

// check configuration result
message CheckConfigurationResult {
  // is valid
  bool is_valid = 1;
  // error message
  optional string error_message = 2;
}

// get discount setting params
message GetDiscountSettingParams {}

// get discount setting result
message GetDiscountSettingResult {
  // discount setting
  moego.models.offering.v2.DiscountSettingDef setting = 1;
}

// update discount setting params
message UpdateDiscountSettingParams {
  // discount setting
  moego.models.offering.v2.DiscountSettingDef setting = 2 [(validate.rules).message = {required: true}];
}

// update discount setting result
message UpdateDiscountSettingResult {
  // discount setting
  moego.models.offering.v2.DiscountSettingDef setting = 1;
}

// get pricing rule overview params
message GetPricingRuleOverviewParams {}

// get pricing rule overview result
message GetPricingRuleOverviewResult {
  // multi pets
  int64 active_multi_pets = 1;
  // multi stays
  int64 active_multi_stays = 2;
  // peak date
  int64 active_peak_date = 3;
  // custom fees
  int64 active_custom_fees = 4;
  // off-hours fee
  int64 active_off_hours_fee = 5;
  // 24 hours charge
  int64 active_24_hours_charge = 6;
  // feeding charge
  int64 active_feeding_charge = 7;
  // medication charge
  int64 active_medication_charge = 8;
  // zone
  int64 active_zone = 9;
}

// pricing rule service
service PricingRuleService {
  // upsert pricing rule
  rpc UpsertPricingRule(UpsertPricingRuleParams) returns (UpsertPricingRuleResult);
  // get pricing rule
  rpc GetPricingRule(GetPricingRuleParams) returns (GetPricingRuleResult);
  // list pricing rule
  rpc ListPricingRules(ListPricingRulesParams) returns (ListPricingRulesResult);
  // calculate pricing rule
  rpc CalculatePricingRule(CalculatePricingRuleParams) returns (CalculatePricingRuleResult);
  // preview pricing rule
  rpc PreviewPricingRule(PreviewPricingRuleParams) returns (PreviewPricingRuleResult);
  // delete pricing rule
  rpc DeletePricingRule(DeletePricingRuleParams) returns (DeletePricingRuleResult);

  // list associated services, used for pricing rule selecting services
  rpc ListAssociatedServices(ListAssociatedServicesParams) returns (ListAssociatedServicesResult);

  // check configuration
  rpc CheckConfiguration(CheckConfigurationParams) returns (CheckConfigurationResult);

  // get discount setting
  rpc GetDiscountSetting(GetDiscountSettingParams) returns (GetDiscountSettingResult);
  // update discount setting
  rpc UpdateDiscountSetting(UpdateDiscountSettingParams) returns (UpdateDiscountSettingResult);

  // get pricing rule overview
  rpc GetPricingRuleOverview(GetPricingRuleOverviewParams) returns (GetPricingRuleOverviewResult);
}
