// @since 2025-03-31 15:46:22
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.fulfillment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1;fulfillmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.fulfillment.v1";

// Fulfillment status
enum Status {
  // unspecified
  STATUS_UNSPECIFIED = 0;
  // unconfirmed
  UNCONFIRMED = 1;
  // confirmed
  CONFIRMED = 2;
  // finished
  FINISHED = 3;
  // canceled
  CANCELED = 4;
  // ready to check in
  READY = 5;
  // checked in
  CHECK_IN = 6;
  // Pending payment
  PENDING_PAYMENT = 10;
  // Completed
  COMPLETED = 11;
}

// Fulfillment source
enum Source {
  // Unspecified
  SOURCE_UNSPECIFIED = 0;
  // web
  WEB = 22018;
  // online booking
  OB = 22168;
  // android
  ANDROID = 17216;
  // ios
  IOS = 17802;
  // auto dm
  AUTO_DM = 23426;
  // google calendar
  GOOGLE_CALENDAR = 19826;
  // open api
  OPEN_API = 23333;
}

// Fulfillment service date type
enum DateType {
  // unspecified
  DATE_TYPE_UNSPECIFIED = 0;
  // Everyday during whole stay period, except for checkout day
  // associated_service_id exist and start_date is empty
  EVERYDAY = 1;
  // Specific dates
  // associated_service_id exist and specific_dates is not empty
  SPECIFIC_DATES = 2;
  // Date point, start date
  // associated_service_id not exist
  DATE_POINT = 3;
  // Everyday during whole stay period, include checkout day
  // associated_service_id exist and start_date is empty
  EVERYDAY_INCLUDE_CHECKOUT_DAY = 4;
}

// Action type
enum ActionType {
  // unspecified
  ACTION_TYPE_UNSPECIFIED = 0;
  // Status change
  STATUS_CHANGE = 1;
  // Time change
  TIME_CHANGE = 2;
  // Color code change
  COLOR_CODE_CHANGE = 3;
  // Edit pet and services
  EDIT_PET_AND_SERVICES = 4;
  // Delete pet
  DELETE_PET = 5;
}
