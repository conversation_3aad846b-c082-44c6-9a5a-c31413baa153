// @since 2025-03-31 15:46:22
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/fulfillment/v1/fulfillment_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1;fulfillmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.fulfillment.v1";

// The Fulfillment model
message FulfillmentModel {
  // The ID of fulfillment
  int64 id = 1;

  // The ID of company
  int64 company_id = 2;

  // The ID of business
  int64 business_id = 3;

  // The ID of customer
  int64 customer_id = 4;

  // The ID of booking request
  int64 booking_request_id = 5;

  // The ID of order
  int64 order_id = 12;

  // The start datetime of fulfillment
  google.protobuf.Timestamp start_date_time = 6;

  // The end datetime of fulfillment
  google.protobuf.Timestamp end_date_time = 7;

  // The end datetime of fulfillment
  Status status = 8;

  // color code
  string color_code = 9;

  // The bitmap value of fulfillment service item type
  int32 service_type_include = 10;

  // source
  Source source = 11;

  // the create time
  google.protobuf.Timestamp created_at = 20;

  // the update time
  google.protobuf.Timestamp updated_at = 21;

  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 22;
}
