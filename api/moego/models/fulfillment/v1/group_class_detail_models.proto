// @since 2025-04-07 15:32:56
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.fulfillment.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1;fulfillmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.fulfillment.v1";

// The GroupClassDetail model
message GroupClassDetailModel {
  // The unique identifier of the model
  int64 id = 1;

  // The fulfillment id
  int64 fulfillment_id = 2;

  // The pet id
  int64 pet_id = 3;

  // The group class id, service id
  int64 group_class_id = 4;

  // The group class instance id
  int64 group_class_instance_id = 5;

  // The group class status
  Status status = 7;

  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  google.protobuf.Timestamp updated_at = 14;
  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 15;

  // The group class status
  enum Status {
    // Unspecified
    STATUS_UNSPECIFIED = 0;
    // Not started
    NOT_STARTED = 1;
    // In progress
    IN_PROGRESS = 2;
    // Completed
    COMPLETED = 3;
  }
}
