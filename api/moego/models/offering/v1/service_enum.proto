syntax = "proto3";

package moego.models.offering.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// The care type enum
enum ServiceItemType {
  // Unspecified care type
  SERVICE_ITEM_TYPE_UNSPECIFIED = 0;
  // Grooming care type
  GROOMING = 1;
  // Boarding care type
  BOARDING = 2;
  // Daycare care type
  DAYCARE = 3;
  // Evaluation care type
  EVALUATION = 4;
  // Dog walking care type
  DOG_WALKING = 5;
  // Training group class
  GROUP_CLASS = 6;
}

// service price unit
enum ServicePriceUnit {
  // Unspecified service price unit
  SERVICE_PRICE_UNIT_UNSPECIFIED = 0;
  // Per session service price unit
  PER_SESSION = 1;
  // Per night service price unit
  PER_NIGHT = 2;
  // Per hour service price unit
  PER_HOUR = 3;
  // Per day service price unit
  PER_DAY = 4;
}

// service type
enum ServiceType {
  // Unspecified service type
  SERVICE_TYPE_UNSPECIFIED = 0;
  // normal service
  SERVICE = 1;
  // service add on
  ADDON = 2;
}

// Service effective range type
// Customize the modification range used by price and duration.
enum ServiceScopeType {
  // unspecified
  SERVICE_SCOPE_TYPE_UNSPECIFIED = 0;
  // only for this appointment
  ONLY_THIS = 1;
  // do not save
  DO_NOT_SAVE = 2;
  // for this and following appointments
  THIS_AND_FUTURE = 3;
  // for all upcoming appointments
  ALL_UPCOMING = 4;
}

// Service override type
enum ServiceOverrideType {
  // unspecified
  SERVICE_OVERRIDE_TYPE_UNSPECIFIED = 0;
  // override by location
  LOCATION = 1;
  // override by client
  CLIENT = 2;
  // override by staff
  STAFF = 3;
  // override by zone
  ZONE = 4;
}

// Service OrderBy type
enum ServiceOrderByType {
  // unspecified
  SERVICE_ORDER_BY_TYPE_UNSPECIFIED = 0;
  // default, order by category sort and service sort asc
  DEFAULT = 1;
  // order by max duration asc
  MAX_DURATION_ASC = 2;
}

// date type
enum DateType {
  // unspecified
  DATE_TYPE_UNSPECIFIED = 0;
  // every day, except checkout day(the old enum name in pet detail is called every day, will cause confusion)
  EVERY_DAY_EXCEPT_CHECKOUT_DAY = 1;
  // specific date
  SPECIFIC_DATE = 2;
  // date point
  DATE_POINT = 3;
  // every day include checkout day
  EVERY_DAY_INCLUDE_CHECKOUT_DAY = 4;
  // every day except check-in day
  EVERY_DAY_EXCEPT_CHECKIN_DAY = 5;
  // last day
  LAST_DAY = 6;
  // first day
  FIRST_DAY = 7;
}
