// @since 2025-02-26 16:46:24
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.offering.v2;

import "google/protobuf/timestamp.proto";
import "google/type/dayofweek.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/offering/v2/pricing_rule_enums.proto";
import "moego/utils/v1/struct.proto";
import "moego/utils/v2/condition_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v2";

// pricing rule upsert definition
message PricingRuleUpsertDef {
  // the unique id
  optional int64 id = 1;

  // rule type
  RuleType type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // rule name
  string rule_name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // active, true means active, false means inactive
  bool is_active = 5;

  // all boarding applicable
  bool all_boarding_applicable = 6;
  // selected service ids, only effective when all_service is false
  repeated int64 selected_boarding_services = 7 [(validate.rules).repeated = {
    ignore_empty: true
    unique: true
    items: {
      int64: {gt: 0}
    }
    max_items: 500
  }];
  // all daycare applicable
  bool all_daycare_applicable = 8;
  // selected service ids, only effective when all_service is false
  repeated int64 selected_daycare_services = 9 [(validate.rules).repeated = {
    ignore_empty: true
    unique: true
    items: {
      int64: {gt: 0}
    }
    max_items: 500
  }];

  // rule configuration
  moego.models.offering.v2.PricingRuleConfiguration rule_configuration = 10 [(validate.rules).message = {required: true}];

  // rule apply type, apply to each one/apply to additional
  moego.models.offering.v2.RuleApplyType rule_apply_type = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // same lodging unit, only effective when rule_item_type is multiple pet
  bool need_in_same_lodging = 12;

  // updated staff id
  optional int64 updated_by = 13;
  // the create time
  optional google.protobuf.Timestamp created_at = 14;
  // the update time
  optional google.protobuf.Timestamp updated_at = 15;
  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 16;
  // is charge per lodging, only effective when rule_item_type is peak date
  optional bool is_charge_per_lodging = 17;
  // source
  optional moego.models.offering.v2.Source source = 18 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // all grooming applicable
  bool all_grooming_applicable = 19;
  // selected service ids, only effective when all_service is false
  repeated int64 selected_grooming_services = 20 [(validate.rules).repeated = {
    ignore_empty: true
    unique: true
    items: {
      int64: {gt: 0}
    }
    max_items: 500
  }];
  // all addon applicable
  bool all_addon_applicable = 21;
  // selected service ids, only effective when all_service is false
  repeated int64 selected_addon_services = 22 [(validate.rules).repeated = {
    ignore_empty: true
    unique: true
    items: {
      int64: {gt: 0}
    }
    max_items: 500
  }];
}

// GenericValue represents a polymorphic value for condition evaluation
message GenericValue {
  // value type
  oneof value {
    // number value
    double number_value = 1 [(validate.rules).double = {gt: 0}];
    // number values
    utils.v2.NumberWrapper number_values = 2;
    // string values
    utils.v2.StringWrapper string_values = 3;
    // date range
    utils.v2.StringDateRange date_range = 4;
    // repeat dates
    RepeatDates repeat_dates = 5;
  }
}

// Repeat dates represents a repeated date range
message RepeatDates {
  // date range
  utils.v2.StringDateRange date_range = 1;
  // the interval
  oneof interval {
    // week
    Week week = 2;
  }

  // week interval
  message Week {
    // interval num
    int32 interval_num = 3;

    // repeat on weekdays
    repeated google.type.DayOfWeek day_of_weeks = 1;
  }
}

// Condition represents a single condition to be evaluated
message Condition {
  // condition type
  ConditionType type = 1;
  // operator
  utils.v2.Operator operator = 2;
  // value
  GenericValue value = 3;
}

// Effect represents how a price should be modified
message Effect {
  // effect type
  EffectType type = 1;
  // value
  double value = 2 [(validate.rules).double = {gte: 0}];
  // rounding position
  // 正数 n: 保留 n 位小数 (例如 1 -> 0.1, 2 -> 0.01)
  // 0: 四舍五入到个位
  // 负数 -n: 四舍五入到 10^n 的位置 (例如 -1 -> 十位, -2 -> 百位)
  optional int32 rounding_position = 3;
}

// ConditionGroup represents a group of conditions with a specific effect
message ConditionGroup {
  // conditions
  repeated Condition conditions = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
  }];
  // effect
  Effect effect = 2;
}

// pricing rule configuration
message PricingRuleConfiguration {
  // condition groups
  repeated ConditionGroup condition_groups = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
  }];
}

// list pricing rule filter
message ListPricingRuleFilter {
  // rule type
  repeated moego.models.offering.v2.RuleType rule_types = 1 [(validate.rules).repeated = {
    max_items: 10
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // care type: grooming/boarding/daycare
  repeated moego.models.offering.v1.ServiceItemType care_types = 2 [(validate.rules).repeated = {
    max_items: 10
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // active
  optional bool is_active = 3;
  // pricing rule ids
  repeated int64 ids = 5 [(validate.rules).repeated = {
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
  // exclude pricing rule ids
  repeated int64 exclude_ids = 6 [(validate.rules).repeated = {
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
}

// pet detail calculate definition, used for pricing rule calculation
message PreviewPetDetailCalculateDef {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service id, not evaluation id
  int64 service_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service price
  double service_price = 3 [(validate.rules).double = {gte: 0}];
  // stay length
  int32 stay_length = 5 [(validate.rules).int32 = {gt: 0}];
}

// pet detail calculate definition, used for pricing rule calculation
message PreviewPetDetailCalculateResultDef {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service id, not evaluation id
  int64 service_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service price
  double adjusted_price = 3 [(validate.rules).double = {gte: 0}];
}

// pet detail calculate definition, used for pricing rule calculation
message PetDetailCalculateDef {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service id, not evaluation id
  int64 service_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service price
  double service_price = 3 [(validate.rules).double = {gte: 0}];
  // lodging unit id
  optional int64 lodging_unit_id = 4 [(validate.rules).int64 = {gte: 0}];
  // service date
  optional string service_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // scope type price, apply to other services
  optional models.offering.v1.ServiceScopeType scope_type_price = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // is split lodging
  bool is_split_lodging = 12;
}

// pet detail calculate result definition, used for pricing rule calculation
message PetDetailCalculateResultDef {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service id
  int64 service_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service price after calculation
  double adjusted_price = 4 [(validate.rules).double = {gte: 0}];
  // service date
  optional string service_date = 6 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // applied rule ids, empty means for preview
  repeated int64 applied_rule_ids = 7 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
}

// discount setting definition
message DiscountSettingDef {
  // the unique id
  optional int64 id = 1;
  // apply best only
  bool apply_best_only = 2;
  // apply sequence
  repeated RuleType apply_sequence = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}
