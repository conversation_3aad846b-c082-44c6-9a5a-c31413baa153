// @since 2024-03-21 14:16:42
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/online_booking/v1/boarding_add_on_detail_models.proto";
import "moego/models/online_booking/v1/boarding_auto_assign_models.proto";
import "moego/models/online_booking/v1/boarding_service_detail_models.proto";
import "moego/models/online_booking/v1/boarding_service_waitlist_model.proto";
import "moego/models/online_booking/v1/booking_request_enums.proto";
import "moego/models/online_booking/v1/booking_request_note.proto";
import "moego/models/online_booking/v1/daycare_add_on_detail_models.proto";
import "moego/models/online_booking/v1/daycare_service_detail_models.proto";
import "moego/models/online_booking/v1/daycare_service_waitlist_model.proto";
import "moego/models/online_booking/v1/dog_walking_service_detail_models.proto";
import "moego/models/online_booking/v1/evaluation_test_detail_models.proto";
import "moego/models/online_booking/v1/feeding_models.proto";
import "moego/models/online_booking/v1/grooming_add_on_detail_models.proto";
import "moego/models/online_booking/v1/grooming_auto_assign_models.proto";
import "moego/models/online_booking/v1/grooming_service_detail_models.proto";
import "moego/models/online_booking/v1/group_class_service_detail_models.proto";
import "moego/models/online_booking/v1/medication_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// The booking request model,
message BookingRequestModel {
  // unique id
  int64 id = 1;

  // business id
  int64 business_id = 2;

  // customer id
  int64 customer_id = 3;

  // appointment id, generated after the booking request is scheduled
  int64 appointment_id = 4;

  // start date, format: yyyy-mm-dd
  optional string start_date = 5;

  // start time, the minutes from 00:00
  optional int32 start_time = 6;

  // end date, format: yyyy-mm-dd
  optional string end_date = 7;

  // end time, the minutes from 00:00
  optional int32 end_time = 8;

  // status, 0: pending, 1: confirmed, 2: canceled
  BookingRequestStatus status = 9;

  // is prepaid
  bool is_prepaid = 10;

  // additional note
  string additional_note = 11;

  // source platform
  BookingRequestSourcePlatform source_platform = 12;

  // service item type include, bitmap value
  int32 service_type_include = 13;

  // the services
  repeated Service services = 14;

  // the company id
  int64 company_id = 15;

  // the create time
  google.protobuf.Timestamp created_at = 20;

  // the update time
  google.protobuf.Timestamp updated_at = 21;

  // booking request attributes, store some additional information
  Attr attr = 22;

  // payment status
  PaymentStatus payment_status = 23;

  // Booking request source
  Source source = 24;

  // comment
  BookingRequestNoteModel comment = 25;

  // source id, see BookingRequestModel.Source
  int64 source_id = 26;

  // payment status definition
  //
  // payment_status 状态流转图：
  //
  //                          +---------------------+
  //                          |  Booking Request    |
  //                          +---------------------+
  //                                    │
  //                                    ▼
  //                          .---------------------.
  //                          | Prepay Enabled?     |
  //                          '---------------------'
  //                              /           \
  //                             /             \
  //                         No /               \ Yes
  //                           /                 \
  //                          ▼                   ▼
  //                  +----------------+    +----------------+
  //                  |  NO_PAYMENT    |    |    WAITING     |
  //                  +----------------+    +----------------+
  //                                              │
  //                                              ▼
  //                                  .--------------------------.
  //                                  | Confirm PaymentIntent    |
  //                                  '--------------------------'
  //                                       /              \
  //                                  Fail/                \ Success
  //                                     /                  \
  //                                    ▼                    ▼
  //                           +----------------+     +----------------+
  //                           |    FAILED      |     |  PROCESSING    |
  //                           +----------------+     +----------------+
  //                                                       │
  //                                                       ▼
  //                                         .-----------------------.
  //                                         | Capture PaymentIntent |
  //                                         '-----------------------'
  //                                              /             \
  //                                        Fail /               \ Success
  //                                            /                 \
  //                                           ▼                   ▼
  //                                    +----------------+    +----------------+
  //                                    |    FAILED      |    |    SUCCESS     |
  //                                    +----------------+    +----------------+
  enum PaymentStatus {
    // unspecified
    PAYMENT_STATUS_UNSPECIFIED = 0;
    // no payment
    // B 端没有开启 prepay
    NO_PAYMENT = 1;
    // waiting: 成功 submit，但是还没触发支付的状态（等待支付）
    // 在 submit 之后，调用 confirm PaymentIntent 之前，payment_status 为 WAITING。
    WAITING = 5;
    // processing
    // 当 C 端 confirm payment 之后，会把 BookingRequest 的 payment_status 设置为 PROCESSING
    PROCESSING = 2;
    // success
    // 当 B 端 accept 或者 auto accept 之后，会去 capture payment，
    // 如果 capture 成功，会把 BookingRequest 的 payment_status 设置为 SUCCESS
    SUCCESS = 3;
    // failed
    // C 端 confirm 失败或者 capture payment 失败，会把 BookingRequest 的 payment_status 设置为 FAILED
    FAILED = 4;
  }

  // Booking request attributes
  message Attr {
    // whether the customer is a new visitor
    bool is_new_visitor = 1;

    // 当 source 为 B 端创建时，记录创建 BookingRequest 的 Staff ID
    optional int64 created_by_staff_id = 2;
  }

  // The booking request service
  message Service {
    // the service type
    oneof service {
      // grooming service
      GroomingService grooming = 1;
      // boarding service
      BoardingService boarding = 2;
      // daycare service
      DaycareService daycare = 3;
      // evaluation service
      EvaluationService evaluation = 4;
      // dog walking service
      DogWalkingService dog_walking = 5;
      // group class service
      GroupClassService group_class = 6;
    }
  }

  // The grooming service
  message GroomingService {
    // the grooming service detail
    GroomingServiceDetailModel service = 1;
    // the addons
    repeated GroomingAddOnDetailModel addons = 2;
    // auto assign
    optional GroomingAutoAssignModel auto_assign = 3;
  }

  // The boarding service
  message BoardingService {
    // the boarding service detail
    BoardingServiceDetailModel service = 1;
    // the addons
    repeated BoardingAddOnDetailModel addons = 2;
    // the feeding. Deprecated, use feedings instead
    FeedingModel feeding = 3 [deprecated = true];
    // the medication. Deprecated, use medications instead
    MedicationModel medication = 4 [deprecated = true];
    // auto assign
    optional BoardingAutoAssignModel auto_assign = 5;
    // the feedings
    repeated FeedingModel feedings = 6;
    // the medications
    repeated MedicationModel medications = 7;
    // Boarding waitlist
    optional moego.models.online_booking.v1.BoardingServiceWaitlistModel waitlist = 8;
  }

  // The daycare service
  message DaycareService {
    // the daycare service detail
    DaycareServiceDetailModel service = 1;
    // the addons
    repeated DaycareAddOnDetailModel addons = 2;
    // the feeding. Deprecated, use feedings instead
    FeedingModel feeding = 3 [deprecated = true];
    // the medication. Deprecated, use medications instead
    MedicationModel medication = 4 [deprecated = true];
    // the feedings
    repeated FeedingModel feedings = 5;
    // the medications
    repeated MedicationModel medications = 6;
    // Boarding waitlist
    optional moego.models.online_booking.v1.DaycareServiceWaitlistModel waitlist = 8;
  }

  // The evaluation service
  message EvaluationService {
    // the evaluation service detail
    EvaluationTestDetailModel service = 1;
  }

  // The dog walking service
  message DogWalkingService {
    // the dog walking service detail
    DogWalkingServiceDetailModel service = 1;
  }

  // The group class service
  message GroupClassService {
    // the group class service detail
    GroupClassServiceDetailModel service = 1;
  }

  // Booking request source definition
  enum Source {
    // unspecified
    SOURCE_UNSPECIFIED = 0;
    // Created by business
    BUSINESS = 1;
    // Created by online booking
    OB = 2;
    // Created by membership, source id is membership id
    MEMBERSHIP = 3;
  }
}
