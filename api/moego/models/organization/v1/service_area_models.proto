// @since 2025-08-25 18:54:26
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.organization.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// The ServiceArea search view
message ServiceAreaSearchView {
  // the unique id
  int64 id = 1;

  // area name
  string name = 2;
}
