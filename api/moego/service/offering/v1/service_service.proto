syntax = "proto3";

package moego.service.offering.v1;

import "google/type/latlng.proto";
import "google/type/money.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/offering/v1/category_models.proto";
import "moego/models/offering/v1/service_bundle_sale_mapping_models.proto";
import "moego/models/offering/v1/service_defs.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/offering/v1/service_models.proto";
import "moego/utils/v2/list.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.offering.v1";

// create service request
message CreateServiceRequest {
  // service to create
  moego.models.offering.v1.CreateServiceDef create_service_def = 1 [(validate.rules).message.required = true];
  // company id
  int64 token_company_id = 2 [(validate.rules).int64.gt = 0];
  // operator id, usually the staff id
  oneof operator_id {
    option (validate.required) = true;
    // staff id
    int64 token_staff_id = 3 [(validate.rules).int64.gt = 0];
    // internal operator
    string internal_operator = 4 [
      (validate.rules).string.min_len = 1,
      (validate.rules).string.max_len = 255
    ];
  }
}

// create service response
message CreateServiceResponse {
  // service created
  moego.models.offering.v1.ServiceModel service = 1 [(validate.rules).message.required = true];
}

// update service request
message UpdateServiceRequest {
  // service to update
  moego.models.offering.v1.UpdateServiceDef update_service_def = 1 [(validate.rules).message.required = true];
  // company id
  int64 token_company_id = 2 [(validate.rules).int64.gt = 0];
  // operator id, usually the staff id
  oneof operator_id {
    option (validate.required) = true;
    // staff id
    int64 token_staff_id = 3 [(validate.rules).int64.gt = 0];
    // internal operator
    string internal_operator = 4 [
      (validate.rules).string.min_len = 1,
      (validate.rules).string.max_len = 255
    ];
  }
}

// update service response
message UpdateServiceResponse {
  // service updated
  moego.models.offering.v1.ServiceModel service = 1 [(validate.rules).message.required = true];
}

// get service list request
message GetServiceListRequest {
  // service type
  optional models.offering.v1.ServiceItemType service_item_type = 1;
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 2;
  // business id list, empty for all
  repeated int64 business_ids = 3;
  // inactive
  optional bool inactive = 4;
  // service type
  optional models.offering.v1.ServiceType service_type = 5;
  // company id
  int64 token_company_id = 6 [(validate.rules).int64.gt = 0];
  // keyword, search by name
  optional string keyword = 7 [(validate.rules).string.max_len = 255];
  // service id list
  repeated int64 service_ids = 8;
}

// get service list response
message GetServiceListResponse {
  // service list
  repeated moego.models.offering.v1.ServiceCategoryModel category_list = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// get service list by ids request
message GetServiceListByIdsRequest {
  // company id
  optional int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // service id list
  repeated int64 service_ids = 2;
}

// get service list by ids response
message GetServiceListByIdsResponse {
  // service list
  repeated moego.models.offering.v1.ServiceBriefView services = 1;
}

// get service by pet and service id request
message GetServiceByPetAndServiceIdRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id, optional, if not set, will return the company base setting
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // pet id with service id list
  map<int64, utils.v2.Int64List> pet_id_with_service_id_list = 3;
}

// get service by pet and service id response
message GetServiceByPetAndServiceIdResponse {
  // pet id with available service list
  map<int64, models.offering.v1.CustomizedServiceViewList> pet_id_with_available_service_list = 1;
}

// customized service query condition
message CustomizedServiceQueryCondition {
  // service id
  int64 service_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // pet id
  optional int64 pet_id = 3 [(validate.rules).int64.gt = 0];
  // staff id
  optional int64 staff_id = 4 [(validate.rules).int64.gt = 0];
  // customer address zipcode
  optional string zipcode = 5 [(validate.rules).string.max_len = 50];
  // customer address coordinate
  optional google.type.LatLng coordinate = 6;
}

// batch get customized service request
message BatchGetCustomizedServiceRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // query condition list
  repeated CustomizedServiceQueryCondition query_condition_list = 2;
}

// batch get customized service response
message BatchGetCustomizedServiceResponse {
  // customized service
  message ServiceWithCustomizedInfo {
    // query condition
    CustomizedServiceQueryCondition query_condition = 1;
    // customized service
    moego.models.offering.v1.CustomizedServiceView customized_service = 2;
  }
  // customized service list
  repeated ServiceWithCustomizedInfo customized_service_list = 1;
}

// override service request
message OverrideServiceRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // override rules by service id
  map<int64, moego.models.offering.v1.ServiceOverrideRule> override_rules_by_service_id = 2;
}

// override service response
message OverrideServiceResponse {}

// get applicable service list request
message GetApplicableServiceListRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // service type
  optional models.offering.v1.ServiceItemType service_item_type = 2;
  // business id, empty for all business in company
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
  // only return available services
  bool only_available = 4;
  // pet id
  optional int64 pet_id = 5;
  // service type
  models.offering.v1.ServiceType service_type = 6;
  // pet info, for service filter
  optional moego.models.offering.v1.ServiceApplicableFilter filter = 7;
  // keyword, search by name
  optional string keyword = 8 [(validate.rules).string.max_len = 255];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 9;
  // inactive
  optional bool inactive = 10;
  // customer address zipcode
  optional string zipcode = 11 [(validate.rules).string.max_len = 50];
  // customer address coordinate
  optional google.type.LatLng coordinate = 12;
}

// get applicable service list response, grouped by category
message GetApplicableServiceListResponse {
  // service list
  repeated moego.models.offering.v1.CustomizedServiceCategoryView category_list = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// request for customized service by pet
message CustomizedServiceByPetRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];
}

// response for customized service by pet
message CustomizedServiceByPetResponse {
  // customized service list
  repeated moego.models.offering.v1.ServiceWithPetCustomizedInfo service_list = 1;
}

// get service detail request
message GetServiceDetailRequest {
  // company id
  optional int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // service id
  int64 service_id = 2 [(validate.rules).int64.gt = 0];
}

// get service detail response
message GetServiceDetailResponse {
  // service detail
  moego.models.offering.v1.ServiceModel service = 1 [(validate.rules).message.required = true];
}

// todo: use mq instead
// remove service filter request
message RemoveServiceFilterRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // filter id
  oneof filter {
    option (validate.required) = true;
    // pet type
    models.customer.v1.PetType pet_type = 2;
    // pet breed
    string pet_breed = 3;
    // pet size
    int64 pet_size_id = 4;
    // pet coat type id
    int64 pet_coat_type_id = 5;
    // service id
    int64 service_id = 6;
    // lodging type id
    int64 lodging_type_id = 7;
    // business id
    int64 business_id = 8;
    // staff id
    int64 staff_id = 9;
  }
}

// remove service filter response
message RemoveServiceFilterResponse {}

// get all service item types request
message GetServiceItemTypesRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
}

// get all service item types response
message GetServiceItemTypesResponse {
  // service item types
  repeated models.offering.v1.ServiceItemType service_item_types = 1;
}

/*
   new apis after api specification
*/

// list service request
message ListServiceRequest {
  // service type deprecated: use service_item_types
  optional models.offering.v1.ServiceItemType service_item_type = 1 [
    (validate.rules).enum = {
      defined_only: true
      not_in: 0
    },
    deprecated = true
  ];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2 [(validate.rules).message.required = true];
  // business id list, empty for all
  repeated int64 business_ids = 3 [(validate.rules).repeated = {
    max_items: 100
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
  // service type
  optional models.offering.v1.ServiceType service_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];
  // company id
  int64 token_company_id = 5 [(validate.rules).int64.gt = 0];
  // order by
  optional models.offering.v1.ServiceOrderByType order_by = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];
  // inactive
  optional bool inactive = 7;
  // service types
  repeated models.offering.v1.ServiceItemType service_item_types = 8 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: 0
      }
    }
  }];
  // prerequisite classes ids
  repeated int64 prerequisite_class_ids = 9 [(validate.rules).repeated = {
    max_items: 100
    items: {
      int64: {gt: 0}
    }
  }];
  // filter prerequisite classes
  optional bool filter_prerequisite_classes = 10;
}

// list service response
message ListServiceResponse {
  // service list
  repeated moego.models.offering.v1.ServiceModel services = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// ListAvailableStaff request
message ListAvailableStaffIdRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // service ids
  repeated int64 service_ids = 3 [(validate.rules).repeated = {
    max_items: 2000
    items: {
      int64: {gt: 0}
    }
  }];
}

// ListAvailableStaff response
message ListAvailableStaffIdResponse {
  // staff id list
  map<int64, StaffIds> service_id_to_staff_ids = 1;

  // staff ids
  message StaffIds {
    // is available for all staffs
    bool is_all_staff = 1;
    // staff ids
    repeated int64 staff_ids = 2;
  }
}

// list bundle services request
message ListBundleServicesRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // service ids
  repeated int64 service_ids = 2 [(validate.rules).repeated = {
    unique: true
    max_items: 2000
    items: {
      int64: {gt: 0}
    }
  }];
}

// list bundle services response
message ListBundleServicesResponse {
  // bundle services
  repeated moego.models.offering.v1.ServiceBundleSaleMappingModel bundle_services = 1;
}

// list categories request
message ListCategoriesRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // filter
  Filter filter = 2;
  // filter
  message Filter {
    // service type
    repeated models.offering.v1.ServiceType service_types = 1 [(validate.rules).repeated = {
      items: {
        enum: {
          defined_only: true
          not_in: 0
        }
      }
    }];
    // service item type
    repeated models.offering.v1.ServiceItemType service_item_types = 2 [(validate.rules).repeated = {
      items: {
        enum: {
          defined_only: true
          not_in: 0
        }
      }
    }];
    // ids
    repeated int64 ids = 3 [(validate.rules).repeated = {
      items: {
        int64: {gt: 0}
      }
    }];
  }
}

// list categories response
message ListCategoriesResponse {
  // categories
  repeated moego.models.offering.v1.CategoryModel categories = 1;
}

// create categories request
// group by service type and service item type then check name duplication
// ignore duplicated categories
message CreateCategoriesRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // categories, ignore id
  repeated moego.models.offering.v1.CategoryModel categories = 2;
}

// create categories response
message CreateCategoriesResponse {
  // categories
  repeated moego.models.offering.v1.CategoryModel categories = 1;
}

// request for GetMaxServicePriceByLodgingType
message GetMaxServicePriceByLodgingTypeRequest {
  // lodging type id
  int64 lodging_type_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 3 [(validate.rules).int64.gt = 0];
}

// response for GetMaxServicePriceByLodgingType
message GetMaxServicePriceByLodgingTypeResponse {
  // max price
  google.type.Money max_price = 1;
}

// service define
service ServiceManagementService {
  // create service, only for b-setting
  rpc CreateService(CreateServiceRequest) returns (CreateServiceResponse) {}
  // update service, only for b-setting
  rpc UpdateService(UpdateServiceRequest) returns (UpdateServiceResponse) {}
  // get service list, only for b-setting
  rpc GetServiceList(GetServiceListRequest) returns (GetServiceListResponse) {}
  // get service by pet and service id
  rpc GetServiceByPetAndServiceId(GetServiceByPetAndServiceIdRequest) returns (GetServiceByPetAndServiceIdResponse) {}
  // override service
  rpc OverrideService(OverrideServiceRequest) returns (OverrideServiceResponse) {}
  // get applicable service list
  rpc GetApplicableServiceList(GetApplicableServiceListRequest) returns (GetApplicableServiceListResponse) {}
  // get customized service by pet
  rpc CustomizedServiceByPet(CustomizedServiceByPetRequest) returns (CustomizedServiceByPetResponse) {}
  // get service detail, only for b-setting
  rpc GetServiceDetail(GetServiceDetailRequest) returns (GetServiceDetailResponse) {}
  // get service list by ids
  rpc GetServiceListByIds(GetServiceListByIdsRequest) returns (GetServiceListByIdsResponse) {}
  // remove service filter
  rpc RemoveServiceFilter(RemoveServiceFilterRequest) returns (RemoveServiceFilterResponse) {}
  // get all service item types by services
  rpc GetServiceItemTypes(GetServiceItemTypesRequest) returns (GetServiceItemTypesResponse) {}

  /**
     new apis after api specification
  */
  // list service
  rpc ListService(ListServiceRequest) returns (ListServiceResponse) {}
  // batch get customized service
  rpc BatchGetCustomizedService(BatchGetCustomizedServiceRequest) returns (BatchGetCustomizedServiceResponse) {}
  // List available staff ids for service
  rpc ListAvailableStaffId(ListAvailableStaffIdRequest) returns (ListAvailableStaffIdResponse) {}
  // list bundle services
  rpc ListBundleServices(ListBundleServicesRequest) returns (ListBundleServicesResponse) {}
  // list categories
  rpc ListCategories(ListCategoriesRequest) returns (ListCategoriesResponse) {}
  // create categories
  rpc CreateCategories(CreateCategoriesRequest) returns (CreateCategoriesResponse) {}

  // Get max service price by lodging type
  rpc GetMaxServicePriceByLodgingType(GetMaxServicePriceByLodgingTypeRequest) returns (GetMaxServicePriceByLodgingTypeResponse) {}
}
