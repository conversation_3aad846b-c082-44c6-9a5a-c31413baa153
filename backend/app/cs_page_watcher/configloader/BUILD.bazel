load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "configloader",
    srcs = [
        "configloader.go",
        "nacos.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/config",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/env",
        "@in_gopkg_yaml_v3//:yaml_v3",
    ],
)

go_test(
    name = "configloader_test",
    srcs = ["nacos_test.go"],
    embed = [":configloader"],
    deps = ["@com_github_stretchr_testify//assert"],
)
