package configloader

import (
	"sync/atomic"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTeamSchedulesDependencyInjection(t *testing.T) {
	// 创建测试配置
	testConfig := &CSPageConfig{
		TeamSchedules: map[string]Schedule{
			"TestTeam": {
				Name:                "Test Team",
				ScheduleID:          "test-schedule-id",
				AdminTaskScheduleID: "test-admin-schedule-id",
				TeamHandle:          "test-team",
			},
		},
	}

	// 创建 atomic.Pointer 并设置配置
	configPtr := &atomic.Pointer[CSPageConfig]{}
	configPtr.Store(testConfig)

	// 验证可以正确获取配置
	loadedConfig := configPtr.Load()
	assert.NotNil(t, loadedConfig)
	assert.Equal(t, 1, len(loadedConfig.TeamSchedules))

	schedule, exists := loadedConfig.TeamSchedules["TestTeam"]
	assert.True(t, exists)
	assert.Equal(t, "Test Team", schedule.Name)
	assert.Equal(t, "test-schedule-id", schedule.ScheduleID)
	assert.Equal(t, "test-admin-schedule-id", schedule.AdminTaskScheduleID)
	assert.Equal(t, "test-team", schedule.TeamHandle)
}
