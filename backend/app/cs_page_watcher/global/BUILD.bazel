load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "global",
    srcs = ["global.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global",
    visibility = ["//visibility:public"],
    deps = ["//backend/common/rpc/framework/log"],
)

go_test(
    name = "global_test",
    srcs = ["global_test.go"],
    embed = [":global"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/config/nacos",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
    ],
)
