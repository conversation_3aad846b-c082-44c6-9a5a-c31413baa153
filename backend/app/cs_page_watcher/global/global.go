package global

import (
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// Jira 自定义字段名称常量
const (
	T1OrGeneral      = "T1 or General"
	IssuePriority    = "Issue Priority"
	CustomerStage    = "Customer Stage"
	Components       = "Components"
	SLABreach        = "SLA Breach"
	LogoName         = "[T1] Logo Name"
	LocationName     = "[T1] Location Name"
	DevEngineer      = "Dev Engineer"
	IssueDescription = "Issue Description"
	ResolutionTime   = "ResolutionTime"
	CreatedBy        = "Created By"
	FeatureDomains   = "Feature Domains"
	CauseAndSolution = "Cause and solution (Note)"
)

// Jira 优先级常量
const (
	PriorityP0 = "P0-Block"
	PriorityP1 = "P1-Critical"
	PriorityP2 = "P2-High"
	PriorityP3 = "P3-Moderate"
	PriorityP4 = "P4-Minor"
	PriorityP5 = "P5-Low"
)

// Jira Tier 常量
const (
	TierT1    = "T1 Ticket"
	TierOther = "Non-T1 Ticket (General)"
)

// Jira 客户阶段常量
const (
	CustomerStageGoLive    = "Go-live Day"
	CustomerStageHyperCare = "Hyper Care Week"
	CustomerStagePostLive  = "Post-live"
)

// 团队常量
const (
	TeamDedicate = "Dedicate"
)

// Slack Emoji 常量
const (
	EmojiThumbsup = "thumbsup"
	EmojiDone     = "white_check_mark"
	EmojiDoing    = "hourglass_flowing_sand"
)

// Squad 常量
const (
	SquadCRM             = "CRM"
	SquadBrandEnterprise = "CRMBrandEnterprise"
	SquadERP             = "ERP"
	SquadFintech         = "Fintech"
	SquadBEPlatform      = "BE-Platform"
	SquadFEPlatform      = "FE-Platform"
	SquadZihaoTest       = "ZihaoTest"
)

type Schedule struct {
	Name                string
	ScheduleID          string
	AdminTaskScheduleID string
	TeamHandle          string
}

var MoegoContacts = map[string]string{
	"Aathi Parthiban":   "<EMAIL>",
	"Amelia Niu":        "<EMAIL>",
	"Autumn Kessler":    "<EMAIL>",
	"Dominique McNeely": "<EMAIL>",
	"Edward Li":         "<EMAIL>",
	"Elizabeth Johnson": "<EMAIL>",
	"Ellie":             "<EMAIL>",
	"Eneli Valencia":    "<EMAIL>",
	"Gelly":             "<EMAIL>",
	"James Lam":         "<EMAIL>",
	"Jerica Tarquinio":  "<EMAIL>",
	"Jonathan Ai":       "<EMAIL>",
	"Joseph Kilgore":    "<EMAIL>",
	"Kristin":           "<EMAIL>",
	"Marina Lian":       "<EMAIL>",
	"Melanie Lew":       "<EMAIL>",
	"Mia Forbes":        "<EMAIL>",
	// "Other" is excluded as it has no email
	"Rachel Shaw":    "<EMAIL>",
	"Rebekah Martin": "<EMAIL>",
	"Sai Lei":        "<EMAIL>",
	"Sam Sullivan":   "<EMAIL>",
	"Sean CP":        "<EMAIL>",
	"Stella Wang":    "<EMAIL>",
	"Tyler Huang":    "<EMAIL>",
	"Wayne Wei":      "<EMAIL>", // Note: original has uppercase 'W'
	"Lydia Lee":      "<EMAIL>",
	"Emma":           "<EMAIL>",
	"Winni Liu":      "<EMAIL>",
	"Mellie L":       "<EMAIL>",
	"Mel Petty":      "<EMAIL>",
	"Joe Marquez":    "<EMAIL>",
	"Eric Reeser":    "<EMAIL>",
	"Joshua Heffner": "<EMAIL>",
	"Brad Urena":     "<EMAIL>",
	"Nathan Gifford": "<EMAIL>",
	"Jenn Hall":      "<EMAIL>",
}

func GetTeamFromComponents(components []string, componentsSquadsMapping map[string]string) (team string) {
	if len(components) == 0 {
		log.Errorf("no components provided")

		return ""
	}
	team, ok := componentsSquadsMapping[components[0]]
	if !ok {
		log.Errorf("component %s has no team mapping", components[0])

		return ""
	}

	return team
}

// TaskType 用于限定支持的任务类型
type TaskType string

const (
	OncallTaskType TaskType = "oncall"
	AdminTaskType  TaskType = "adminTask"
)
