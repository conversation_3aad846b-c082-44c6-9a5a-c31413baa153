package datadog

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync/atomic"
	"time"

	"github.com/DataDog/datadog-api-client-go/v2/api/datadog"
	"github.com/DataDog/datadog-api-client-go/v2/api/datadogV2"
	backoff "github.com/cenkalti/backoff/v4"
	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/entity"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/slack"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// IncidentGateway 定义了向值班系统发送告警通知的契约。
type IncidentGateway interface {
	// TriggerIncident 发送告警通知到值班系统。
	TriggerIncident(ctx context.Context, incident *Incident) error
	GetCommander(team string, ttype global.TaskType) (commanderEmail string, err error)
	// GetRootCause retrieves the root cause of an incident from Datadog
	GetRootCause(ctx context.Context, incidentID string) (rootCause string, err error)
}

func NewIncidentGateway(
	cfg *configloader.Config,
	slackClient slack.Client,
	csPageReaderWriter entity.CsPageReaderWriter,
	nacosCsConfig *atomic.Pointer[configloader.CSPageConfig]) IncidentGateway {
	return &IncidentGatewayImpl{
		slackClient:        slackClient,
		cfg:                cfg,
		csPageReaderWriter: csPageReaderWriter,
		nacosCsConfig:      nacosCsConfig,
	}
}

type IncidentGatewayImpl struct {
	slackClient        slack.Client
	cfg                *configloader.Config
	csPageReaderWriter entity.CsPageReaderWriter
	nacosCsConfig      *atomic.Pointer[configloader.CSPageConfig]
}

type DutyResponder struct {
	commanderID    string
	commanderEmail string
	teamHandle     string
}

// getCommanderByTaskType retrieves commander information based on the task type
func (d *IncidentGatewayImpl) getCommanderByTaskType(
	apiKey, appKey, team string, taskType global.TaskType) (ret *DutyResponder, err error) {
	ret = new(DutyResponder)

	config := d.nacosCsConfig.Load()
	if config == nil {
		err = fmt.Errorf("getCommanderByTaskType: nacos config not loaded")

		return
	}

	schedule, ok := config.TeamSchedules[team]
	if !ok {
		err = fmt.Errorf("getCommanderByTaskType: team %v not found", team)

		return
	}

	var scheduleID string
	switch taskType {
	case global.OncallTaskType:
		scheduleID = schedule.ScheduleID
	case global.AdminTaskType:
		scheduleID = schedule.AdminTaskScheduleID
	default:
		err = fmt.Errorf("getCommanderByTaskType: unsupported task type %v", taskType)

		return
	}

	if scheduleID == "" {
		err = fmt.Errorf("getCommanderByTaskType: scheduleID not found for task type %v", taskType)

		return
	}
	ret.teamHandle = schedule.TeamHandle

	ctx := context.WithValue(
		context.Background(),
		datadog.ContextAPIKeys,
		map[string]datadog.APIKey{
			"apiKeyAuth": {
				Key: apiKey,
			},
			"appKeyAuth": {
				Key: appKey,
			},
		},
	)
	ctx = context.WithValue(ctx,
		datadog.ContextServerVariables,
		map[string]string{
			"site": "us5.datadoghq.com",
		})
	configuration := datadog.NewConfiguration()

	apiClient := datadog.NewAPIClient(configuration)
	api := datadogV2.NewOnCallApi(apiClient)

	resp, _, err := api.GetScheduleOnCallUser(ctx, scheduleID,
		datadogV2.GetScheduleOnCallUserOptionalParameters{
			Include: datadog.PtrString("user"),
		})
	if err != nil {
		err = fmt.Errorf("getCommanderByTaskType: GetScheduleOnCallUser error:%v", err)

		return
	}
	if len(resp.Included) > 0 {
		ret.commanderID = *resp.Included[0].ScheduleUser.Id
		ret.commanderEmail = *resp.Included[0].ScheduleUser.Attributes.Email
	}
	if ret.commanderID == "" {
		err = fmt.Errorf("getCommanderByTaskType: commanderID not found")

		return
	}

	return
}

func (d *IncidentGatewayImpl) GetCommander(team string, ttype global.TaskType) (commanderEmail string, err error) {
	responder, err := d.getCommanderByTaskType(
		d.cfg.CsPageWatcher.DatadogAPIKey,
		d.cfg.CsPageWatcher.DatadogAPPKey,
		team,
		ttype)
	if err != nil {
		return "", fmt.Errorf("failed to get commander for team %s with type %s: %w", team, ttype, err)
	}

	return responder.commanderEmail, nil
}

func (d *IncidentGatewayImpl) pageATeam(ctx context.Context, apiKey, appKey, title, teamHandle string) error {
	log.InfoContextf(ctx, "pageATeam: will page a team: teamHandle:%v", teamHandle)
	// UserDataID := "c65695fb-b21b-11ef-91b2-dee2953e9407"
	ddCtx := context.WithValue(
		ctx,
		datadog.ContextAPIKeys,
		map[string]datadog.APIKey{
			"apiKeyAuth": {
				Key: apiKey,
			},
			"appKeyAuth": {
				Key: appKey,
			},
		},
	)
	ddCtx = context.WithValue(ddCtx,
		datadog.ContextServerVariables,
		map[string]string{
			"site": "coral.oncall.datadoghq.com",
		})

	handler := datadogV2.OnCallPageTargetType(datadogV2.ONCALLPAGETARGETTYPE_TEAM_HANDLE)
	body := datadogV2.CreatePageRequest{
		Data: &datadogV2.CreatePageRequestData{
			Attributes: &datadogV2.CreatePageRequestDataAttributes{
				Title:   title,
				Urgency: datadogV2.PageUrgency(datadogV2.URGENCY_HIGH),
				Target: datadogV2.CreatePageRequestDataAttributesTarget{
					Identifier: datadog.PtrString(teamHandle),
					Type:       &handler,
				},
			},
			Type: datadogV2.CREATEPAGEREQUESTDATATYPE_PAGES,
		},
	}

	configuration := datadog.NewConfiguration()
	apiClient := datadog.NewAPIClient(configuration)
	api := datadogV2.NewOnCallPagingApi(apiClient)
	resp, _, err := api.CreateOnCallPage(ddCtx, body)
	if err != nil {
		err = fmt.Errorf("pageATeam: CreateOnCallPage error:%v", err)
		log.ErrorContext(ctx, err)

		return err
	}
	log.InfoContextf(ctx, "pageATeam: oncall data: %s", lo.Must(json.Marshal(resp.GetData())))

	return nil
}

func getInitSlackMsg(incident *Incident, devEmail string) string {
	var builder strings.Builder
	builder.WriteString(fmt.Sprintf("Ticket: https://moego.atlassian.net/browse/%v\n", incident.Issue.Key))
	builder.WriteString(fmt.Sprintf("Summary: %v\n", incident.Issue.Summary))

	if incident.Issue.LogoName != "" {
		builder.WriteString(fmt.Sprintf("Logo: %v\n", incident.Issue.LogoName))
	}

	builder.WriteString(fmt.Sprintf("Issue Description: %v\n", incident.Issue.IssueDescription))
	builder.WriteString(fmt.Sprintf("Created By: %v\n", incident.Issue.CreatedByCustom))
	builder.WriteString(fmt.Sprintf("Submitted by(Jira Reporter):%v\n", incident.Issue.Reporter.EmailAddress))
	builder.WriteString(fmt.Sprintf("Priority: %v\n", incident.IssuePriority))
	builder.WriteString(fmt.Sprintf("QA Owner(Jira Assignee): %v\n", incident.Issue.Assignee.EmailAddress))
	builder.WriteString(fmt.Sprintf("Eng Owner: %v", devEmail))

	return builder.String()
}

// TriggerIncident implements IncidentGateway.
func (d *IncidentGatewayImpl) TriggerIncident(ctx context.Context, incident *Incident) error {
	dbPage, dbErr := d.csPageReaderWriter.GetCsPage(&entity.CsPage{CsPageJiraTicket: incident.Issue.Key})
	var strategy IncidentTriggerStrategy
	if dbErr != nil {
		strategy = &FirstTimeTriggerStrategy{impl: d}
	} else {
		strategy = &RepeatTriggerStrategy{impl: d, dbPage: dbPage}
	}

	return strategy.Trigger(ctx, incident)
}

/*
handleIncidentCreation 用于处理 Datadog 事件（Incident）的创建流程，主要包括：
1. 获取当前 oncall 指挥官信息（根据团队和任务类型）。
2. 调用 Datadog API 创建新的 Incident。
3. 通知 oncall 团队进行响应。
4. 获取并返回该 Incident 关联的 Slack 群 ID（用于后续沟通）。
5. 尝试将相关的 refUser 邀请进 incident 群聊。
6. 向 incident 群发送初始化消息，便于团队成员了解事件详情。
最终返回 Datadog Incident ID、Slack 群 ID、指挥官信息和错误信息。
*/
func (d *IncidentGatewayImpl) handleIncidentCreation(
	ctx context.Context, incident *Incident, ddAPIKey, ddAPPKey string) (
	datadogIncidentID int64, incidentSlackChannelID string, responder *DutyResponder, err error) {
	responder, err = d.getCommanderByTaskType(ddAPIKey, ddAPPKey, incident.OncallTeam, global.OncallTaskType)
	if err != nil {
		log.ErrorContextf(ctx, "TriggerIncident: get command id failed: %v", err)

		return 0, "", nil, err
	}

	title := fmt.Sprintf("[%v] CS Page Incident: %v", incident.Issue.Key, incident.Issue.Summary)
	summary := fmt.Sprintf(`jira url: https://moego.atlassian.net/browse/%v
summary: %v`, incident.Issue.Key, incident.Issue.Summary)

	datadogIncidentID, err = createIncident(
		ddAPIKey, ddAPPKey, responder.commanderID, responder.teamHandle, title, summary)
	if err != nil {
		log.ErrorContextf(ctx, "TriggerIncident: createIncident failed: %v", err)

		return 0, "", nil, err
	}

	time.Sleep(time.Second * 5)
	err = d.pageATeam(ctx, ddAPIKey, ddAPPKey, title, responder.teamHandle)

	err = backoff.Retry(func() error {
		incidentSlackChannelID, err = d.getIncidentInfo(ddAPIKey, ddAPPKey, fmt.Sprint(datadogIncidentID))
		if err != nil {
			log.ErrorContextf(ctx, "TriggerIncident: getIncidentInfo failed: %v", err)
		}

		return err
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
	if err != nil {
		log.ErrorContextf(ctx, "TriggerIncident: getIncidentInfo failed after retries: %v", err)
		incidentSlackChannelID = ""
	}

	// 尝试将refUser邀请进incidentChannel
	if incidentSlackChannelID != "" {
		_ = d.addMembers2IncidentChannel(ctx, incidentSlackChannelID, incident.RefUsers)
	}

	// 发送一条初始化消息到incident群里
	err = backoff.Retry(func() error {
		_, err = d.slackClient.SendMessage(
			incidentSlackChannelID, getInitSlackMsg(incident, responder.commanderEmail))

		return err
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
	if err != nil {
		log.ErrorContextf(ctx, "TriggerIncident: sendInitMessage2IncidentChannel failed after retries: %v", err)
	}

	return datadogIncidentID, incidentSlackChannelID, responder, nil
}

func (d *IncidentGatewayImpl) handleT1SlackNotification(
	ctx context.Context, incident *Incident, commanderEmail string) (t1NotifyMessageTimestamp string, err error) {
	err = backoff.Retry(func() error {
		t1NotifyMessageTimestamp, err = d.sendInitMessage2T1Channel(
			getInitSlackMsg(incident, commanderEmail))
		if err != nil {
			log.ErrorContextf(ctx, "handleT1SlackNotification: sendInitMessage2T1Channel failed: %v", err)
		}

		return err
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))

	return t1NotifyMessageTimestamp, err
}

func (d *IncidentGatewayImpl) slackNotifyAssignee(
	ctx context.Context, incident *Incident, devEmail string) (err error) {
	if devEmail == UnknownDevEmail {
		return
	}
	msg := getInitSlackMsg(incident, devEmail)
	err = d.slackClient.SendMessageToPerson(incident.Issue.Assignee.EmailAddress, msg)
	if err != nil {
		log.ErrorContextf(ctx, "slackNotifyAssignee: NotifyAssignee failed: %v", err)
	}

	return
}

func (d *IncidentGatewayImpl) addMembers2IncidentChannel(
	ctx context.Context, incidentSlackChannelID string, refUsers []string) (err error) {
	err = d.slackClient.JoinChannel(incidentSlackChannelID)
	if err != nil {
		log.ErrorContextf(ctx, "addMembers2IncidentChannel: JoinChannel failed: %v", err)

		return
	}
	time.Sleep(time.Second)
	// 去除重复的refUsers
	refUsers = lo.Uniq(refUsers)
	err = d.slackClient.AddMembersToChannel(incidentSlackChannelID, refUsers)
	if err != nil {
		log.ErrorContextf(ctx, "addMembers2IncidentChannel: AddMembersToChannel failed: %v", err)
	}

	return
}

func (d *IncidentGatewayImpl) getIncidentInfo(
	apiKey, appKey, publicID string) (slackChannelID string, err error) {
	ctx := context.WithValue(
		context.Background(),
		datadog.ContextAPIKeys,
		map[string]datadog.APIKey{
			"apiKeyAuth": {
				Key: apiKey,
			},
			"appKeyAuth": {
				Key: appKey,
			},
		},
	)
	ctx = context.WithValue(ctx,
		datadog.ContextServerVariables,
		map[string]string{
			"site": "us5.datadoghq.com",
		})
	configuration := datadog.NewConfiguration()
	configuration.SetUnstableOperationEnabled("v2.ListIncidentIntegrations", true)

	apiClient := datadog.NewAPIClient(configuration)
	api := datadogV2.NewIncidentsApi(apiClient)
	resp, _, err := api.ListIncidentIntegrations(ctx, publicID)
	if err != nil {
		return "", err
	}
	for _, v := range resp.Data {
		attr := v.Attributes
		if attr != nil && attr.IntegrationType == 1 {
			metadata := attr.Metadata.SlackIntegrationMetadata
			if metadata != nil {
				channels := metadata.Channels
				if len(channels) > 0 {
					return channels[0].ChannelId, nil
				}
			}
		}
	}

	return "", fmt.Errorf("cannot find slack channel id")
}

func (d *IncidentGatewayImpl) sendInitMessage2T1Channel(msg string) (ts string, err error) {
	ts, err = d.slackClient.SendMessage(d.cfg.CsPageWatcher.T1SlackChannelID, msg)
	if err != nil {
		return
	}

	return
}

func createIncident(apiKey, appKey,
	commanderUserID, team, title, summary string) (publicID int64, err error) {
	log.Infof("createIncident: will create Incident, commanderUserID:%v,%v, title:%v", commanderUserID, team, title)
	// UserDataID := "c65695fb-b21b-11ef-91b2-dee2953e9407"
	teamList := []string{team}
	body := datadogV2.IncidentCreateRequest{
		Data: datadogV2.IncidentCreateData{
			Type: datadogV2.INCIDENTTYPE_INCIDENTS,
			Attributes: datadogV2.IncidentCreateAttributes{
				Title:               title,
				CustomerImpacted:    true,
				CustomerImpactScope: datadog.PtrString(summary),
				Fields: map[string]datadogV2.IncidentFieldAttributes{
					"state": {
						IncidentFieldAttributesSingleValue: &datadogV2.IncidentFieldAttributesSingleValue{
							Type:  datadogV2.INCIDENTFIELDATTRIBUTESSINGLEVALUETYPE_DROPDOWN.Ptr(),
							Value: *datadog.NewNullableString(datadog.PtrString("active")),
						}},
					"summary": {
						IncidentFieldAttributesSingleValue: &datadogV2.IncidentFieldAttributesSingleValue{
							Type:  datadogV2.INCIDENTFIELDATTRIBUTESSINGLEVALUETYPE_TEXTBOX.Ptr(),
							Value: *datadog.NewNullableString(datadog.PtrString(summary)),
						}},
					"detection_method": {
						IncidentFieldAttributesSingleValue: &datadogV2.IncidentFieldAttributesSingleValue{
							Type:  datadogV2.INCIDENTFIELDATTRIBUTESSINGLEVALUETYPE_DROPDOWN.Ptr(),
							Value: *datadog.NewNullableString(datadog.PtrString("customer")),
						}},
					"teams": {
						IncidentFieldAttributesMultipleValue: &datadogV2.IncidentFieldAttributesMultipleValue{
							Type:  datadogV2.INCIDENTFIELDATTRIBUTESVALUETYPE_MULTISELECT.Ptr(),
							Value: *datadog.NewNullableList(&teamList),
						}},
					"severity": {
						IncidentFieldAttributesSingleValue: &datadogV2.IncidentFieldAttributesSingleValue{
							Type:  datadogV2.INCIDENTFIELDATTRIBUTESSINGLEVALUETYPE_DROPDOWN.Ptr(),
							Value: *datadog.NewNullableString(datadog.PtrString("SEV-3")),
						}},
				},
			},
			Relationships: &datadogV2.IncidentCreateRelationships{
				CommanderUser: *datadogV2.NewNullableNullableRelationshipToUser(
					&datadogV2.NullableRelationshipToUser{
						Data: *datadogV2.NewNullableNullableRelationshipToUserData(
							&datadogV2.NullableRelationshipToUserData{
								Type: datadogV2.USERSTYPE_USERS,
								Id:   commanderUserID,
							}),
					}),
			},
		},
	}
	ctx := context.WithValue(
		context.Background(),
		datadog.ContextAPIKeys,
		map[string]datadog.APIKey{
			"apiKeyAuth": {
				Key: apiKey,
			},
			"appKeyAuth": {
				Key: appKey,
			},
		},
	)
	ctx = context.WithValue(ctx,
		datadog.ContextServerVariables,
		map[string]string{
			"site": "us5.datadoghq.com",
		})
	configuration := datadog.NewConfiguration()
	configuration.SetUnstableOperationEnabled("v2.CreateIncident", true)
	apiClient := datadog.NewAPIClient(configuration)
	api := datadogV2.NewIncidentsApi(apiClient)
	resp, _, err := api.CreateIncident(ctx, body)
	if err != nil {
		err = fmt.Errorf("createIncident, Error when calling `IncidentsApi.CreateIncident`: %s", err.Error())

		return
	}
	publicID = *resp.Data.Attributes.PublicId

	return
}

func (d *IncidentGatewayImpl) GetRootCause(ctx context.Context, incidentID string) (rootCause string, err error) {
	ddCtx := context.WithValue(
		ctx,
		datadog.ContextAPIKeys,
		map[string]datadog.APIKey{
			"apiKeyAuth": {
				Key: d.cfg.CsPageWatcher.DatadogAPIKey,
			},
			"appKeyAuth": {
				Key: d.cfg.CsPageWatcher.DatadogAPPKey,
			},
		},
	)
	ddCtx = context.WithValue(ddCtx,
		datadog.ContextServerVariables,
		map[string]string{
			"site": "us5.datadoghq.com",
		})

	configuration := datadog.NewConfiguration()
	configuration.SetUnstableOperationEnabled("v2.GetIncident", true)

	apiClient := datadog.NewAPIClient(configuration)
	api := datadogV2.NewIncidentsApi(apiClient)

	resp, _, err := api.GetIncident(ddCtx, incidentID)
	if err != nil {
		return "", fmt.Errorf("failed to get incident %s: %w", incidentID, err)
	}

	// Extract root cause from incident fields
	if resp.Data.Attributes != nil {
		// Check if there's a field specifically for root cause
		if fields := resp.Data.Attributes.Fields; fields != nil {
			// Look for common field names that might contain root cause information
			if rootCauseField, exists := fields["root_cause"]; exists {
				if singleValue := rootCauseField.IncidentFieldAttributesSingleValue; singleValue != nil {
					if singleValue.Value.IsSet() {
						// Get the actual string value from the nullable string
						if strPtr := singleValue.Value.Get(); strPtr != nil {
							rootCause = *strPtr
						}
					}
				}
			}

			// Also check for "cause" field
			if rootCause == "" {
				if causeField, exists := fields["cause"]; exists {
					if singleValue := causeField.IncidentFieldAttributesSingleValue; singleValue != nil {
						if singleValue.Value.IsSet() {
							// Get the actual string value from the nullable string
							if strPtr := singleValue.Value.Get(); strPtr != nil {
								rootCause = *strPtr
							}
						}
					}
				}
			}
		}

		// If no specific field found, use the incident description/summary as fallback
		if rootCause == "" && resp.Data.Attributes.CustomerImpactScope.IsSet() {
			if strPtr := resp.Data.Attributes.CustomerImpactScope.Get(); strPtr != nil {
				rootCause = *strPtr
			}
		}
	}

	return rootCause, nil
}
