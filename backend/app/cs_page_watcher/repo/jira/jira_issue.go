package jira

import "time"

// UserInfo represents the assignee of a Jira issue.
type UserInfo struct {
	DisplayName  string
	EmailAddress string
}

// Parent represents the parent of a Jira issue (for subtasks).
type Parent struct {
	ID  string `json:"id,omitempty" structs:"id,omitempty"`
	Key string `json:"key,omitempty" structs:"key,omitempty"`
}

type Issue struct {
	ID      string
	Key     string
	Summary string
	Status  string
	Comment string
	// 这里通过global.GetTeamFromComponents获取
	JiraSquad            string
	IssueDescription     string
	Description          string
	Created              time.Time
	Updated              time.Time
	Assignee             UserInfo // 添加Assignee字段
	Reporter             UserInfo // 添加Assignee字段
	Parent               *Parent  // 添加Parent字段用于子任务
	T1OrGeneral          string
	IssuePriority        string
	CustomerStage        string
	Components           []string // 添加 Components 字段
	SLABreach            string
	LogoName             string
	LocationName         string
	DevEngineer          []UserInfo
	ResolutionTimeCustom string // 添加 ResolutionTimeCustom 字段
	CreatedByCustom      string // 添加 CreatedByCustom 字段
	FeatureDomains       string // 添加 FeatureDomains 字段
	// CustomFields  map[string]interface{} // 用于存储用户自定义的字段
}
