server:
  filter:
    - debuglog
    - recovery
  service:
    - name: backend.proto.fulfillment.v1.FulfillmentService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.fulfillment.v1.AppointmentService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.fulfillment.v1.FulfillmentReportService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.fulfillment.v1.CalendarService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
client:
  network: tcp
  protocol: grpc
  filter:
    - debuglog
  transport: grpc
  timeout: 600000
  service:
    - callee: postgres.moego_fulfillment
      target: dsn://postgresql://moego_developer_240310_eff7a0dc:<EMAIL>:40132/moego_fulfillment?sslmode=disable
      protocol: gorm
      transport: gorm
    - callee: moego-svc-business-customer
      target: dns://moego-svc-business-customer:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-organization
      target: dns://moego-svc-organization:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-appointment
      target: dns://moego-svc-appointment:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-server-grooming
      target: http://moego-service-grooming:9206
      protocol: http
    - callee: moego-server-message
      target: http://moego-service-message:9205
      protocol: http
    - callee: moego-server-payment
      target: http://moego-service-payment:9204
      protocol: http
    - callee: moego-offering
      target: dns://moego-offering:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-offering
      target: dns://moego-svc-offering:9090
      protocol: grpc
      network: tcp
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres.moego_fulfillment
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  config:
    redis:
      host: redis.t2.moego.dev
      port: 40179
      password: iMoReGoTdesstingeCache250310_7fec987d
      tls: true