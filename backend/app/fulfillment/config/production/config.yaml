secrets:
  - name: 'moego/production/datasource'
    prefix: 'secret.datasource.'
  - name: "moego/production/redis"
    prefix: "secret.redis."
server:
  filter:
    - opentelemetry
    - recovery
    - debuglog
  service:
    - name: backend.proto.fulfillment.v1.FulfillmentService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.fulfillment.v1.AppointmentService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.fulfillment.v1.FulfillmentReportService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.fulfillment.v1.CalendarService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 5000
  service:
    - callee: postgres.moego_fulfillment
      target: dsn://postgresql://${secret.datasource.postgres.moego_fulfillment.username}:${secret.datasource.postgres.moego_fulfillment.password}@${secret.datasource.postgres.url.master}:${secret.datasource.postgres.port}/moego_fulfillment?sslmode=disable
      protocol: gorm
      transport: gorm
    - callee: moego-svc-business-customer
      target: dns://moego-svc-business-customer:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-organization
      target: dns://moego-svc-organization:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-appointment
      target: dns://moego-svc-appointment:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-server-grooming
      target: http://moego-service-grooming:9206
      protocol: http
    - callee: moego-server-message
      target: http://moego-service-message:9205
      protocol: http
    - callee: moego-server-payment
      target: http://moego-service-payment:9204
      protocol: http
    - callee: moego-offering
      target: dns://moego-offering:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-offering
      target: dns://moego-svc-offering:9090
      protocol: grpc
      network: tcp
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres.moego_fulfillment
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
  config:
    redis:
      host: ${secret.redis.host}
      port: ${secret.redis.port}
      password: ${secret.redis.password}
      tls: ${secret.redis.tls}
