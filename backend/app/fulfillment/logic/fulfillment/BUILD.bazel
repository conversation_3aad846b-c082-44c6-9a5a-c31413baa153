load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "fulfillment",
    srcs = [
        "fulfillment.go",
        "pet.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/fulfillment",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db/appointment",
        "//backend/app/fulfillment/repo/db/fulfillment",
        "//backend/app/fulfillment/repo/db/serviceinstance",
        "//backend/app/fulfillment/repo/pet",
        "//backend/common/rpc/framework/log",
        "//backend/proto/fulfillment/v1:fulfillment",
        "//backend/proto/offering/v1:offering",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "fulfillment_test",
    srcs = [
        "fulfillment_test.go",
        "pet_test.go",
    ],
    embed = [":fulfillment"],
    deps = [
        "//backend/app/fulfillment/repo/db/appointment",
        "//backend/app/fulfillment/repo/db/appointment/mock",
        "//backend/app/fulfillment/repo/db/fulfillment",
        "//backend/app/fulfillment/repo/db/fulfillment/mock",
        "//backend/app/fulfillment/repo/db/serviceinstance",
        "//backend/app/fulfillment/repo/db/serviceinstance/mock",
        "//backend/app/fulfillment/repo/pet/mock",
        "//backend/proto/fulfillment/v1:fulfillment",
        "//backend/proto/offering/v1:offering",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/business_customer/v1:business_customer",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/customer/v1:customer",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_mock//gomock",
        "@tools_gotest_v3//assert",
    ],
)
