// greeter/entity.go 包含了该logic层内会用到的所有实体
// 请注意, 为了可迭代和可维护, 即使logic entity 和 repo entity 是同一个实体, 也请分开定义
package fulfillment

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/appointment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/pet"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

const (
	defaultLimit = 200
)

func New() *Logic {
	return &Logic{
		fulfillmentCli:     fulfillment.New(),
		appointmentCli:     appointment.New(),
		serviceInstanceCli: serviceinstance.New(),
		petCli:             pet.New(),
	}
}

func NewByParams(
	fulfillmentCli fulfillment.ReadWriter,
	appointmentCli appointment.ReadWriter,
	serviceInstanceCli serviceinstance.ReadWriter,
	petCli pet.ReadWriter,
) *Logic {
	return &Logic{
		fulfillmentCli:     fulfillmentCli,
		appointmentCli:     appointmentCli,
		serviceInstanceCli: serviceInstanceCli,
		petCli:             petCli,
	}
}

type Logic struct {
	fulfillmentCli     fulfillment.ReadWriter
	appointmentCli     appointment.ReadWriter
	serviceInstanceCli serviceinstance.ReadWriter
	petCli             pet.ReadWriter
}

func (l *Logic) ListFulfillment(ctx context.Context, req *pb.ListFulfillmentRequest) (
	*pb.ListFulfillmentResponse, error) {
	// 校验
	if err := verifyGetFulfillmentRequest(req); err != nil {
		return nil, err
	}
	limit := int(req.GetPagination().GetLimit())
	if limit == 0 {
		limit = defaultLimit
	}
	// 组包
	startTime := time.Unix(req.StartTime.Seconds, int64(req.StartTime.Nanos))
	endTime := time.Unix(req.EndTime.Seconds, int64(req.EndTime.Nanos))
	param := &fulfillment.BaseParam{
		BusinessID: int32(req.GetBusinessId()),
		CompanyID:  int32(req.GetCompanyId()),
		PaginationInfo: &fulfillment.PaginationInfo{
			Offset: req.GetPagination().GetOffset(),
			Limit:  int32(limit),
		},
		StartTime: startTime,
		EndTime:   endTime,
	}
	filter := buildFilter(req)
	// 查询
	fulfillmentList, err := l.fulfillmentCli.List(ctx, param, filter)
	if err != nil {
		return nil, err
	}
	total, err := l.fulfillmentCli.Count(ctx, param, filter)
	if err != nil {
		return nil, err
	}
	// 拼回包
	var pbList []*pb.Fulfillment
	for _, f := range fulfillmentList {
		pbList = append(pbList, &pb.Fulfillment{
			Id:                f.ID,
			BusinessId:        f.BusinessID,
			CustomerId:        f.CustomerID,
			PetId:             f.PetID,
			ServiceInstanceId: f.ServiceInstanceID,
			ServiceFactoryId:  f.ServiceFactoryID,
			CareType:          offeringpb.CareCategory(f.CareType),
			LodgingId:         f.LodgingID,
			PlaygroupId:       f.PlaygroupID,
			State:             pb.State(f.State),
			StaffIds:          []int64{f.StaffID}, // 需根据实际表结构补充
			StartTime:         timestamppb.New(f.StartTime),
			EndTime:           timestamppb.New(f.EndTime),
		})
	}
	// 4. 组装返回
	return &pb.ListFulfillmentResponse{
		Pagination:   req.GetPagination(),
		Total:        int32(total),
		Fulfillments: pbList,
		IsEnd:        len(pbList) < limit,
	}, nil
}

func verifyGetFulfillmentRequest(req *pb.ListFulfillmentRequest) error {
	startTime := req.GetStartTime().AsTime()
	endTime := req.GetEndTime().AsTime()
	if startTime.After(endTime) {
		return status.Error(codes.InvalidArgument, "start time must be after end")
	}

	return nil
}

func buildFilter(req *pb.ListFulfillmentRequest) *fulfillment.Filter {
	var staffID int64
	petIDs := []int64{}
	states := []int32{}
	customerIDs := []int64{}
	careTypes := []int32{}
	if req.GetFilter() != nil {
		if req.GetFilter().GetPetIds() != nil {
			petIDs = req.GetFilter().GetPetIds()
		}
		if req.GetFilter().GetStates() != nil {
			states = req.GetFilter().GetStates()
		}
		if req.GetFilter().GetCustomerIds() != nil {
			customerIDs = req.GetFilter().GetCustomerIds()
		}
		careTypes = changeCareType(req.GetFilter().GetCareTypes())
		if len(req.GetFilter().GetStaffIds()) > 0 {
			staffID = req.GetFilter().GetStaffIds()[0]
		}
	}

	return &fulfillment.Filter{
		PetIDs:      petIDs,
		StaffID:     staffID,
		States:      states,
		CustomerIDs: customerIDs,
		CareTypes:   careTypes,
	}
}

func changeCareType(careTypes []offeringpb.CareCategory) []int32 {
	if careTypes == nil {
		return []int32{}
	}
	result := []int32{}
	for _, c := range careTypes {
		result = append(result, int32(c))
	}

	return result
}
