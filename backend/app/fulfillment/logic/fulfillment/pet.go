package fulfillment

import (
	"context"

	appointmentRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/appointment"
	fulfillmentRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

// GetPetsByTimeRange 根据时间区间获取宠物信息
func (l *Logic) GetPetsByTimeRange(ctx context.Context,
	req *pb.GetPetsByTimeRangeRequest) (*pb.GetPetsByTimeRangeResponse, error) {
	log.InfoContextf(ctx, "GetPetsByTimeRange req:%+v", req)

	// 1. 通过时间范围查询appointmentIDs
	appointmentIDs, err := l.getAppointmentIDsByTimeRange(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "failed to get appointment IDs: %v", err)

		return nil, err
	}

	if len(appointmentIDs) == 0 {
		return &pb.GetPetsByTimeRangeResponse{
			Pets:  []*pb.PetInfo{},
			Total: 0,
		}, nil
	}

	// 2. 通过appointmentIDs查询serviceInstanceList，统计petIDs并去重
	petIDs, err := l.getPetIDsFromAppointments(ctx, appointmentIDs)
	if err != nil {
		log.ErrorContextf(ctx, "failed to get pet IDs from appointments: %v", err)

		return nil, err
	}

	if len(petIDs) == 0 {
		return &pb.GetPetsByTimeRangeResponse{
			Pets:  []*pb.PetInfo{},
			Total: 0,
		}, nil
	}

	// 3. 通过petIDs查询pet信息并填充回包
	pets, err := l.getPetInfos(ctx, req.GetCompanyId(), petIDs)
	if err != nil {
		log.ErrorContextf(ctx, "failed to get pet infos: %v", err)

		return nil, err
	}

	return &pb.GetPetsByTimeRangeResponse{
		Pets:  pets,
		Total: int32(len(pets)),
	}, nil
}

// getAppointmentIDsByTimeRange 通过时间范围查询appointmentIDs
func (l *Logic) getAppointmentIDsByTimeRange(ctx context.Context, req *pb.GetPetsByTimeRangeRequest) ([]int64, error) {
	// 构建查询参数
	param := &appointmentRepo.BaseParam{
		BusinessID: int32(req.GetBusinessId()),
		CompanyID:  int32(req.GetCompanyId()),
		StartTime:  req.GetStartTime().AsTime(),
		EndTime:    req.GetEndTime().AsTime(),
	}

	// 查询appointments
	appointments, err := l.appointmentCli.List(ctx, param, nil)
	if err != nil {
		return nil, err
	}

	// 提取appointmentIDs
	appointmentIDs := make([]int64, 0, len(appointments))
	for _, apt := range appointments {
		appointmentIDs = append(appointmentIDs, int64(apt.ID))
	}

	return appointmentIDs, nil
}

// getPetIDsFromAppointments 通过appointmentIDs查询serviceInstanceList，统计petIDs并去重
func (l *Logic) getPetIDsFromAppointments(ctx context.Context, appointmentIDs []int64) ([]int64, error) {
	if len(appointmentIDs) == 0 {
		return []int64{}, nil
	}

	// 批量查询所有appointmentIDs对应的service instances
	serviceInstances, err := l.serviceInstanceCli.GetByAppointmentIDs(ctx, appointmentIDs)
	if err != nil {
		log.ErrorContextf(ctx, "failed to get service instances for appointments %v: %v", appointmentIDs, err)

		return nil, err
	}

	// 统计petIDs并去重
	petIDSet := make(map[int64]bool)
	for _, si := range serviceInstances {
		if si.PetID > 0 {
			petIDSet[int64(si.PetID)] = true
		}
	}

	// 转换为slice
	petIDs := make([]int64, 0, len(petIDSet))
	for petID := range petIDSet {
		petIDs = append(petIDs, petID)
	}

	return petIDs, nil
}

// getPetInfos 通过petIDs查询pet信息并转换为PetInfo
func (l *Logic) getPetInfos(ctx context.Context, companyID int64, petIDs []int64) ([]*pb.PetInfo, error) {
	if len(petIDs) == 0 {
		return []*pb.PetInfo{}, nil
	}

	// 批量查询pet信息
	petModels, err := l.petCli.BatchGetPetInfo(ctx, companyID, petIDs)
	if err != nil {
		return nil, err
	}

	// 转换为PetInfo
	pets := make([]*pb.PetInfo, 0, len(petModels))
	for _, petModel := range petModels {
		petInfo := &pb.PetInfo{
			Id:         petModel.GetId(),
			AvatarPath: petModel.GetAvatarPath(),
			Name:       petModel.GetPetName(),
			Type:       int32(petModel.GetPetType()),
		}
		pets = append(pets, petInfo)
	}

	return pets, nil
}

// GetPetCountByTimeRange 根据时间区间统计不同careType的宠物数量
func (l *Logic) GetPetCountByTimeRange(ctx context.Context,
	req *pb.GetPetCountByTimeRangeRequest) (*pb.GetPetCountByTimeRangeResponse, error) {
	log.InfoContextf(ctx, "GetPetCountByTimeRange req:%+v", req)

	// 1. 通过时间范围查询appointmentIDs
	appointmentIDs, err := l.getAppointmentIDsByTimeRangeForCount(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "failed to get appointment IDs: %v", err)

		return nil, err
	}

	if len(appointmentIDs) == 0 {
		return &pb.GetPetCountByTimeRangeResponse{
			DailyPetCounts: []*pb.DailyPetCount{},
			TotalPetCount:  0,
		}, nil
	}

	// 2. 根据appointments查询fulfillment记录
	fulfillments, err := l.getFulfillmentsByAppointmentIDs(ctx, appointmentIDs)
	if err != nil {
		log.ErrorContextf(ctx, "failed to get fulfillments: %v", err)

		return nil, err
	}

	if len(fulfillments) == 0 {
		return &pb.GetPetCountByTimeRangeResponse{
			DailyPetCounts: []*pb.DailyPetCount{},
			TotalPetCount:  0,
		}, nil
	}

	// 3. 统计和分组数据
	dailyCounts, totalPetCount := l.analyzeFulfillmentData(fulfillments)

	return &pb.GetPetCountByTimeRangeResponse{
		DailyPetCounts: dailyCounts,
		TotalPetCount:  int32(totalPetCount),
	}, nil
}

// getAppointmentIDsByTimeRangeForCount 通过时间范围查询appointmentIDs（用于统计）
func (l *Logic) getAppointmentIDsByTimeRangeForCount(ctx context.Context,
	req *pb.GetPetCountByTimeRangeRequest) ([]int64, error) {
	// 构建查询参数
	param := &appointmentRepo.BaseParam{
		BusinessID: int32(req.GetBusinessId()),
		CompanyID:  int32(req.GetCompanyId()),
		StartTime:  req.GetStartTime().AsTime(),
		EndTime:    req.GetEndTime().AsTime(),
	}

	// 查询appointments
	appointments, err := l.appointmentCli.List(ctx, param, nil)
	if err != nil {
		return nil, err
	}

	// 提取appointmentIDs
	appointmentIDs := make([]int64, 0, len(appointments))
	for _, apt := range appointments {
		appointmentIDs = append(appointmentIDs, int64(apt.ID))
	}

	return appointmentIDs, nil
}

// getFulfillmentsByAppointmentIDs 根据appointmentIDs查询fulfillment记录
func (l *Logic) getFulfillmentsByAppointmentIDs(ctx context.Context,
	appointmentIDs []int64) ([]*fulfillmentRepo.Fulfillment, error) {
	if len(appointmentIDs) == 0 {
		return []*fulfillmentRepo.Fulfillment{}, nil
	}

	// 批量查询所有appointmentIDs对应的fulfillment记录
	fulfillments, err := l.fulfillmentCli.GetByAppointmentIDs(ctx, appointmentIDs)
	if err != nil {
		log.ErrorContextf(ctx, "failed to get fulfillments for appointments %v: %v", appointmentIDs, err)

		return nil, err
	}

	return fulfillments, nil
}

// analyzeFulfillmentData 分析fulfillment数据，按日期分组统计
func (l *Logic) analyzeFulfillmentData(fulfillments []*fulfillmentRepo.Fulfillment) ([]*pb.DailyPetCount, int) {
	// 按日期分组的数据结构
	dailyData := make(map[string]*dailyStats)

	// 用于统计总宠物数量的set
	totalPetSet := make(map[int64]bool)

	for _, f := range fulfillments {
		// 统计总宠物数量（去重）
		if f.PetID > 0 {
			totalPetSet[f.PetID] = true
		}

		// 按日期分组统计
		date := f.StartTime.Format("2006-01-02")
		if dailyData[date] == nil {
			dailyData[date] = &dailyStats{
				careTypePets: make(map[int32]map[int64]bool), // careType -> petID set
				lodgingPets:  make(map[int64]bool),           // petID set for lodging
			}
		}

		// 统计careType
		if f.PetID > 0 && f.CareType > 0 {
			if dailyData[date].careTypePets[f.CareType] == nil {
				dailyData[date].careTypePets[f.CareType] = make(map[int64]bool)
			}
			dailyData[date].careTypePets[f.CareType][f.PetID] = true
		}

		// 统计需要住宿的宠物
		if f.PetID > 0 && f.LodgingID > 0 {
			dailyData[date].lodgingPets[f.PetID] = true
		}
	}

	// 转换为DailyPetCount
	var dailyCounts []*pb.DailyPetCount
	for date, stats := range dailyData {
		// 构建careType统计
		careTypeCount := make(map[int64]int32)
		for careType, petSet := range stats.careTypePets {
			careTypeCount[int64(careType)] = int32(len(petSet))
		}

		dailyCount := &pb.DailyPetCount{
			Date:                date,
			PetCareTypeCount:    careTypeCount,
			PetCountWithLodging: int32(len(stats.lodgingPets)),
		}
		dailyCounts = append(dailyCounts, dailyCount)
	}

	return dailyCounts, len(totalPetSet)
}

// dailyStats 每日统计数据
type dailyStats struct {
	careTypePets map[int32]map[int64]bool // careType -> petID set
	lodgingPets  map[int64]bool           // petID set for lodging
}
