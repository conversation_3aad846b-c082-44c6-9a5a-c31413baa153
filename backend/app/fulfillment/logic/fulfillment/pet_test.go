package fulfillment

import (
	"context"
	"errors"
	"testing"
	"time"

	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gotest.tools/v3/assert"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	customermodel "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	appointmentRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/appointment"
	appointmentMock "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/appointment/mock"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	fulfillmentMock "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment/mock"
	serviceinstanceRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	serviceinstanceMock "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance/mock"
	petMock "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/pet/mock"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

func TestAnalyzeFulfillmentData_EmptyData(t *testing.T) {
	// 测试空数据情况
	fulfillments := []*fulfillment.Fulfillment{}

	logic := &Logic{}
	dailyCounts, totalPetCount := logic.analyzeFulfillmentData(fulfillments)

	assert.Equal(t, len(dailyCounts), 0)
	assert.Equal(t, totalPetCount, 0)
}

func TestAnalyzeFulfillmentData_SingleDay(t *testing.T) {
	// 测试单日数据统计
	now := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:        1,
			PetID:     100,
			CareType:  1, // GROOMING
			LodgingID: 0,
			StartTime: now,
		},
		{
			ID:        2,
			PetID:     200,
			CareType:  2, // BOARDING
			LodgingID: 10,
			StartTime: now,
		},
		{
			ID:        3,
			PetID:     100, // 同一只宠物，应该去重
			CareType:  1,
			LodgingID: 0,
			StartTime: now,
		},
	}

	logic := &Logic{}
	dailyCounts, totalPetCount := logic.analyzeFulfillmentData(fulfillments)

	// 验证总宠物数量（去重后应该是2只）
	assert.Equal(t, totalPetCount, 2)

	// 验证每日统计数据
	assert.Equal(t, len(dailyCounts), 1)

	dailyCount := dailyCounts[0]
	assert.Equal(t, dailyCount.Date, "2024-01-15")

	// 验证care type统计
	assert.Equal(t, len(dailyCount.PetCareTypeCount), 2)
	assert.Equal(t, dailyCount.PetCareTypeCount[1], int32(1)) // GROOMING: 1只宠物
	assert.Equal(t, dailyCount.PetCareTypeCount[2], int32(1)) // BOARDING: 1只宠物

	// 验证住宿宠物数量
	assert.Equal(t, dailyCount.PetCountWithLodging, int32(1)) // 只有petID=200的宠物需要住宿
}

func TestAnalyzeFulfillmentData_MultipleDays(t *testing.T) {
	// 测试多日数据统计
	day1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	day2 := time.Date(2024, 1, 16, 10, 0, 0, 0, time.UTC)

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:        1,
			PetID:     100,
			CareType:  1,
			LodgingID: 0,
			StartTime: day1,
		},
		{
			ID:        2,
			PetID:     200,
			CareType:  2,
			LodgingID: 10,
			StartTime: day1,
		},
		{
			ID:        3,
			PetID:     300,
			CareType:  1,
			LodgingID: 0,
			StartTime: day2,
		},
		{
			ID:        4,
			PetID:     100, // 同一只宠物在不同日期
			CareType:  3,
			LodgingID: 20,
			StartTime: day2,
		},
	}

	logic := &Logic{}
	dailyCounts, totalPetCount := logic.analyzeFulfillmentData(fulfillments)

	// 验证总宠物数量（去重后应该是3只）
	assert.Equal(t, totalPetCount, 3)

	// 验证每日统计数据
	assert.Equal(t, len(dailyCounts), 2)

	// 按日期排序以便验证
	var day1Count, day2Count *pb.DailyPetCount
	for _, count := range dailyCounts {
		if count.Date == "2024-01-15" {
			day1Count = count
		} else if count.Date == "2024-01-16" {
			day2Count = count
		}
	}

	// 验证第一天数据
	assert.Assert(t, day1Count != nil)
	assert.Equal(t, len(day1Count.PetCareTypeCount), 2)
	assert.Equal(t, day1Count.PetCareTypeCount[1], int32(1)) // GROOMING: 1只
	assert.Equal(t, day1Count.PetCareTypeCount[2], int32(1)) // BOARDING: 1只
	assert.Equal(t, day1Count.PetCountWithLodging, int32(1)) // 1只需要住宿

	// 验证第二天数据
	assert.Assert(t, day2Count != nil)
	assert.Equal(t, len(day2Count.PetCareTypeCount), 2)
	assert.Equal(t, day2Count.PetCareTypeCount[1], int32(1)) // GROOMING: 1只
	assert.Equal(t, day2Count.PetCareTypeCount[3], int32(1)) // 其他类型: 1只
	assert.Equal(t, day2Count.PetCountWithLodging, int32(1)) // 1只需要住宿
}

func TestAnalyzeFulfillmentData_CareTypeGrouping(t *testing.T) {
	// 测试按care type分组统计
	now := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:        1,
			PetID:     100,
			CareType:  1, // GROOMING
			LodgingID: 0,
			StartTime: now,
		},
		{
			ID:        2,
			PetID:     200,
			CareType:  1, // GROOMING - 同一类型
			LodgingID: 0,
			StartTime: now,
		},
		{
			ID:        3,
			PetID:     300,
			CareType:  2, // BOARDING
			LodgingID: 10,
			StartTime: now,
		},
		{
			ID:        4,
			PetID:     400,
			CareType:  3, // DAYCARE
			LodgingID: 0,
			StartTime: now,
		},
	}

	logic := &Logic{}
	dailyCounts, totalPetCount := logic.analyzeFulfillmentData(fulfillments)

	// 验证总宠物数量
	assert.Equal(t, totalPetCount, 4)

	// 验证每日统计数据
	assert.Equal(t, len(dailyCounts), 1)

	dailyCount := dailyCounts[0]
	assert.Equal(t, len(dailyCount.PetCareTypeCount), 3)
	assert.Equal(t, dailyCount.PetCareTypeCount[1], int32(2)) // GROOMING: 2只
	assert.Equal(t, dailyCount.PetCareTypeCount[2], int32(1)) // BOARDING: 1只
	assert.Equal(t, dailyCount.PetCareTypeCount[3], int32(1)) // DAYCARE: 1只
}

func TestAnalyzeFulfillmentData_LodgingPets(t *testing.T) {
	// 测试住宿宠物统计
	now := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:        1,
			PetID:     100,
			CareType:  1,
			LodgingID: 10, // 需要住宿
			StartTime: now,
		},
		{
			ID:        2,
			PetID:     200,
			CareType:  2,
			LodgingID: 20, // 需要住宿
			StartTime: now,
		},
		{
			ID:        3,
			PetID:     300,
			CareType:  1,
			LodgingID: 0, // 不需要住宿
			StartTime: now,
		},
		{
			ID:        4,
			PetID:     100, // 同一只宠物，多个住宿记录
			CareType:  1,
			LodgingID: 30,
			StartTime: now,
		},
	}

	logic := &Logic{}
	dailyCounts, totalPetCount := logic.analyzeFulfillmentData(fulfillments)

	// 验证总宠物数量
	assert.Equal(t, totalPetCount, 3)

	// 验证每日统计数据
	assert.Equal(t, len(dailyCounts), 1)

	dailyCount := dailyCounts[0]
	// 验证住宿宠物数量（去重后应该是2只：petID=100和200）
	assert.Equal(t, dailyCount.PetCountWithLodging, int32(2))
}

func TestAnalyzeFulfillmentData_PetDeduplication(t *testing.T) {
	// 测试宠物去重逻辑
	now := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:        1,
			PetID:     100,
			CareType:  1,
			LodgingID: 10,
			StartTime: now,
		},
		{
			ID:        2,
			PetID:     100, // 同一只宠物
			CareType:  2,
			LodgingID: 20,
			StartTime: now,
		},
		{
			ID:        3,
			PetID:     100, // 同一只宠物
			CareType:  1,
			LodgingID: 0,
			StartTime: now,
		},
		{
			ID:        4,
			PetID:     200,
			CareType:  1,
			LodgingID: 0,
			StartTime: now,
		},
	}

	logic := &Logic{}
	dailyCounts, totalPetCount := logic.analyzeFulfillmentData(fulfillments)

	// 验证总宠物数量（去重后应该是2只）
	assert.Equal(t, totalPetCount, 2)

	// 验证每日统计数据
	assert.Equal(t, len(dailyCounts), 1)

	dailyCount := dailyCounts[0]
	// 验证care type统计（同一只宠物在不同care type中应该分别计算）
	assert.Equal(t, len(dailyCount.PetCareTypeCount), 2)
	assert.Equal(t, dailyCount.PetCareTypeCount[1], int32(2)) // GROOMING: 2只宠物（100和200）
	assert.Equal(t, dailyCount.PetCareTypeCount[2], int32(1)) // BOARDING: 1只宠物（100）

	// 验证住宿宠物数量（去重后应该是1只：petID=100）
	assert.Equal(t, dailyCount.PetCountWithLodging, int32(1))
}

func TestAnalyzeFulfillmentData_ZeroPetID(t *testing.T) {
	// 测试PetID为0的情况（应该被忽略）
	now := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:        1,
			PetID:     0, // 无效的PetID
			CareType:  1,
			LodgingID: 10,
			StartTime: now,
		},
		{
			ID:        2,
			PetID:     100,
			CareType:  1,
			LodgingID: 0,
			StartTime: now,
		},
		{
			ID:        3,
			PetID:     0, // 无效的PetID
			CareType:  2,
			LodgingID: 20,
			StartTime: now,
		},
	}

	logic := &Logic{}
	dailyCounts, totalPetCount := logic.analyzeFulfillmentData(fulfillments)

	// 验证总宠物数量（只有PetID=100的应该被计算）
	assert.Equal(t, totalPetCount, 1)

	// 验证每日统计数据
	assert.Equal(t, len(dailyCounts), 1)

	dailyCount := dailyCounts[0]
	// 验证care type统计（只有有效的PetID应该被计算）
	assert.Equal(t, len(dailyCount.PetCareTypeCount), 1)
	assert.Equal(t, dailyCount.PetCareTypeCount[1], int32(1)) // 只有PetID=100

	// 验证住宿宠物数量（无效PetID不应该被计算）
	assert.Equal(t, dailyCount.PetCountWithLodging, int32(0))
}

func TestAnalyzeFulfillmentData_ZeroCareType(t *testing.T) {
	// 测试CareType为0的情况（应该被忽略）
	now := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:        1,
			PetID:     100,
			CareType:  0, // 无效的CareType
			LodgingID: 0,
			StartTime: now,
		},
		{
			ID:        2,
			PetID:     200,
			CareType:  1,
			LodgingID: 0,
			StartTime: now,
		},
	}

	logic := &Logic{}
	dailyCounts, totalPetCount := logic.analyzeFulfillmentData(fulfillments)

	// 验证总宠物数量（两只宠物都应该被计算）
	assert.Equal(t, totalPetCount, 2)

	// 验证每日统计数据
	assert.Equal(t, len(dailyCounts), 1)

	dailyCount := dailyCounts[0]
	// 验证care type统计（只有有效的CareType应该被计算）
	assert.Equal(t, len(dailyCount.PetCareTypeCount), 1)
	assert.Equal(t, dailyCount.PetCareTypeCount[1], int32(1)) // 只有CareType=1的
}

// ========== GetPetsByTimeRange 相关测试 ==========

func TestGetPetsByTimeRange_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := appointmentMock.NewMockReadWriter(ctrl)
	mockServiceInstanceCli := serviceinstanceMock.NewMockReadWriter(ctrl)
	mockPetCli := petMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli:     mockAppointmentCli,
		serviceInstanceCli: mockServiceInstanceCli,
		petCli:             mockPetCli,
	}

	req := &pb.GetPetsByTimeRangeRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)),
		EndTime:    timestamppb.New(time.Date(2024, 1, 16, 0, 0, 0, 0, time.UTC)),
	}

	// Mock appointments
	appointments := []*appointmentRepo.Appointment{
		{ID: 1, BusinessID: 456, CompanyID: 123},
		{ID: 2, BusinessID: 456, CompanyID: 123},
	}

	// Mock service instances
	serviceInstances := []*serviceinstanceRepo.ServiceInstance{
		{PetID: 100, AppointmentID: 1},
		{PetID: 200, AppointmentID: 1},
		{PetID: 100, AppointmentID: 2}, // 重复的宠物ID
	}

	// Mock pet info
	petInfos := []*businesscustomerpb.BusinessCustomerPetInfoModel{
		{
			Id:         100,
			PetName:    "Buddy",
			AvatarPath: "/avatar1.jpg",
			PetType:    customermodel.PetType_PET_TYPE_DOG,
		},
		{
			Id:         200,
			PetName:    "Kitty",
			AvatarPath: "/avatar2.jpg",
			PetType:    customermodel.PetType_PET_TYPE_CAT,
		},
	}

	mockAppointmentCli.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(appointments, nil)
	mockServiceInstanceCli.EXPECT().GetByAppointmentIDs(gomock.Any(), []int64{1, 2}).Return(serviceInstances, nil)
	mockPetCli.EXPECT().BatchGetPetInfo(gomock.Any(), int64(123), gomock.Any()).Return(petInfos, nil)

	resp, err := logic.GetPetsByTimeRange(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, len(resp.Pets), 2)
	assert.Equal(t, resp.Total, int32(2))

	// 验证返回的宠物信息
	petMap := make(map[int64]*pb.PetInfo)
	for _, pet := range resp.Pets {
		petMap[pet.Id] = pet
	}

	assert.Assert(t, petMap[100] != nil)
	assert.Equal(t, petMap[100].Name, "Buddy")
	assert.Equal(t, petMap[100].AvatarPath, "/avatar1.jpg")
	assert.Equal(t, petMap[100].Type, int32(customermodel.PetType_PET_TYPE_DOG))

	assert.Assert(t, petMap[200] != nil)
	assert.Equal(t, petMap[200].Name, "Kitty")
	assert.Equal(t, petMap[200].AvatarPath, "/avatar2.jpg")
	assert.Equal(t, petMap[200].Type, int32(customermodel.PetType_PET_TYPE_CAT))
}

func TestGetPetsByTimeRange_NoAppointments(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := appointmentMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli: mockAppointmentCli,
	}

	req := &pb.GetPetsByTimeRangeRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)),
		EndTime:    timestamppb.New(time.Date(2024, 1, 16, 0, 0, 0, 0, time.UTC)),
	}

	// Mock empty appointments
	mockAppointmentCli.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*appointmentRepo.Appointment{}, nil)

	resp, err := logic.GetPetsByTimeRange(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, len(resp.Pets), 0)
	assert.Equal(t, resp.Total, int32(0))
}

func TestGetPetsByTimeRange_NoServiceInstances(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := appointmentMock.NewMockReadWriter(ctrl)
	mockServiceInstanceCli := serviceinstanceMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli:     mockAppointmentCli,
		serviceInstanceCli: mockServiceInstanceCli,
	}

	req := &pb.GetPetsByTimeRangeRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)),
		EndTime:    timestamppb.New(time.Date(2024, 1, 16, 0, 0, 0, 0, time.UTC)),
	}

	appointments := []*appointmentRepo.Appointment{
		{ID: 1, BusinessID: 456, CompanyID: 123},
	}

	mockAppointmentCli.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(appointments, nil)
	mockServiceInstanceCli.EXPECT().GetByAppointmentIDs(gomock.Any(), []int64{1}).Return([]*serviceinstanceRepo.ServiceInstance{}, nil)

	resp, err := logic.GetPetsByTimeRange(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, len(resp.Pets), 0)
	assert.Equal(t, resp.Total, int32(0))
}

func TestGetPetsByTimeRange_AppointmentError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := appointmentMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli: mockAppointmentCli,
	}

	req := &pb.GetPetsByTimeRangeRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)),
		EndTime:    timestamppb.New(time.Date(2024, 1, 16, 0, 0, 0, 0, time.UTC)),
	}

	mockAppointmentCli.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("database error"))

	resp, err := logic.GetPetsByTimeRange(context.Background(), req)
	assert.Assert(t, err != nil)
	assert.Assert(t, resp == nil)
}

// ========== GetPetCountByTimeRange 相关测试 ==========

func TestGetPetCountByTimeRange_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := appointmentMock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli: mockAppointmentCli,
		fulfillmentCli: mockFulfillmentCli,
	}

	req := &pb.GetPetCountByTimeRangeRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)),
		EndTime:    timestamppb.New(time.Date(2024, 1, 16, 0, 0, 0, 0, time.UTC)),
	}

	// Mock appointments
	appointments := []*appointmentRepo.Appointment{
		{ID: 1, BusinessID: 456, CompanyID: 123},
		{ID: 2, BusinessID: 456, CompanyID: 123},
	}

	// Mock fulfillments
	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:        1,
			PetID:     100,
			CareType:  1,
			LodgingID: 10,
			StartTime: time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC),
		},
		{
			ID:        2,
			PetID:     200,
			CareType:  2,
			LodgingID: 0,
			StartTime: time.Date(2024, 1, 15, 11, 0, 0, 0, time.UTC),
		},
		{
			ID:        3,
			PetID:     100, // 同一只宠物
			CareType:  1,
			LodgingID: 20,
			StartTime: time.Date(2024, 1, 16, 10, 0, 0, 0, time.UTC),
		},
	}

	mockAppointmentCli.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(appointments, nil)
	mockFulfillmentCli.EXPECT().GetByAppointmentIDs(gomock.Any(), []int64{1, 2}).Return(fulfillments, nil)

	resp, err := logic.GetPetCountByTimeRange(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, resp.TotalPetCount, int32(2)) // 去重后2只宠物
	assert.Equal(t, len(resp.DailyPetCounts), 2)  // 2天数据

	// 验证每日统计
	dailyMap := make(map[string]*pb.DailyPetCount)
	for _, daily := range resp.DailyPetCounts {
		dailyMap[daily.Date] = daily
	}

	// 验证第一天
	day1 := dailyMap["2024-01-15"]
	assert.Assert(t, day1 != nil)
	assert.Equal(t, len(day1.PetCareTypeCount), 2)
	assert.Equal(t, day1.PetCareTypeCount[1], int32(1)) // GROOMING: 1只
	assert.Equal(t, day1.PetCareTypeCount[2], int32(1)) // BOARDING: 1只
	assert.Equal(t, day1.PetCountWithLodging, int32(1)) // 1只需要住宿

	// 验证第二天
	day2 := dailyMap["2024-01-16"]
	assert.Assert(t, day2 != nil)
	assert.Equal(t, len(day2.PetCareTypeCount), 1)
	assert.Equal(t, day2.PetCareTypeCount[1], int32(1)) // GROOMING: 1只
	assert.Equal(t, day2.PetCountWithLodging, int32(1)) // 1只需要住宿
}

func TestGetPetCountByTimeRange_NoAppointments(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := appointmentMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli: mockAppointmentCli,
	}

	req := &pb.GetPetCountByTimeRangeRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)),
		EndTime:    timestamppb.New(time.Date(2024, 1, 16, 0, 0, 0, 0, time.UTC)),
	}

	mockAppointmentCli.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*appointmentRepo.Appointment{}, nil)

	resp, err := logic.GetPetCountByTimeRange(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, len(resp.DailyPetCounts), 0)
	assert.Equal(t, resp.TotalPetCount, int32(0))
}

func TestGetPetCountByTimeRange_NoFulfillments(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := appointmentMock.NewMockReadWriter(ctrl)
	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli: mockAppointmentCli,
		fulfillmentCli: mockFulfillmentCli,
	}

	req := &pb.GetPetCountByTimeRangeRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)),
		EndTime:    timestamppb.New(time.Date(2024, 1, 16, 0, 0, 0, 0, time.UTC)),
	}

	appointments := []*appointmentRepo.Appointment{
		{ID: 1, BusinessID: 456, CompanyID: 123},
	}

	mockAppointmentCli.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(appointments, nil)
	mockFulfillmentCli.EXPECT().GetByAppointmentIDs(gomock.Any(), []int64{1}).Return([]*fulfillment.Fulfillment{}, nil)

	resp, err := logic.GetPetCountByTimeRange(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, len(resp.DailyPetCounts), 0)
	assert.Equal(t, resp.TotalPetCount, int32(0))
}

// ========== 辅助函数测试 ==========

func TestGetAppointmentIDsByTimeRange(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppointmentCli := appointmentMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		appointmentCli: mockAppointmentCli,
	}

	req := &pb.GetPetsByTimeRangeRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)),
		EndTime:    timestamppb.New(time.Date(2024, 1, 16, 0, 0, 0, 0, time.UTC)),
	}

	appointments := []*appointmentRepo.Appointment{
		{ID: 1, BusinessID: 456, CompanyID: 123},
		{ID: 2, BusinessID: 456, CompanyID: 123},
		{ID: 3, BusinessID: 456, CompanyID: 123},
	}

	mockAppointmentCli.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(appointments, nil)

	appointmentIDs, err := logic.getAppointmentIDsByTimeRange(context.Background(), req)
	assert.NilError(t, err)
	assert.Equal(t, len(appointmentIDs), 3)
	assert.DeepEqual(t, appointmentIDs, []int64{1, 2, 3})
}

func TestGetPetIDsFromAppointments(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockServiceInstanceCli := serviceinstanceMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		serviceInstanceCli: mockServiceInstanceCli,
	}

	appointmentIDs := []int64{1, 2}

	serviceInstances := []*serviceinstanceRepo.ServiceInstance{
		{PetID: 100, AppointmentID: 1},
		{PetID: 200, AppointmentID: 1},
		{PetID: 100, AppointmentID: 2}, // 重复的宠物ID
		{PetID: 0, AppointmentID: 2},   // 无效的宠物ID
	}

	mockServiceInstanceCli.EXPECT().GetByAppointmentIDs(gomock.Any(), appointmentIDs).Return(serviceInstances, nil)

	petIDs, err := logic.getPetIDsFromAppointments(context.Background(), appointmentIDs)
	assert.NilError(t, err)
	assert.Equal(t, len(petIDs), 2)
	assert.DeepEqual(t, petIDs, []int64{100, 200})
}

func TestGetPetIDsFromAppointments_Empty(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logic := &Logic{}

	petIDs, err := logic.getPetIDsFromAppointments(context.Background(), []int64{})
	assert.NilError(t, err)
	assert.Equal(t, len(petIDs), 0)
}

func TestGetPetInfos(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockPetCli := petMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		petCli: mockPetCli,
	}

	companyID := int64(123)
	petIDs := []int64{100, 200}

	petInfos := []*businesscustomerpb.BusinessCustomerPetInfoModel{
		{
			Id:         100,
			PetName:    "Buddy",
			AvatarPath: "/avatar1.jpg",
			PetType:    customermodel.PetType_PET_TYPE_DOG,
		},
		{
			Id:         200,
			PetName:    "Kitty",
			AvatarPath: "/avatar2.jpg",
			PetType:    customermodel.PetType_PET_TYPE_CAT,
		},
	}

	mockPetCli.EXPECT().BatchGetPetInfo(gomock.Any(), companyID, petIDs).Return(petInfos, nil)

	result, err := logic.getPetInfos(context.Background(), companyID, petIDs)
	assert.NilError(t, err)
	assert.Equal(t, len(result), 2)

	// 验证转换结果
	petMap := make(map[int64]*pb.PetInfo)
	for _, pet := range result {
		petMap[pet.Id] = pet
	}

	assert.Assert(t, petMap[100] != nil)
	assert.Equal(t, petMap[100].Name, "Buddy")
	assert.Equal(t, petMap[100].AvatarPath, "/avatar1.jpg")
	assert.Equal(t, petMap[100].Type, int32(customermodel.PetType_PET_TYPE_DOG))

	assert.Assert(t, petMap[200] != nil)
	assert.Equal(t, petMap[200].Name, "Kitty")
	assert.Equal(t, petMap[200].AvatarPath, "/avatar2.jpg")
	assert.Equal(t, petMap[200].Type, int32(customermodel.PetType_PET_TYPE_CAT))
}

func TestGetPetInfos_Empty(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logic := &Logic{}

	result, err := logic.getPetInfos(context.Background(), 123, []int64{})
	assert.NilError(t, err)
	assert.Equal(t, len(result), 0)
}

func TestGetFulfillmentsByAppointmentIDs(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockFulfillmentCli := fulfillmentMock.NewMockReadWriter(ctrl)

	logic := &Logic{
		fulfillmentCli: mockFulfillmentCli,
	}

	appointmentIDs := []int64{1, 2}

	fulfillments := []*fulfillment.Fulfillment{
		{ID: 1, PetID: 100, CareType: 1, LodgingID: 10},
		{ID: 2, PetID: 200, CareType: 2, LodgingID: 0},
	}

	mockFulfillmentCli.EXPECT().GetByAppointmentIDs(gomock.Any(), appointmentIDs).Return(fulfillments, nil)

	result, err := logic.getFulfillmentsByAppointmentIDs(context.Background(), appointmentIDs)
	assert.NilError(t, err)
	assert.Equal(t, len(result), 2)
	assert.DeepEqual(t, result, fulfillments)
}

func TestGetFulfillmentsByAppointmentIDs_Empty(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logic := &Logic{}

	result, err := logic.getFulfillmentsByAppointmentIDs(context.Background(), []int64{})
	assert.NilError(t, err)
	assert.Equal(t, len(result), 0)
}
