// gorm/entity.go 包含了该repo层内会用到的所有实体, 比如数据库的表结构
// 请注意, 为了可迭代和可维护, 即使logic entity 和 repo entity 是同一个实体, 也请分开定义
package fulfillment

import "time"

// 列名常量定义，避免手搓列名
const (
	ColumnBusinessID        = "business_id"
	ColumnCompanyID         = "company_id"
	ColumnCustomerID        = "customer_id"
	ColumnPetID             = "pet_id"
	ColumnAppointmentID     = "appointment_id"
	ColumnCareType          = "care_type"
	ColumnStaffID           = "staff_id"
	ColumnStartTime         = "start_time"
	ColumnEndTime           = "end_time"
	ColumnCreateTime        = "create_time"
	ColumnDepth             = "depth"
	ColumnServiceInstanceID = "service_instance_id"
	ColumnServiceFactoryID  = "service_factory_id"
	ColumnLodgingID         = "lodging_id"
	ColumnPlaygroupID       = "playgroup_id"
	ColumnState             = "state"
	ColumnSource            = "source"
	ColumnID                = "id"
)

type BaseParam struct {
	BusinessID     int32
	CompanyID      int32
	PaginationInfo *PaginationInfo
	StartTime      time.Time
	EndTime        time.Time
}

type Filter struct {
	PetIDs      []int64
	StaffID     int64
	States      []int32
	CustomerIDs []int64
	CareTypes   []int32
}

type PaginationInfo struct {
	Offset int32
	Limit  int32
}

const fulfillmentTableName = "fulfillment_detail"

type Fulfillment struct {
	ID                   int64     `json:"id" gorm:"column:id"`
	BusinessID           int64     `json:"business_id" gorm:"column:business_id"`
	CompanyID            int64     `json:"company_id" gorm:"column:company_id"`
	CustomerID           int64     `json:"customer_id" gorm:"column:customer_id"`
	PetID                int64     `json:"pet_id" gorm:"column:pet_id"`
	AppointmentID        int64     `json:"appointment_id" gorm:"column:appointment_id"`
	ServiceInstanceID    int64     `json:"service_instance_id" gorm:"column:service_instance_id"`
	ServiceFactoryID     int64     `json:"service_factory_id" gorm:"column:service_factory_id"`
	LodgingID            int64     `json:"lodging_id" gorm:"column:lodging_id"`
	PlaygroupID          int64     `json:"playgroup_id" gorm:"column:playgroup_id"`
	StaffID              int64     `json:"staff_id" gorm:"column:staff_id"`
	StartTime            time.Time `json:"start_time" gorm:"column:start_time"`
	EndTime              time.Time `json:"end_time" gorm:"column:end_time"`
	CareType             int32     `json:"care_type" gorm:"column:care_type"`
	State                int32     `json:"state" gorm:"column:state"`
	Source               int32     `json:"source" gorm:"column:source"`
	CreatedAt            time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt            time.Time `json:"updated_at" gorm:"column:updated_at"`
	WorkMode             int32     `json:"work_mode" gorm:"column:work_mode"`
	DurationOverrideType int32     `json:"duration_override_type" gorm:"column:duration_override_type"`
	PriceOverrideType    int32     `json:"price_override_type" gorm:"column:price_override_type"`
}

// TableName 表名
func (m *Fulfillment) TableName() string {
	return fulfillmentTableName
}
