// Code generated by MockGen. DO NOT EDIT.
// Source: ./service_instance.go
//
// Generated by this command:
//
//	mockgen -source=./service_instance.go -destination=./mock/service_instance_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	serviceinstance "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockReadWriter) BatchCreate(ctx context.Context, serviceInstances []*serviceinstance.ServiceInstance) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, serviceInstances)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockReadWriterMockRecorder) BatchCreate(ctx, serviceInstances any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockReadWriter)(nil).BatchCreate), ctx, serviceInstances)
}

// Create mocks base method.
func (m *MockReadWriter) Create(ctx context.Context, si *serviceinstance.ServiceInstance) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, si)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockReadWriterMockRecorder) Create(ctx, si any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockReadWriter)(nil).Create), ctx, si)
}

// Delete mocks base method.
func (m *MockReadWriter) Delete(ctx context.Context, id int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockReadWriterMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockReadWriter)(nil).Delete), ctx, id)
}

// GetByAppointmentID mocks base method.
func (m *MockReadWriter) GetByAppointmentID(ctx context.Context, appointmentID int) ([]*serviceinstance.ServiceInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAppointmentID", ctx, appointmentID)
	ret0, _ := ret[0].([]*serviceinstance.ServiceInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAppointmentID indicates an expected call of GetByAppointmentID.
func (mr *MockReadWriterMockRecorder) GetByAppointmentID(ctx, appointmentID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAppointmentID", reflect.TypeOf((*MockReadWriter)(nil).GetByAppointmentID), ctx, appointmentID)
}

// GetByAppointmentIDs mocks base method.
func (m *MockReadWriter) GetByAppointmentIDs(ctx context.Context, appointmentIDs []int64) ([]*serviceinstance.ServiceInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAppointmentIDs", ctx, appointmentIDs)
	ret0, _ := ret[0].([]*serviceinstance.ServiceInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAppointmentIDs indicates an expected call of GetByAppointmentIDs.
func (mr *MockReadWriterMockRecorder) GetByAppointmentIDs(ctx, appointmentIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAppointmentIDs", reflect.TypeOf((*MockReadWriter)(nil).GetByAppointmentIDs), ctx, appointmentIDs)
}

// GetByID mocks base method.
func (m *MockReadWriter) GetByID(ctx context.Context, id int) (*serviceinstance.ServiceInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*serviceinstance.ServiceInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockReadWriterMockRecorder) GetByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockReadWriter)(nil).GetByID), ctx, id)
}

// GetByIDs mocks base method.
func (m *MockReadWriter) GetByIDs(ctx context.Context, ids []int64) ([]*serviceinstance.ServiceInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIDs", ctx, ids)
	ret0, _ := ret[0].([]*serviceinstance.ServiceInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIDs indicates an expected call of GetByIDs.
func (mr *MockReadWriterMockRecorder) GetByIDs(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIDs", reflect.TypeOf((*MockReadWriter)(nil).GetByIDs), ctx, ids)
}

// List mocks base method.
func (m *MockReadWriter) List(ctx context.Context, baseParam *serviceinstance.BaseParam, filter *serviceinstance.Filter) ([]*serviceinstance.ServiceInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, baseParam, filter)
	ret0, _ := ret[0].([]*serviceinstance.ServiceInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockReadWriterMockRecorder) List(ctx, baseParam, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockReadWriter)(nil).List), ctx, baseParam, filter)
}

// Update mocks base method.
func (m *MockReadWriter) Update(ctx context.Context, si *serviceinstance.ServiceInstance) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, si)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockReadWriterMockRecorder) Update(ctx, si any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockReadWriter)(nil).Update), ctx, si)
}
