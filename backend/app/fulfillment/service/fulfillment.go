package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/fulfillment"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

type FulfillmentService struct {
	fulfillment                              *fulfillment.Logic
	pb.UnimplementedFulfillmentServiceServer // TODO: 实现所有接口后删除
}

func NewFulfillmentService() *FulfillmentService {
	return &FulfillmentService{
		fulfillment: fulfillment.New(),
	}
}

func (a *FulfillmentService) ListFulfillment(ctx context.Context,
	req *pb.ListFulfillmentRequest) (*pb.ListFulfillmentResponse, error) {
	return a.fulfillment.ListFulfillment(ctx, req)
}

func (a *FulfillmentService) GetPetsByTimeRange(ctx context.Context,
	req *pb.GetPetsByTimeRangeRequest) (*pb.GetPetsByTimeRangeResponse, error) {
	return a.fulfillment.GetPetsByTimeRange(ctx, req)
}

func (a *FulfillmentService) GetPetCountByTimeRange(ctx context.Context,
	req *pb.GetPetCountByTimeRangeRequest) (*pb.GetPetCountByTimeRangeResponse, error) {
	return a.fulfillment.GetPetCountByTimeRange(ctx, req)
}
