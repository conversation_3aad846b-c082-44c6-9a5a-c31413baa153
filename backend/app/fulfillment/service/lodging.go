package service

import (
	"context"

	calendarpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

type CalendarService struct {
	calendarpb.UnimplementedCalendarServiceServer
}

func NewCalendarService() *CalendarService {
	return &CalendarService{}
}

func (s *CalendarService) GetLodgingCalendar(_ context.Context,
	_ *calendarpb.GetLodgingCalendarRequest) (*calendarpb.GetLodgingCalendarResponse, error) {
	return &calendarpb.GetLodgingCalendarResponse{}, nil
}
