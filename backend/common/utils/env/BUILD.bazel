load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "env",
    srcs = ["env.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/utils/env",
    visibility = ["//visibility:public"],
    deps = ["//backend/common/rpc/framework/log"],
)

go_test(
    name = "env_test",
    srcs = ["env_test.go"],
    embed = [":env"],
    deps = ["@com_github_stretchr_testify//assert"],
)
