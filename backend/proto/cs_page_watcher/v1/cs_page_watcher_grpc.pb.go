// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0191::java-package=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0191::java-multiple-files=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/cs_page_watcher/v1/cs_page_watcher.proto

package cs_page_watcher

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CSPageWatcherService_TriggerPage_FullMethodName      = "/backend.proto.cs_page_watcher.v1.CSPageWatcherService/TriggerPage"
	CSPageWatcherService_TriggerAdminTask_FullMethodName = "/backend.proto.cs_page_watcher.v1.CSPageWatcherService/TriggerAdminTask"
	CSPageWatcherService_CompleteIncident_FullMethodName = "/backend.proto.cs_page_watcher.v1.CSPageWatcherService/CompleteIncident"
	CSPageWatcherService_CompleteJira_FullMethodName     = "/backend.proto.cs_page_watcher.v1.CSPageWatcherService/CompleteJira"
	CSPageWatcherService_RunTask_FullMethodName          = "/backend.proto.cs_page_watcher.v1.CSPageWatcherService/RunTask"
)

// CSPageWatcherServiceClient is the client API for CSPageWatcherService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// CSPageWatcherService 定义了 CSPageWatcher 服务的接口。
type CSPageWatcherServiceClient interface {
	// TriggerPage start a cs page from jira ticket
	TriggerPage(ctx context.Context, in *TriggerPageRequest, opts ...grpc.CallOption) (*DefaultResponse, error)
	// TriggerAdminTask assigns jira ticket to appropriate commander
	TriggerAdminTask(ctx context.Context, in *TriggerAdminTaskRequest, opts ...grpc.CallOption) (*DefaultResponse, error)
	// CompleteIncident ...
	CompleteIncident(ctx context.Context, in *CompleteIncidentRequest, opts ...grpc.CallOption) (*DefaultResponse, error)
	// CompleteJira ...
	CompleteJira(ctx context.Context, in *CompleteJiraRequest, opts ...grpc.CallOption) (*DefaultResponse, error)
	// RunTask executes a specific task by name
	RunTask(ctx context.Context, in *RunTaskRequest, opts ...grpc.CallOption) (*DefaultResponse, error)
}

type cSPageWatcherServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCSPageWatcherServiceClient(cc grpc.ClientConnInterface) CSPageWatcherServiceClient {
	return &cSPageWatcherServiceClient{cc}
}

func (c *cSPageWatcherServiceClient) TriggerPage(ctx context.Context, in *TriggerPageRequest, opts ...grpc.CallOption) (*DefaultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DefaultResponse)
	err := c.cc.Invoke(ctx, CSPageWatcherService_TriggerPage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cSPageWatcherServiceClient) TriggerAdminTask(ctx context.Context, in *TriggerAdminTaskRequest, opts ...grpc.CallOption) (*DefaultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DefaultResponse)
	err := c.cc.Invoke(ctx, CSPageWatcherService_TriggerAdminTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cSPageWatcherServiceClient) CompleteIncident(ctx context.Context, in *CompleteIncidentRequest, opts ...grpc.CallOption) (*DefaultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DefaultResponse)
	err := c.cc.Invoke(ctx, CSPageWatcherService_CompleteIncident_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cSPageWatcherServiceClient) CompleteJira(ctx context.Context, in *CompleteJiraRequest, opts ...grpc.CallOption) (*DefaultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DefaultResponse)
	err := c.cc.Invoke(ctx, CSPageWatcherService_CompleteJira_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cSPageWatcherServiceClient) RunTask(ctx context.Context, in *RunTaskRequest, opts ...grpc.CallOption) (*DefaultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DefaultResponse)
	err := c.cc.Invoke(ctx, CSPageWatcherService_RunTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CSPageWatcherServiceServer is the server API for CSPageWatcherService service.
// All implementations must embed UnimplementedCSPageWatcherServiceServer
// for forward compatibility.
//
// CSPageWatcherService 定义了 CSPageWatcher 服务的接口。
type CSPageWatcherServiceServer interface {
	// TriggerPage start a cs page from jira ticket
	TriggerPage(context.Context, *TriggerPageRequest) (*DefaultResponse, error)
	// TriggerAdminTask assigns jira ticket to appropriate commander
	TriggerAdminTask(context.Context, *TriggerAdminTaskRequest) (*DefaultResponse, error)
	// CompleteIncident ...
	CompleteIncident(context.Context, *CompleteIncidentRequest) (*DefaultResponse, error)
	// CompleteJira ...
	CompleteJira(context.Context, *CompleteJiraRequest) (*DefaultResponse, error)
	// RunTask executes a specific task by name
	RunTask(context.Context, *RunTaskRequest) (*DefaultResponse, error)
	mustEmbedUnimplementedCSPageWatcherServiceServer()
}

// UnimplementedCSPageWatcherServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCSPageWatcherServiceServer struct{}

func (UnimplementedCSPageWatcherServiceServer) TriggerPage(context.Context, *TriggerPageRequest) (*DefaultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerPage not implemented")
}
func (UnimplementedCSPageWatcherServiceServer) TriggerAdminTask(context.Context, *TriggerAdminTaskRequest) (*DefaultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerAdminTask not implemented")
}
func (UnimplementedCSPageWatcherServiceServer) CompleteIncident(context.Context, *CompleteIncidentRequest) (*DefaultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompleteIncident not implemented")
}
func (UnimplementedCSPageWatcherServiceServer) CompleteJira(context.Context, *CompleteJiraRequest) (*DefaultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompleteJira not implemented")
}
func (UnimplementedCSPageWatcherServiceServer) RunTask(context.Context, *RunTaskRequest) (*DefaultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunTask not implemented")
}
func (UnimplementedCSPageWatcherServiceServer) mustEmbedUnimplementedCSPageWatcherServiceServer() {}
func (UnimplementedCSPageWatcherServiceServer) testEmbeddedByValue()                              {}

// UnsafeCSPageWatcherServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CSPageWatcherServiceServer will
// result in compilation errors.
type UnsafeCSPageWatcherServiceServer interface {
	mustEmbedUnimplementedCSPageWatcherServiceServer()
}

func RegisterCSPageWatcherServiceServer(s grpc.ServiceRegistrar, srv CSPageWatcherServiceServer) {
	// If the following call pancis, it indicates UnimplementedCSPageWatcherServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CSPageWatcherService_ServiceDesc, srv)
}

func _CSPageWatcherService_TriggerPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CSPageWatcherServiceServer).TriggerPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CSPageWatcherService_TriggerPage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CSPageWatcherServiceServer).TriggerPage(ctx, req.(*TriggerPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CSPageWatcherService_TriggerAdminTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerAdminTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CSPageWatcherServiceServer).TriggerAdminTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CSPageWatcherService_TriggerAdminTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CSPageWatcherServiceServer).TriggerAdminTask(ctx, req.(*TriggerAdminTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CSPageWatcherService_CompleteIncident_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompleteIncidentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CSPageWatcherServiceServer).CompleteIncident(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CSPageWatcherService_CompleteIncident_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CSPageWatcherServiceServer).CompleteIncident(ctx, req.(*CompleteIncidentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CSPageWatcherService_CompleteJira_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompleteJiraRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CSPageWatcherServiceServer).CompleteJira(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CSPageWatcherService_CompleteJira_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CSPageWatcherServiceServer).CompleteJira(ctx, req.(*CompleteJiraRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CSPageWatcherService_RunTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CSPageWatcherServiceServer).RunTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CSPageWatcherService_RunTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CSPageWatcherServiceServer).RunTask(ctx, req.(*RunTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CSPageWatcherService_ServiceDesc is the grpc.ServiceDesc for CSPageWatcherService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CSPageWatcherService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.cs_page_watcher.v1.CSPageWatcherService",
	HandlerType: (*CSPageWatcherServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TriggerPage",
			Handler:    _CSPageWatcherService_TriggerPage_Handler,
		},
		{
			MethodName: "TriggerAdminTask",
			Handler:    _CSPageWatcherService_TriggerAdminTask_Handler,
		},
		{
			MethodName: "CompleteIncident",
			Handler:    _CSPageWatcherService_CompleteIncident_Handler,
		},
		{
			MethodName: "CompleteJira",
			Handler:    _CSPageWatcherService_CompleteJira_Handler,
		},
		{
			MethodName: "RunTask",
			Handler:    _CSPageWatcherService_RunTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/cs_page_watcher/v1/cs_page_watcher.proto",
}
