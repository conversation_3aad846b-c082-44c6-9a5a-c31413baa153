// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 平铺使用 --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 平铺使用 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 平铺使用 --)
// (-- api-linter:  core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 平铺使用 --)
// (-- api-linter:  core::0134::synonyms=disabled
//     aip.dev/not-precedent: 使用Set表达Upsert语义 --)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/customer_portal/v1/customer_portal_service.proto

package customerportalpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CustomerPortalService_SetOnlineBookingScript_FullMethodName = "/backend.proto.customer_portal.v1.CustomerPortalService/SetOnlineBookingScript"
	CustomerPortalService_GetOnlineBookingScript_FullMethodName = "/backend.proto.customer_portal.v1.CustomerPortalService/GetOnlineBookingScript"
)

// CustomerPortalServiceClient is the client API for CustomerPortalService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// CustomerPortalService
type CustomerPortalServiceClient interface {
	// SetOnlineBookingScript 设置OB CSS&JS 脚本
	SetOnlineBookingScript(ctx context.Context, in *SetOnlineBookingScriptRequest, opts ...grpc.CallOption) (*SetOnlineBookingScriptResponse, error)
	// GetOnlineBookingScript 获取OB CSS&JS 脚本
	GetOnlineBookingScript(ctx context.Context, in *GetOnlineBookingScriptRequest, opts ...grpc.CallOption) (*GetOnlineBookingScriptResponse, error)
}

type customerPortalServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCustomerPortalServiceClient(cc grpc.ClientConnInterface) CustomerPortalServiceClient {
	return &customerPortalServiceClient{cc}
}

func (c *customerPortalServiceClient) SetOnlineBookingScript(ctx context.Context, in *SetOnlineBookingScriptRequest, opts ...grpc.CallOption) (*SetOnlineBookingScriptResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetOnlineBookingScriptResponse)
	err := c.cc.Invoke(ctx, CustomerPortalService_SetOnlineBookingScript_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerPortalServiceClient) GetOnlineBookingScript(ctx context.Context, in *GetOnlineBookingScriptRequest, opts ...grpc.CallOption) (*GetOnlineBookingScriptResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOnlineBookingScriptResponse)
	err := c.cc.Invoke(ctx, CustomerPortalService_GetOnlineBookingScript_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CustomerPortalServiceServer is the server API for CustomerPortalService service.
// All implementations must embed UnimplementedCustomerPortalServiceServer
// for forward compatibility.
//
// CustomerPortalService
type CustomerPortalServiceServer interface {
	// SetOnlineBookingScript 设置OB CSS&JS 脚本
	SetOnlineBookingScript(context.Context, *SetOnlineBookingScriptRequest) (*SetOnlineBookingScriptResponse, error)
	// GetOnlineBookingScript 获取OB CSS&JS 脚本
	GetOnlineBookingScript(context.Context, *GetOnlineBookingScriptRequest) (*GetOnlineBookingScriptResponse, error)
	mustEmbedUnimplementedCustomerPortalServiceServer()
}

// UnimplementedCustomerPortalServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCustomerPortalServiceServer struct{}

func (UnimplementedCustomerPortalServiceServer) SetOnlineBookingScript(context.Context, *SetOnlineBookingScriptRequest) (*SetOnlineBookingScriptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetOnlineBookingScript not implemented")
}
func (UnimplementedCustomerPortalServiceServer) GetOnlineBookingScript(context.Context, *GetOnlineBookingScriptRequest) (*GetOnlineBookingScriptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnlineBookingScript not implemented")
}
func (UnimplementedCustomerPortalServiceServer) mustEmbedUnimplementedCustomerPortalServiceServer() {}
func (UnimplementedCustomerPortalServiceServer) testEmbeddedByValue()                               {}

// UnsafeCustomerPortalServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CustomerPortalServiceServer will
// result in compilation errors.
type UnsafeCustomerPortalServiceServer interface {
	mustEmbedUnimplementedCustomerPortalServiceServer()
}

func RegisterCustomerPortalServiceServer(s grpc.ServiceRegistrar, srv CustomerPortalServiceServer) {
	// If the following call pancis, it indicates UnimplementedCustomerPortalServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CustomerPortalService_ServiceDesc, srv)
}

func _CustomerPortalService_SetOnlineBookingScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetOnlineBookingScriptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerPortalServiceServer).SetOnlineBookingScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerPortalService_SetOnlineBookingScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerPortalServiceServer).SetOnlineBookingScript(ctx, req.(*SetOnlineBookingScriptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerPortalService_GetOnlineBookingScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineBookingScriptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerPortalServiceServer).GetOnlineBookingScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerPortalService_GetOnlineBookingScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerPortalServiceServer).GetOnlineBookingScript(ctx, req.(*GetOnlineBookingScriptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CustomerPortalService_ServiceDesc is the grpc.ServiceDesc for CustomerPortalService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CustomerPortalService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.customer_portal.v1.CustomerPortalService",
	HandlerType: (*CustomerPortalServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetOnlineBookingScript",
			Handler:    _CustomerPortalService_SetOnlineBookingScript_Handler,
		},
		{
			MethodName: "GetOnlineBookingScript",
			Handler:    _CustomerPortalService_GetOnlineBookingScript_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/customer_portal/v1/customer_portal_service.proto",
}
