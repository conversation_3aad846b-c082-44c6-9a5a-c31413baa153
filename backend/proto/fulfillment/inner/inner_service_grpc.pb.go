// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/fulfillment/inner/inner_service.proto

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: This is a legacy inner service package that doesn't follow versioned package naming convention. --)

package fulfillmentinnerpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	InnerService_GetGroomingDetailByAppointmentId_FullMethodName = "/backend.proto.fulfillment.inner.InnerService/GetGroomingDetailByAppointmentId"
)

// InnerServiceClient is the client API for InnerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 内部服务接口，用于处理fulfillment模块内部的业务逻辑
// (-- api-linter: core::0191::file-layout=disabled
//
//	aip.dev/not-precedent: Legacy service layout maintained for backward compatibility. --)
type InnerServiceClient interface {
	// 通过预约ID获取美容详情信息，包括宠物详情、服务操作等相关数据
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	api-linter: core::0131::response-message-name=disabled
	//	aip.dev/not-precedent: Legacy method naming convention maintained for backward compatibility. --)
	GetGroomingDetailByAppointmentId(ctx context.Context, in *GetGroomingDetailByAppointmentIdRequest, opts ...grpc.CallOption) (*GetGroomingDetailByAppointmentIdResponse, error)
}

type innerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewInnerServiceClient(cc grpc.ClientConnInterface) InnerServiceClient {
	return &innerServiceClient{cc}
}

func (c *innerServiceClient) GetGroomingDetailByAppointmentId(ctx context.Context, in *GetGroomingDetailByAppointmentIdRequest, opts ...grpc.CallOption) (*GetGroomingDetailByAppointmentIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGroomingDetailByAppointmentIdResponse)
	err := c.cc.Invoke(ctx, InnerService_GetGroomingDetailByAppointmentId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InnerServiceServer is the server API for InnerService service.
// All implementations must embed UnimplementedInnerServiceServer
// for forward compatibility.
//
// 内部服务接口，用于处理fulfillment模块内部的业务逻辑
// (-- api-linter: core::0191::file-layout=disabled
//
//	aip.dev/not-precedent: Legacy service layout maintained for backward compatibility. --)
type InnerServiceServer interface {
	// 通过预约ID获取美容详情信息，包括宠物详情、服务操作等相关数据
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	api-linter: core::0131::response-message-name=disabled
	//	aip.dev/not-precedent: Legacy method naming convention maintained for backward compatibility. --)
	GetGroomingDetailByAppointmentId(context.Context, *GetGroomingDetailByAppointmentIdRequest) (*GetGroomingDetailByAppointmentIdResponse, error)
	mustEmbedUnimplementedInnerServiceServer()
}

// UnimplementedInnerServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedInnerServiceServer struct{}

func (UnimplementedInnerServiceServer) GetGroomingDetailByAppointmentId(context.Context, *GetGroomingDetailByAppointmentIdRequest) (*GetGroomingDetailByAppointmentIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroomingDetailByAppointmentId not implemented")
}
func (UnimplementedInnerServiceServer) mustEmbedUnimplementedInnerServiceServer() {}
func (UnimplementedInnerServiceServer) testEmbeddedByValue()                      {}

// UnsafeInnerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to InnerServiceServer will
// result in compilation errors.
type UnsafeInnerServiceServer interface {
	mustEmbedUnimplementedInnerServiceServer()
}

func RegisterInnerServiceServer(s grpc.ServiceRegistrar, srv InnerServiceServer) {
	// If the following call pancis, it indicates UnimplementedInnerServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&InnerService_ServiceDesc, srv)
}

func _InnerService_GetGroomingDetailByAppointmentId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroomingDetailByAppointmentIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InnerServiceServer).GetGroomingDetailByAppointmentId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InnerService_GetGroomingDetailByAppointmentId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InnerServiceServer).GetGroomingDetailByAppointmentId(ctx, req.(*GetGroomingDetailByAppointmentIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// InnerService_ServiceDesc is the grpc.ServiceDesc for InnerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var InnerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.fulfillment.inner.InnerService",
	HandlerType: (*InnerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGroomingDetailByAppointmentId",
			Handler:    _InnerService_GetGroomingDetailByAppointmentId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/fulfillment/inner/inner_service.proto",
}
