// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/calendar.proto

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: lodging package structure is appropriate for this domain --)

package fulfillmentpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// LodgingInfo 住宿信息
type LodgingInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 住宿ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 住宿类型
	UnitType int32 `protobuf:"varint,2,opt,name=unit_type,json=unitType,proto3" json:"unit_type,omitempty"`
	// 住宿单元
	Units []*LodgingUnit `protobuf:"bytes,3,rep,name=units,proto3" json:"units,omitempty"`
	// 住宿名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// 最大宠物数量
	MaxPetNumber int32 `protobuf:"varint,5,opt,name=max_pet_number,json=maxPetNumber,proto3" json:"max_pet_number,omitempty"`
	// 最大宠物总数
	MaxPetTotalNumber int32 `protobuf:"varint,6,opt,name=max_pet_total_number,json=maxPetTotalNumber,proto3" json:"max_pet_total_number,omitempty"`
	// 每日容量使用情况
	DailyCapacityUsed map[string]int32 `protobuf:"bytes,7,rep,name=daily_capacity_used,json=dailyCapacityUsed,proto3" json:"daily_capacity_used,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// 是否需要宠物大小过滤
	NeedPetSizeFilter bool `protobuf:"varint,8,opt,name=need_pet_size_filter,json=needPetSizeFilter,proto3" json:"need_pet_size_filter,omitempty"`
	// 宠物大小ID
	PetSizeIds []int64 `protobuf:"varint,9,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// 总容量
	TotalCapacity int32 `protobuf:"varint,10,opt,name=total_capacity,json=totalCapacity,proto3" json:"total_capacity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LodgingInfo) Reset() {
	*x = LodgingInfo{}
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LodgingInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingInfo) ProtoMessage() {}

func (x *LodgingInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingInfo.ProtoReflect.Descriptor instead.
func (*LodgingInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_proto_rawDescGZIP(), []int{0}
}

func (x *LodgingInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingInfo) GetUnitType() int32 {
	if x != nil {
		return x.UnitType
	}
	return 0
}

func (x *LodgingInfo) GetUnits() []*LodgingUnit {
	if x != nil {
		return x.Units
	}
	return nil
}

func (x *LodgingInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LodgingInfo) GetMaxPetNumber() int32 {
	if x != nil {
		return x.MaxPetNumber
	}
	return 0
}

func (x *LodgingInfo) GetMaxPetTotalNumber() int32 {
	if x != nil {
		return x.MaxPetTotalNumber
	}
	return 0
}

func (x *LodgingInfo) GetDailyCapacityUsed() map[string]int32 {
	if x != nil {
		return x.DailyCapacityUsed
	}
	return nil
}

func (x *LodgingInfo) GetNeedPetSizeFilter() bool {
	if x != nil {
		return x.NeedPetSizeFilter
	}
	return false
}

func (x *LodgingInfo) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *LodgingInfo) GetTotalCapacity() int32 {
	if x != nil {
		return x.TotalCapacity
	}
	return 0
}

// LodgingUnit 住宿单元
type LodgingUnit struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 住宿单元ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 住宿单元名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 住宿预约信息
	Appointments  []*LodgingAppointmentInfo `protobuf:"bytes,3,rep,name=appointments,proto3" json:"appointments,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LodgingUnit) Reset() {
	*x = LodgingUnit{}
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LodgingUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingUnit) ProtoMessage() {}

func (x *LodgingUnit) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingUnit.ProtoReflect.Descriptor instead.
func (*LodgingUnit) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_proto_rawDescGZIP(), []int{1}
}

func (x *LodgingUnit) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingUnit) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LodgingUnit) GetAppointments() []*LodgingAppointmentInfo {
	if x != nil {
		return x.Appointments
	}
	return nil
}

// LodgingAppointmentInfo 住宿预约信息
type LodgingAppointmentInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 预约ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 颜色代码
	ColorCode string `protobuf:"bytes,3,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// 开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 服务实例ID
	ServiceInstanceId int64 `protobuf:"varint,6,opt,name=service_instance_id,json=serviceInstanceId,proto3" json:"service_instance_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *LodgingAppointmentInfo) Reset() {
	*x = LodgingAppointmentInfo{}
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LodgingAppointmentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingAppointmentInfo) ProtoMessage() {}

func (x *LodgingAppointmentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingAppointmentInfo.ProtoReflect.Descriptor instead.
func (*LodgingAppointmentInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_proto_rawDescGZIP(), []int{2}
}

func (x *LodgingAppointmentInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingAppointmentInfo) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *LodgingAppointmentInfo) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *LodgingAppointmentInfo) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *LodgingAppointmentInfo) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *LodgingAppointmentInfo) GetServiceInstanceId() int64 {
	if x != nil {
		return x.ServiceInstanceId
	}
	return 0
}

// LodgingFilter 住宿过滤条件
type LodgingFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 住宿类型ID列表
	LodgingTypeIds []int64 `protobuf:"varint,1,rep,packed,name=lodging_type_ids,json=lodgingTypeIds,proto3" json:"lodging_type_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *LodgingFilter) Reset() {
	*x = LodgingFilter{}
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LodgingFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingFilter) ProtoMessage() {}

func (x *LodgingFilter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingFilter.ProtoReflect.Descriptor instead.
func (*LodgingFilter) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_proto_rawDescGZIP(), []int{3}
}

func (x *LodgingFilter) GetLodgingTypeIds() []int64 {
	if x != nil {
		return x.LodgingTypeIds
	}
	return nil
}

var File_backend_proto_fulfillment_v1_calendar_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_calendar_proto_rawDesc = "" +
	"\n" +
	"+backend/proto/fulfillment/v1/calendar.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\x1a'backend/proto/offering/v1/service.proto\"\x98\x04\n" +
	"\vLodgingInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tunit_type\x18\x02 \x01(\x05R\bunitType\x12?\n" +
	"\x05units\x18\x03 \x03(\v2).backend.proto.fulfillment.v1.LodgingUnitR\x05units\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12$\n" +
	"\x0emax_pet_number\x18\x05 \x01(\x05R\fmaxPetNumber\x12/\n" +
	"\x14max_pet_total_number\x18\x06 \x01(\x05R\x11maxPetTotalNumber\x12p\n" +
	"\x13daily_capacity_used\x18\a \x03(\<EMAIL>\x11dailyCapacityUsed\x12/\n" +
	"\x14need_pet_size_filter\x18\b \x01(\bR\x11needPetSizeFilter\x12 \n" +
	"\fpet_size_ids\x18\t \x03(\x03R\n" +
	"petSizeIds\x12%\n" +
	"\x0etotal_capacity\x18\n" +
	" \x01(\x05R\rtotalCapacity\x1aD\n" +
	"\x16DailyCapacityUsedEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\"\x8b\x01\n" +
	"\vLodgingUnit\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12X\n" +
	"\fappointments\x18\x03 \x03(\v24.backend.proto.fulfillment.v1.LodgingAppointmentInfoR\fappointments\"\x8a\x02\n" +
	"\x16LodgingAppointmentInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\x12\x1d\n" +
	"\n" +
	"color_code\x18\x03 \x01(\tR\tcolorCode\x129\n" +
	"\n" +
	"start_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12.\n" +
	"\x13service_instance_id\x18\x06 \x01(\x03R\x11serviceInstanceId\"K\n" +
	"\rLodgingFilter\x12:\n" +
	"\x10lodging_type_ids\x18\x01 \x03(\x03B\x10\xbaH\r\x92\x01\n" +
	"\x10d\x18\x01\"\x04\"\x02(\x00R\x0elodgingTypeIdsBt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_calendar_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_calendar_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_calendar_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_calendar_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_calendar_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_calendar_proto_rawDesc), len(file_backend_proto_fulfillment_v1_calendar_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_calendar_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_calendar_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_backend_proto_fulfillment_v1_calendar_proto_goTypes = []any{
	(*LodgingInfo)(nil),            // 0: backend.proto.fulfillment.v1.LodgingInfo
	(*LodgingUnit)(nil),            // 1: backend.proto.fulfillment.v1.LodgingUnit
	(*LodgingAppointmentInfo)(nil), // 2: backend.proto.fulfillment.v1.LodgingAppointmentInfo
	(*LodgingFilter)(nil),          // 3: backend.proto.fulfillment.v1.LodgingFilter
	nil,                            // 4: backend.proto.fulfillment.v1.LodgingInfo.DailyCapacityUsedEntry
	(*timestamppb.Timestamp)(nil),  // 5: google.protobuf.Timestamp
}
var file_backend_proto_fulfillment_v1_calendar_proto_depIdxs = []int32{
	1, // 0: backend.proto.fulfillment.v1.LodgingInfo.units:type_name -> backend.proto.fulfillment.v1.LodgingUnit
	4, // 1: backend.proto.fulfillment.v1.LodgingInfo.daily_capacity_used:type_name -> backend.proto.fulfillment.v1.LodgingInfo.DailyCapacityUsedEntry
	2, // 2: backend.proto.fulfillment.v1.LodgingUnit.appointments:type_name -> backend.proto.fulfillment.v1.LodgingAppointmentInfo
	5, // 3: backend.proto.fulfillment.v1.LodgingAppointmentInfo.start_time:type_name -> google.protobuf.Timestamp
	5, // 4: backend.proto.fulfillment.v1.LodgingAppointmentInfo.end_time:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_calendar_proto_init() }
func file_backend_proto_fulfillment_v1_calendar_proto_init() {
	if File_backend_proto_fulfillment_v1_calendar_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_calendar_proto_rawDesc), len(file_backend_proto_fulfillment_v1_calendar_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_calendar_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_calendar_proto_depIdxs,
		MessageInfos:      file_backend_proto_fulfillment_v1_calendar_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_calendar_proto = out.File
	file_backend_proto_fulfillment_v1_calendar_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_calendar_proto_depIdxs = nil
}
