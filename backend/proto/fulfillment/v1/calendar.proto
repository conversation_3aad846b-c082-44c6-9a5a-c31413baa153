syntax = "proto3";

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: lodging package structure is appropriate for this domain --)
package backend.proto.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "buf/validate/validate.proto";
import "backend/proto/offering/v1/service.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// LodgingInfo 住宿信息
message LodgingInfo {
    // 住宿ID
    int64 id = 1;
    // 住宿类型
    int32 unit_type = 2;
    // 住宿单元
    repeated LodgingUnit units = 3;
    // 住宿名称
    string name = 4;
    // 最大宠物数量
    int32 max_pet_number = 5;
    // 最大宠物总数
    int32 max_pet_total_number = 6;
    // 每日容量使用情况
    map<string, int32> daily_capacity_used = 7;
    // 是否需要宠物大小过滤
    bool need_pet_size_filter = 8;
    // 宠物大小ID
    repeated int64 pet_size_ids = 9;
    // 总容量
    int32 total_capacity = 10;
}

// LodgingUnit 住宿单元
message LodgingUnit {
    // 住宿单元ID
    int64 id = 1;
    // 住宿单元名称
    string name = 2;
    // 住宿预约信息
    repeated LodgingAppointmentInfo appointments = 3;
}

// LodgingAppointmentInfo 住宿预约信息
message LodgingAppointmentInfo {
    // 预约ID
    int64 id = 1;
    // 客户ID
    int64 customer_id = 2;
    // 颜色代码
    string color_code = 3;
    // 开始时间
    google.protobuf.Timestamp start_time = 4;
    // 结束时间
    google.protobuf.Timestamp end_time = 5;
    // 服务实例ID
    int64 service_instance_id = 6;
}

// LodgingFilter 住宿过滤条件
message LodgingFilter {
    // 住宿类型ID列表
    repeated int64 lodging_type_ids = 1 [(buf.validate.field).repeated = {
        max_items: 100
        unique: true
        items: {
          int64: {gte: 0}
        }
    }];
}
