// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/calendar_service.proto

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: lodging package structure is appropriate for this domain --)

package fulfillmentpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetLodgingCalendarRequest 获取住宿日历请求
type GetLodgingCalendarRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 查询开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 查询结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 过滤条件
	Filter        *LodgingFilter `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLodgingCalendarRequest) Reset() {
	*x = GetLodgingCalendarRequest{}
	mi := &file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLodgingCalendarRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLodgingCalendarRequest) ProtoMessage() {}

func (x *GetLodgingCalendarRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLodgingCalendarRequest.ProtoReflect.Descriptor instead.
func (*GetLodgingCalendarRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetLodgingCalendarRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetLodgingCalendarRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GetLodgingCalendarRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *GetLodgingCalendarRequest) GetFilter() *LodgingFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// GetLodgingCalendarResponse 获取住宿日历响应
type GetLodgingCalendarResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 住宿信息列表
	// (-- api-linter: core::0140::lower-snake=disabled
	//
	//	aip.dev/not-precedent: lodgingInfos is clear and follows existing convention --)
	LodgingInfos  []*LodgingInfo `protobuf:"bytes,1,rep,name=lodgingInfos,proto3" json:"lodgingInfos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLodgingCalendarResponse) Reset() {
	*x = GetLodgingCalendarResponse{}
	mi := &file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLodgingCalendarResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLodgingCalendarResponse) ProtoMessage() {}

func (x *GetLodgingCalendarResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLodgingCalendarResponse.ProtoReflect.Descriptor instead.
func (*GetLodgingCalendarResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetLodgingCalendarResponse) GetLodgingInfos() []*LodgingInfo {
	if x != nil {
		return x.LodgingInfos
	}
	return nil
}

var File_backend_proto_fulfillment_v1_calendar_service_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_calendar_service_proto_rawDesc = "" +
	"\n" +
	"3backend/proto/fulfillment/v1/calendar_service.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\x1a+backend/proto/fulfillment/v1/calendar.proto\"\xfc\x01\n" +
	"\x19GetLodgingCalendarRequest\x12(\n" +
	"\vbusiness_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x129\n" +
	"\n" +
	"start_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12C\n" +
	"\x06filter\x18\x04 \x01(\v2+.backend.proto.fulfillment.v1.LodgingFilterR\x06filter\"k\n" +
	"\x1aGetLodgingCalendarResponse\x12M\n" +
	"\flodgingInfos\x18\x01 \x03(\v2).backend.proto.fulfillment.v1.LodgingInfoR\flodgingInfos2\x9b\x01\n" +
	"\x0fCalendarService\x12\x87\x01\n" +
	"\x12GetLodgingCalendar\x127.backend.proto.fulfillment.v1.GetLodgingCalendarRequest\x1a8.backend.proto.fulfillment.v1.GetLodgingCalendarResponseBt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_calendar_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_calendar_service_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_fulfillment_v1_calendar_service_proto_goTypes = []any{
	(*GetLodgingCalendarRequest)(nil),  // 0: backend.proto.fulfillment.v1.GetLodgingCalendarRequest
	(*GetLodgingCalendarResponse)(nil), // 1: backend.proto.fulfillment.v1.GetLodgingCalendarResponse
	(*timestamppb.Timestamp)(nil),      // 2: google.protobuf.Timestamp
	(*LodgingFilter)(nil),              // 3: backend.proto.fulfillment.v1.LodgingFilter
	(*LodgingInfo)(nil),                // 4: backend.proto.fulfillment.v1.LodgingInfo
}
var file_backend_proto_fulfillment_v1_calendar_service_proto_depIdxs = []int32{
	2, // 0: backend.proto.fulfillment.v1.GetLodgingCalendarRequest.start_time:type_name -> google.protobuf.Timestamp
	2, // 1: backend.proto.fulfillment.v1.GetLodgingCalendarRequest.end_time:type_name -> google.protobuf.Timestamp
	3, // 2: backend.proto.fulfillment.v1.GetLodgingCalendarRequest.filter:type_name -> backend.proto.fulfillment.v1.LodgingFilter
	4, // 3: backend.proto.fulfillment.v1.GetLodgingCalendarResponse.lodgingInfos:type_name -> backend.proto.fulfillment.v1.LodgingInfo
	0, // 4: backend.proto.fulfillment.v1.CalendarService.GetLodgingCalendar:input_type -> backend.proto.fulfillment.v1.GetLodgingCalendarRequest
	1, // 5: backend.proto.fulfillment.v1.CalendarService.GetLodgingCalendar:output_type -> backend.proto.fulfillment.v1.GetLodgingCalendarResponse
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_calendar_service_proto_init() }
func file_backend_proto_fulfillment_v1_calendar_service_proto_init() {
	if File_backend_proto_fulfillment_v1_calendar_service_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_calendar_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_calendar_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_calendar_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_calendar_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_calendar_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_calendar_service_proto = out.File
	file_backend_proto_fulfillment_v1_calendar_service_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_calendar_service_proto_depIdxs = nil
}
