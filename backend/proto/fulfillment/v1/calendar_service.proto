syntax = "proto3";

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: lodging package structure is appropriate for this domain --)
package backend.proto.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "buf/validate/validate.proto";
import "backend/proto/fulfillment/v1/calendar.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// CalendarService 住宿服务
service CalendarService {
  // GetLodgingCalendar 获取住宿日历信息
  // (-- api-linter: core::0131::response-message-name=disabled
  //     aip.dev/not-precedent: GetLodgingCalendarResponse is appropriate for this use case --)
  rpc GetLodgingCalendar(GetLodgingCalendarRequest) returns (GetLodgingCalendarResponse);
}

// GetLodgingCalendarRequest 获取住宿日历请求
message GetLodgingCalendarRequest {
    // 商家ID
    int64 business_id = 1 [(buf.validate.field).int64.gt = 0];
    // 查询开始时间
    google.protobuf.Timestamp start_time = 2;
    // 查询结束时间
    google.protobuf.Timestamp end_time = 3;
    // 过滤条件
    LodgingFilter filter = 4;
}

// GetLodgingCalendarResponse 获取住宿日历响应
message GetLodgingCalendarResponse {
  // 住宿信息列表
  // (-- api-linter: core::0140::lower-snake=disabled
  //     aip.dev/not-precedent: lodgingInfos is clear and follows existing convention --)
  repeated LodgingInfo lodgingInfos = 1;      
}
