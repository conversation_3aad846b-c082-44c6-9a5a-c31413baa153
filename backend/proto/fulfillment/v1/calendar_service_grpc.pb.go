// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/fulfillment/v1/calendar_service.proto

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: lodging package structure is appropriate for this domain --)

package fulfillmentpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CalendarService_GetLodgingCalendar_FullMethodName = "/backend.proto.fulfillment.v1.CalendarService/GetLodgingCalendar"
)

// CalendarServiceClient is the client API for CalendarService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// CalendarService 住宿服务
type CalendarServiceClient interface {
	// GetLodgingCalendar 获取住宿日历信息
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetLodgingCalendarResponse is appropriate for this use case --)
	GetLodgingCalendar(ctx context.Context, in *GetLodgingCalendarRequest, opts ...grpc.CallOption) (*GetLodgingCalendarResponse, error)
}

type calendarServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCalendarServiceClient(cc grpc.ClientConnInterface) CalendarServiceClient {
	return &calendarServiceClient{cc}
}

func (c *calendarServiceClient) GetLodgingCalendar(ctx context.Context, in *GetLodgingCalendarRequest, opts ...grpc.CallOption) (*GetLodgingCalendarResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLodgingCalendarResponse)
	err := c.cc.Invoke(ctx, CalendarService_GetLodgingCalendar_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CalendarServiceServer is the server API for CalendarService service.
// All implementations must embed UnimplementedCalendarServiceServer
// for forward compatibility.
//
// CalendarService 住宿服务
type CalendarServiceServer interface {
	// GetLodgingCalendar 获取住宿日历信息
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetLodgingCalendarResponse is appropriate for this use case --)
	GetLodgingCalendar(context.Context, *GetLodgingCalendarRequest) (*GetLodgingCalendarResponse, error)
	mustEmbedUnimplementedCalendarServiceServer()
}

// UnimplementedCalendarServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCalendarServiceServer struct{}

func (UnimplementedCalendarServiceServer) GetLodgingCalendar(context.Context, *GetLodgingCalendarRequest) (*GetLodgingCalendarResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLodgingCalendar not implemented")
}
func (UnimplementedCalendarServiceServer) mustEmbedUnimplementedCalendarServiceServer() {}
func (UnimplementedCalendarServiceServer) testEmbeddedByValue()                         {}

// UnsafeCalendarServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CalendarServiceServer will
// result in compilation errors.
type UnsafeCalendarServiceServer interface {
	mustEmbedUnimplementedCalendarServiceServer()
}

func RegisterCalendarServiceServer(s grpc.ServiceRegistrar, srv CalendarServiceServer) {
	// If the following call pancis, it indicates UnimplementedCalendarServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CalendarService_ServiceDesc, srv)
}

func _CalendarService_GetLodgingCalendar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLodgingCalendarRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CalendarServiceServer).GetLodgingCalendar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CalendarService_GetLodgingCalendar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CalendarServiceServer).GetLodgingCalendar(ctx, req.(*GetLodgingCalendarRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CalendarService_ServiceDesc is the grpc.ServiceDesc for CalendarService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CalendarService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.fulfillment.v1.CalendarService",
	HandlerType: (*CalendarServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLodgingCalendar",
			Handler:    _CalendarService_GetLodgingCalendar_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/fulfillment/v1/calendar_service.proto",
}
