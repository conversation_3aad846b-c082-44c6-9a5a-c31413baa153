syntax = "proto3";

package backend.proto.fulfillment.v1;

import "google/protobuf/timestamp.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// 分页信息
message PaginationRef {
  // 偏移量，默认0
  int32 offset = 1;
  // 每页数量，默认200
  int32 limit = 2;
}
  
// 排序方式
enum SortType {
  // SORT_TYPE_UNSPECIFIED 默认排序类型
  SORT_TYPE_UNSPECIFIED = 0;
  // SORT_TYPE_START_TIME 按服务开始时间
  SORT_TYPE_START_TIME = 1;
}

// 日期类型
enum DateType {
  // 未指定日期类型
  DATE_TYPE_UNSPECIFIED = 0;
  // 每日日期
  DATE_TYPE_DATE_EVERYDAY = 1;
  // 特定日期
  DATE_TYPE_SPECIFIC_DATE = 2;
  // 每日包含退房日
  DATE_TYPE_EVERYDAY_EXCEPT_CHECKOUT_DAY = 3;
  // 每日排除入住日
  DATE_TYPE_EVERYDAY_EXCEPT_CHECKIN_DAY = 4;
  // 最后一天
  DATE_TYPE_LAST_DAY = 5;
  // 第一天
  DATE_TYPE_FIRST_DAY = 6;
}

// 服务类型枚举
enum ServiceType {
  // 未指定的服务类型
  SERVICE_TYPE_UNSPECIFIED = 0;
  // 服务类型
  SERVICE_TYPE_SERVICE = 1;
  // 选项类型
  SERVICE_TYPE_OPTION = 2;
}

// 覆盖类型枚举，用于定义不同的覆盖策略
enum OverrideType {
  // 未指定
  OVERRIDE_TYPE_UNSPECIFIED = 0;
  // 覆盖类型 1 - override by location (business)
  OVERRIDE_TYPE_LOCATION = 1;
  // override by pet (client)
  OVERRIDE_TYPE_PET = 2;
  // override by staff
  OVERRIDE_TYPE_STAFF = 3;
}

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
  // (-- api-linter: core::0126::unspecified=disabled
  //     aip.dev/not-precedent: We need to do this because
  //     the content of the error code is automatically generated by
  //     the script and is exclusive to each service.
  //     Please do not turn off this linter for the rest of the enum --)
  // 成功
  ERR_CODE_OK = 0;
  // 不合法的参数
  ERR_CODE_INVALID = 122000;

  // ------ fulfillment report error code ------
  ERR_CODE_SEND_REPORT = 123001;
}
