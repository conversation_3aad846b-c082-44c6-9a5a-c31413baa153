syntax = "proto3";

package backend.proto.fulfillment.v1;

import "backend/proto/offering/v1/care_type.proto";
import "google/protobuf/timestamp.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// 过滤条件
message FulfillmentFilter {
  // 服务类型（可选，不填就是所有类型）
  repeated backend.proto.offering.v1.CareCategory care_types = 1;
  // 宠物ID（可选，不填就是所有pet）
  repeated int64 pet_ids = 2;
  // 员工ID列表(可选)
  repeated int64 staff_ids = 3;
  // 状态(可选)
  repeated int32 states = 4;
  // 客户ID（可选）
  repeated int64 customer_ids = 5;
}

// 履约信息
message Fulfillment {
  // 履约ID
  int64 id = 1;
  // 开始时间
  google.protobuf.Timestamp start_time = 2;  
  // 结束时间
  google.protobuf.Timestamp end_time = 3;
  // 商家ID
  int64 business_id = 4; 
  // 公司ID
  int64 customer_id = 5;
  // 宠物ID
  int64 pet_id = 6;
  // 服务实例ID
  int64 service_instance_id = 7;
  // 服务factory_id
  int64 service_factory_id = 8;
  // 护理类型
  backend.proto.offering.v1.CareCategory care_type = 9;
  // 住宿ID
  int64 lodging_id = 10;
  // 场地ID
  int64 playgroup_id = 11;
  // 状态
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: don't need --)
  State state = 12;
  // 员工ID类型
  repeated int64 staff_ids = 13;
  // 订单ID
  int64 order_id = 14;
}

// State信息
enum State {
  // STATE_UNSPECIFIED 默认state
  STATE_UNSPECIFIED = 0;
  // STATE_NOT_START 未开始的state
  STATE_NOT_START = 1;
}
