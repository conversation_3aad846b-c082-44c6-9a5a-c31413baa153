syntax = "proto3";

package backend.proto.fulfillment.v1;
import "google/protobuf/timestamp.proto";
import "backend/proto/fulfillment/v1/fulfillment.proto";
import "backend/proto/fulfillment/v1/common.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

import "buf/validate/validate.proto";

// FulfillmentService
service FulfillmentService {
  // ListFulfillment 获取履约信息
  rpc ListFulfillment(ListFulfillmentRequest) returns (ListFulfillmentResponse);

    // GetPetsByTimeRange 根据时间区间获取宠物信息
  // (-- api-linter: core::0136::prepositions=disabled
  //     aip.dev/not-precedent: GetPetsByTimeRange is clear and appropriate --)
  // (-- api-linter: core::0131::response-message-name=disabled
  //     aip.dev/not-precedent: GetPetsByTimeRangeResponse is appropriate for this use case --)
  rpc GetPetsByTimeRange(GetPetsByTimeRangeRequest) returns (GetPetsByTimeRangeResponse);

  // GetPetCountByTimeRange 根据时间区间统计不同careType的宠物数量
  // (-- api-linter: core::0136::prepositions=disabled
  //     aip.dev/not-precedent: GetPetCountByTimeRange is clear and appropriate --)
  // (-- api-linter: core::0131::response-message-name=disabled
  //     aip.dev/not-precedent: GetPetCountByTimeRangeResponse is appropriate for this use case --)
  rpc GetPetCountByTimeRange(GetPetCountByTimeRangeRequest) returns (GetPetCountByTimeRangeResponse);
}

// GetPetsByTimeRangeRequest 根据时间区间获取宠物信息请求
message GetPetsByTimeRangeRequest {
  // 公司ID
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: company_id is clear and descriptive --)
  int64 company_id = 1 [(buf.validate.field).int64.gt = 0];
  // 商家ID
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: business_id is clear and descriptive --)
  int64 business_id = 2 [(buf.validate.field).int64.gt = 0];
  // 查询开始时间
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: start_time is clear and descriptive --)
  google.protobuf.Timestamp start_time = 3 [(buf.validate.field).required = true];
  // 查询结束时间
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: end_time is clear and descriptive --)
  google.protobuf.Timestamp end_time = 4 [(buf.validate.field).required = true];
}

// GetPetsByTimeRangeResponse 根据时间区间获取宠物信息响应
message GetPetsByTimeRangeResponse {
  // 宠物信息列表
  repeated PetInfo pets = 1;
  // 总数量
  int32 total = 2;
}

// PetInfo 宠物信息
message PetInfo {
  // 宠物ID
  int64 id = 1;
  // 宠物头像路径
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: avatar_path is clear and descriptive --)
  string avatar_path = 2;
  // 宠物名称
  string name = 3;
  // 宠物类型
  int32 type = 4;
}

// GetPetCountByTimeRangeRequest 根据时间区间统计不同careType的宠物数量请求
message GetPetCountByTimeRangeRequest {
  // 公司ID
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: company_id is clear and descriptive --)
  int64 company_id = 1 [(buf.validate.field).int64.gt = 0];
  // 商家ID
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: business_id is clear and descriptive --)
  int64 business_id = 2 [(buf.validate.field).int64.gt = 0];
  // 查询开始时间
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: start_time is clear and descriptive --)
  google.protobuf.Timestamp start_time = 3 [(buf.validate.field).required = true];
  // 查询结束时间
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: end_time is clear and descriptive --)
  google.protobuf.Timestamp end_time = 4 [(buf.validate.field).required = true];
}

// GetPetCountByTimeRangeResponse 根据时间区间统计不同careType的宠物数量响应
message GetPetCountByTimeRangeResponse {
  // 按日期分组的统计数据
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: daily_pet_counts is clear and descriptive --)
  repeated DailyPetCount daily_pet_counts = 1;
  // 总宠物数量（所有日期的去重总数）
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: total_pet_count is clear and descriptive --)
  int32 total_pet_count = 2;
}

// DailyPetCount 每日宠物数量统计
message DailyPetCount {
  // 日期（YYYY-MM-DD格式）
  // (-- api-linter: core::0142::time-field-type=disabled
  //     aip.dev/not-precedent: string date format is appropriate for daily statistics --)
  string date = 1;
  // 按care type分组的宠物数量统计
  // key: care_type_id (int64)
  // value: 该care type的宠物数量 (int32)
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: We need to do this because reasons. --)
  map<int64, int32> pet_care_type_count = 2;
  // 需要住宿的宠物数量
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: pet_count_with_lodging is clear and descriptive --)
  int32 pet_count_with_lodging = 3;
}

// ListFulfillmentRequest
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 无效的parent定义 --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 不需要page-size --)
// (-- api-linter: core::0158::request-page_token-field=disabled
//     aip.dev/not-precedent: 不需要page_token --)
// (-- api-linter: core::0158::request-next_page_token-field=disabled
//     aip.dev/not-precedent: 不需要next_page_token --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 不需要page-token --)
message ListFulfillmentRequest {
  // 公司ID
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: company_id is clear and descriptive --)
  int64 company_id = 1 [(buf.validate.field).int64.gt = 0];
  // 商家ID
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: business_id is clear and descriptive --)
  int64 business_id = 2 [(buf.validate.field).int64.gt = 0];
  // 查询开始时间
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: start_time is clear and descriptive --)
  google.protobuf.Timestamp start_time = 3;
  // 查询结束时间
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: end_time is clear and descriptive --)
  google.protobuf.Timestamp end_time = 4;
  // 过滤条件
  // (-- api-linter: core::0132::request-field-types=disabled
  //     aip.dev/not-precedent: 打平成string不利用协议理解 --)
  FulfillmentFilter filter = 5;
  // 排序方式
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: sort_type is clear and descriptive --)
  SortType sort_type = 6;
  // 分页信息
  PaginationRef pagination = 7;
}

// ListFulfillmentResponse
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 不需要next-page-token --)
message ListFulfillmentResponse {
  // 履约列表
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  repeated Fulfillment fulfillments = 1;
  // 分页信息
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  PaginationRef pagination = 2;
  // 是否最后一页
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: is_end is clear and descriptive --)
  bool is_end = 3;
  // 总条数
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  int32 total = 4;
}