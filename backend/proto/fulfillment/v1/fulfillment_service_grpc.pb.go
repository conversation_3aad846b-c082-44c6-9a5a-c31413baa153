// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/fulfillment/v1/fulfillment_service.proto

package fulfillmentpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	FulfillmentService_ListFulfillment_FullMethodName        = "/backend.proto.fulfillment.v1.FulfillmentService/ListFulfillment"
	FulfillmentService_GetPetsByTimeRange_FullMethodName     = "/backend.proto.fulfillment.v1.FulfillmentService/GetPetsByTimeRange"
	FulfillmentService_GetPetCountByTimeRange_FullMethodName = "/backend.proto.fulfillment.v1.FulfillmentService/GetPetCountByTimeRange"
)

// FulfillmentServiceClient is the client API for FulfillmentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// FulfillmentService
type FulfillmentServiceClient interface {
	// ListFulfillment 获取履约信息
	ListFulfillment(ctx context.Context, in *ListFulfillmentRequest, opts ...grpc.CallOption) (*ListFulfillmentResponse, error)
	// GetPetsByTimeRange 根据时间区间获取宠物信息
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: GetPetsByTimeRange is clear and appropriate --)
	//
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetPetsByTimeRangeResponse is appropriate for this use case --)
	GetPetsByTimeRange(ctx context.Context, in *GetPetsByTimeRangeRequest, opts ...grpc.CallOption) (*GetPetsByTimeRangeResponse, error)
	// GetPetCountByTimeRange 根据时间区间统计不同careType的宠物数量
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: GetPetCountByTimeRange is clear and appropriate --)
	//
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetPetCountByTimeRangeResponse is appropriate for this use case --)
	GetPetCountByTimeRange(ctx context.Context, in *GetPetCountByTimeRangeRequest, opts ...grpc.CallOption) (*GetPetCountByTimeRangeResponse, error)
}

type fulfillmentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFulfillmentServiceClient(cc grpc.ClientConnInterface) FulfillmentServiceClient {
	return &fulfillmentServiceClient{cc}
}

func (c *fulfillmentServiceClient) ListFulfillment(ctx context.Context, in *ListFulfillmentRequest, opts ...grpc.CallOption) (*ListFulfillmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListFulfillmentResponse)
	err := c.cc.Invoke(ctx, FulfillmentService_ListFulfillment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentServiceClient) GetPetsByTimeRange(ctx context.Context, in *GetPetsByTimeRangeRequest, opts ...grpc.CallOption) (*GetPetsByTimeRangeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPetsByTimeRangeResponse)
	err := c.cc.Invoke(ctx, FulfillmentService_GetPetsByTimeRange_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentServiceClient) GetPetCountByTimeRange(ctx context.Context, in *GetPetCountByTimeRangeRequest, opts ...grpc.CallOption) (*GetPetCountByTimeRangeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPetCountByTimeRangeResponse)
	err := c.cc.Invoke(ctx, FulfillmentService_GetPetCountByTimeRange_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FulfillmentServiceServer is the server API for FulfillmentService service.
// All implementations must embed UnimplementedFulfillmentServiceServer
// for forward compatibility.
//
// FulfillmentService
type FulfillmentServiceServer interface {
	// ListFulfillment 获取履约信息
	ListFulfillment(context.Context, *ListFulfillmentRequest) (*ListFulfillmentResponse, error)
	// GetPetsByTimeRange 根据时间区间获取宠物信息
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: GetPetsByTimeRange is clear and appropriate --)
	//
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetPetsByTimeRangeResponse is appropriate for this use case --)
	GetPetsByTimeRange(context.Context, *GetPetsByTimeRangeRequest) (*GetPetsByTimeRangeResponse, error)
	// GetPetCountByTimeRange 根据时间区间统计不同careType的宠物数量
	// (-- api-linter: core::0136::prepositions=disabled
	//
	//	aip.dev/not-precedent: GetPetCountByTimeRange is clear and appropriate --)
	//
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: GetPetCountByTimeRangeResponse is appropriate for this use case --)
	GetPetCountByTimeRange(context.Context, *GetPetCountByTimeRangeRequest) (*GetPetCountByTimeRangeResponse, error)
	mustEmbedUnimplementedFulfillmentServiceServer()
}

// UnimplementedFulfillmentServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFulfillmentServiceServer struct{}

func (UnimplementedFulfillmentServiceServer) ListFulfillment(context.Context, *ListFulfillmentRequest) (*ListFulfillmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFulfillment not implemented")
}
func (UnimplementedFulfillmentServiceServer) GetPetsByTimeRange(context.Context, *GetPetsByTimeRangeRequest) (*GetPetsByTimeRangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetsByTimeRange not implemented")
}
func (UnimplementedFulfillmentServiceServer) GetPetCountByTimeRange(context.Context, *GetPetCountByTimeRangeRequest) (*GetPetCountByTimeRangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetCountByTimeRange not implemented")
}
func (UnimplementedFulfillmentServiceServer) mustEmbedUnimplementedFulfillmentServiceServer() {}
func (UnimplementedFulfillmentServiceServer) testEmbeddedByValue()                            {}

// UnsafeFulfillmentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FulfillmentServiceServer will
// result in compilation errors.
type UnsafeFulfillmentServiceServer interface {
	mustEmbedUnimplementedFulfillmentServiceServer()
}

func RegisterFulfillmentServiceServer(s grpc.ServiceRegistrar, srv FulfillmentServiceServer) {
	// If the following call pancis, it indicates UnimplementedFulfillmentServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&FulfillmentService_ServiceDesc, srv)
}

func _FulfillmentService_ListFulfillment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFulfillmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentServiceServer).ListFulfillment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentService_ListFulfillment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentServiceServer).ListFulfillment(ctx, req.(*ListFulfillmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentService_GetPetsByTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetsByTimeRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentServiceServer).GetPetsByTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentService_GetPetsByTimeRange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentServiceServer).GetPetsByTimeRange(ctx, req.(*GetPetsByTimeRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentService_GetPetCountByTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetCountByTimeRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentServiceServer).GetPetCountByTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentService_GetPetCountByTimeRange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentServiceServer).GetPetCountByTimeRange(ctx, req.(*GetPetCountByTimeRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FulfillmentService_ServiceDesc is the grpc.ServiceDesc for FulfillmentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FulfillmentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.fulfillment.v1.FulfillmentService",
	HandlerType: (*FulfillmentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListFulfillment",
			Handler:    _FulfillmentService_ListFulfillment_Handler,
		},
		{
			MethodName: "GetPetsByTimeRange",
			Handler:    _FulfillmentService_GetPetsByTimeRange_Handler,
		},
		{
			MethodName: "GetPetCountByTimeRange",
			Handler:    _FulfillmentService_GetPetCountByTimeRange_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/fulfillment/v1/fulfillment_service.proto",
}
