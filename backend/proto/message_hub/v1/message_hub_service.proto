syntax = "proto3";

package backend.proto.message_hub.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/message_hub/v1;messagehubpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.message_hub.v1";

import "buf/validate/validate.proto";
import "google/type/phone_number.proto";
import "backend/proto/message_hub/v1/callback.proto";
import "backend/proto/message_hub/v1/send_state.proto";

// MessageHubService 是用于发送各种类型消息的中心服务。
service MessageHubService {
  // SendMessage 用于发送各种类型的消息。
  rpc SendMessage(SendMessageRequest) returns (SendMessageResponse);
}

// SendMessageRequest
message SendMessageRequest {
  // 消息的有效负载。每个请求只能发送一种类型。
  oneof payload {
    option (buf.validate.oneof).required = true;
    // 短信消息。
    SendSmsPayload sms = 1;
    // 语音通话消息。
    SendCallPayload call = 2;
  }
}

// SendSmsPayload
message SendSmsPayload {
  // 发送方的电话号码。
  // 对于 B 端发送给 C 端的场景，需要设置 B 端的手机号作为 sender。
  // 对于 MoeGo 平台对外发送的场景，不需要设置 sender，服务端会自行使用平台的号码发送。
  optional google.type.PhoneNumber sender = 1;

  // 接收方的电话号码。
  google.type.PhoneNumber recipient = 2;

  // 短信的内容。
  string content = 3 [(buf.validate.field).string.min_len = 1];

  // 用于接收此短信状态变化的回调。
  optional Callback state_callback = 4;
}

// SendCallPayload
message SendCallPayload {
  // 发送方的电话号码。
  // 对于 B 端发送给 C 端的场景，需要设置 B 端的手机号作为 sender。
  // 对于 MoeGo 平台对外发送的场景，不需要设置 sender，服务端会自行使用平台的号码发送。
  optional google.type.PhoneNumber sender = 1;

  // 接收方的电话号码。
  google.type.PhoneNumber recipient = 2;

  // 通话中要播放的内容。
  string content = 3 [(buf.validate.field).string.min_len = 1];

  // 用于接收此通话状态变化的回调。
  optional Callback state_callback = 4;

  // 用于接收来自接收方的数字回复的回调。
  optional Callback reply_callback = 5;
}


// SendMessageResponse
message SendMessageResponse {
  // 由 Message Hub 生成的消息的唯一标识符。
  string message_id = 1;
  // 消息接收的初始状态。
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: This field is in a response message,
  //     which is inherently output-only. --)
  SendState state = 2;
  // 如果消息发送失败，则为错误代码。
  int32 error_code = 3;
  // 如果消息发送失败，则为错误信息。
  string error_message = 4;
}

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
  // (-- api-linter: core::0126::unspecified=disabled
  //     aip.dev/not-precedent: We need to do this because
  //     the content of the error code is automatically generated by
  //     the script and is exclusive to each service.
  //     Please do not turn off this linter for the rest of the enum --)
  // 成功
  ERR_CODE_OK = 0;
  // 本服务自动分配的全局唯一的起始错误码
  ERR_CODE_UNSPECIFIED = 996400;
}
