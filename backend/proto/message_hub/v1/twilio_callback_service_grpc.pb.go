// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/message_hub/v1/twilio_callback_service.proto

package messagehubpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TwilioCallbackService_HandleSmsStatusCallback_FullMethodName = "/backend.proto.message_hub.v1.TwilioCallbackService/HandleSmsStatusCallback"
)

// TwilioCallbackServiceClient is the client API for TwilioCallbackService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// TwilioCallbackService handles callbacks from Twilio for message status updates.
type TwilioCallbackServiceClient interface {
	// HandleSmsStatusCallback receives status updates from Twilio.
	// see: https://www.twilio.com/docs/messaging/api/message-resource#twilios-request-to-the-statuscallback-url
	HandleSmsStatusCallback(ctx context.Context, in *HandleSmsStatusCallbackRequest, opts ...grpc.CallOption) (*HandleSmsStatusCallbackResponse, error)
}

type twilioCallbackServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTwilioCallbackServiceClient(cc grpc.ClientConnInterface) TwilioCallbackServiceClient {
	return &twilioCallbackServiceClient{cc}
}

func (c *twilioCallbackServiceClient) HandleSmsStatusCallback(ctx context.Context, in *HandleSmsStatusCallbackRequest, opts ...grpc.CallOption) (*HandleSmsStatusCallbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandleSmsStatusCallbackResponse)
	err := c.cc.Invoke(ctx, TwilioCallbackService_HandleSmsStatusCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TwilioCallbackServiceServer is the server API for TwilioCallbackService service.
// All implementations must embed UnimplementedTwilioCallbackServiceServer
// for forward compatibility.
//
// TwilioCallbackService handles callbacks from Twilio for message status updates.
type TwilioCallbackServiceServer interface {
	// HandleSmsStatusCallback receives status updates from Twilio.
	// see: https://www.twilio.com/docs/messaging/api/message-resource#twilios-request-to-the-statuscallback-url
	HandleSmsStatusCallback(context.Context, *HandleSmsStatusCallbackRequest) (*HandleSmsStatusCallbackResponse, error)
	mustEmbedUnimplementedTwilioCallbackServiceServer()
}

// UnimplementedTwilioCallbackServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTwilioCallbackServiceServer struct{}

func (UnimplementedTwilioCallbackServiceServer) HandleSmsStatusCallback(context.Context, *HandleSmsStatusCallbackRequest) (*HandleSmsStatusCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleSmsStatusCallback not implemented")
}
func (UnimplementedTwilioCallbackServiceServer) mustEmbedUnimplementedTwilioCallbackServiceServer() {}
func (UnimplementedTwilioCallbackServiceServer) testEmbeddedByValue()                               {}

// UnsafeTwilioCallbackServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TwilioCallbackServiceServer will
// result in compilation errors.
type UnsafeTwilioCallbackServiceServer interface {
	mustEmbedUnimplementedTwilioCallbackServiceServer()
}

func RegisterTwilioCallbackServiceServer(s grpc.ServiceRegistrar, srv TwilioCallbackServiceServer) {
	// If the following call pancis, it indicates UnimplementedTwilioCallbackServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TwilioCallbackService_ServiceDesc, srv)
}

func _TwilioCallbackService_HandleSmsStatusCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleSmsStatusCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TwilioCallbackServiceServer).HandleSmsStatusCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TwilioCallbackService_HandleSmsStatusCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TwilioCallbackServiceServer).HandleSmsStatusCallback(ctx, req.(*HandleSmsStatusCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TwilioCallbackService_ServiceDesc is the grpc.ServiceDesc for TwilioCallbackService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TwilioCallbackService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.message_hub.v1.TwilioCallbackService",
	HandlerType: (*TwilioCallbackServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HandleSmsStatusCallback",
			Handler:    _TwilioCallbackService_HandleSmsStatusCallback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/message_hub/v1/twilio_callback_service.proto",
}
