// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/offering/v1/care_type_service.proto

package offeringpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CareTypeService_CreateCareType_FullMethodName          = "/backend.proto.offering.v1.CareTypeService/CreateCareType"
	CareTypeService_GetCareType_FullMethodName             = "/backend.proto.offering.v1.CareTypeService/GetCareType"
	CareTypeService_ListCareTypes_FullMethodName           = "/backend.proto.offering.v1.CareTypeService/ListCareTypes"
	CareTypeService_UpdateCareType_FullMethodName          = "/backend.proto.offering.v1.CareTypeService/UpdateCareType"
	CareTypeService_DeleteCareType_FullMethodName          = "/backend.proto.offering.v1.CareTypeService/DeleteCareType"
	CareTypeService_CreateCareTypeAttribute_FullMethodName = "/backend.proto.offering.v1.CareTypeService/CreateCareTypeAttribute"
	CareTypeService_ListCareTypeAttributes_FullMethodName  = "/backend.proto.offering.v1.CareTypeService/ListCareTypeAttributes"
	CareTypeService_DeleteCareTypeAttribute_FullMethodName = "/backend.proto.offering.v1.CareTypeService/DeleteCareTypeAttribute"
)

// CareTypeServiceClient is the client API for CareTypeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// (-- api-linter: core::0131::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0133::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0134::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0135::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// Service for managing CareType configurations .
type CareTypeServiceClient interface {
	// Creates a new care type.
	CreateCareType(ctx context.Context, in *CreateCareTypeRequest, opts ...grpc.CallOption) (*CreateCareTypeResponse, error)
	// Gets a single care type.
	GetCareType(ctx context.Context, in *GetCareTypeRequest, opts ...grpc.CallOption) (*GetCareTypeResponse, error)
	// Lists care types.
	ListCareTypes(ctx context.Context, in *ListCareTypesRequest, opts ...grpc.CallOption) (*ListCareTypesResponse, error)
	// Updates a care type.
	UpdateCareType(ctx context.Context, in *UpdateCareTypeRequest, opts ...grpc.CallOption) (*UpdateCareTypeResponse, error)
	// Deletes a care type.
	DeleteCareType(ctx context.Context, in *DeleteCareTypeRequest, opts ...grpc.CallOption) (*DeleteCareTypeResponse, error)
	// Creates a new care type attribute.
	CreateCareTypeAttribute(ctx context.Context, in *CreateCareTypeAttributeRequest, opts ...grpc.CallOption) (*CreateCareTypeAttributeResponse, error)
	// Lists care type attributes.
	ListCareTypeAttributes(ctx context.Context, in *ListCareTypeAttributesRequest, opts ...grpc.CallOption) (*ListCareTypeAttributesResponse, error)
	// Deletes a care type attribute.
	DeleteCareTypeAttribute(ctx context.Context, in *DeleteCareTypeAttributeRequest, opts ...grpc.CallOption) (*DeleteCareTypeAttributeResponse, error)
}

type careTypeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCareTypeServiceClient(cc grpc.ClientConnInterface) CareTypeServiceClient {
	return &careTypeServiceClient{cc}
}

func (c *careTypeServiceClient) CreateCareType(ctx context.Context, in *CreateCareTypeRequest, opts ...grpc.CallOption) (*CreateCareTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCareTypeResponse)
	err := c.cc.Invoke(ctx, CareTypeService_CreateCareType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *careTypeServiceClient) GetCareType(ctx context.Context, in *GetCareTypeRequest, opts ...grpc.CallOption) (*GetCareTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCareTypeResponse)
	err := c.cc.Invoke(ctx, CareTypeService_GetCareType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *careTypeServiceClient) ListCareTypes(ctx context.Context, in *ListCareTypesRequest, opts ...grpc.CallOption) (*ListCareTypesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCareTypesResponse)
	err := c.cc.Invoke(ctx, CareTypeService_ListCareTypes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *careTypeServiceClient) UpdateCareType(ctx context.Context, in *UpdateCareTypeRequest, opts ...grpc.CallOption) (*UpdateCareTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateCareTypeResponse)
	err := c.cc.Invoke(ctx, CareTypeService_UpdateCareType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *careTypeServiceClient) DeleteCareType(ctx context.Context, in *DeleteCareTypeRequest, opts ...grpc.CallOption) (*DeleteCareTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteCareTypeResponse)
	err := c.cc.Invoke(ctx, CareTypeService_DeleteCareType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *careTypeServiceClient) CreateCareTypeAttribute(ctx context.Context, in *CreateCareTypeAttributeRequest, opts ...grpc.CallOption) (*CreateCareTypeAttributeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCareTypeAttributeResponse)
	err := c.cc.Invoke(ctx, CareTypeService_CreateCareTypeAttribute_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *careTypeServiceClient) ListCareTypeAttributes(ctx context.Context, in *ListCareTypeAttributesRequest, opts ...grpc.CallOption) (*ListCareTypeAttributesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCareTypeAttributesResponse)
	err := c.cc.Invoke(ctx, CareTypeService_ListCareTypeAttributes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *careTypeServiceClient) DeleteCareTypeAttribute(ctx context.Context, in *DeleteCareTypeAttributeRequest, opts ...grpc.CallOption) (*DeleteCareTypeAttributeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteCareTypeAttributeResponse)
	err := c.cc.Invoke(ctx, CareTypeService_DeleteCareTypeAttribute_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CareTypeServiceServer is the server API for CareTypeService service.
// All implementations must embed UnimplementedCareTypeServiceServer
// for forward compatibility.
//
// (-- api-linter: core::0131::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0133::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0134::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0135::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// Service for managing CareType configurations .
type CareTypeServiceServer interface {
	// Creates a new care type.
	CreateCareType(context.Context, *CreateCareTypeRequest) (*CreateCareTypeResponse, error)
	// Gets a single care type.
	GetCareType(context.Context, *GetCareTypeRequest) (*GetCareTypeResponse, error)
	// Lists care types.
	ListCareTypes(context.Context, *ListCareTypesRequest) (*ListCareTypesResponse, error)
	// Updates a care type.
	UpdateCareType(context.Context, *UpdateCareTypeRequest) (*UpdateCareTypeResponse, error)
	// Deletes a care type.
	DeleteCareType(context.Context, *DeleteCareTypeRequest) (*DeleteCareTypeResponse, error)
	// Creates a new care type attribute.
	CreateCareTypeAttribute(context.Context, *CreateCareTypeAttributeRequest) (*CreateCareTypeAttributeResponse, error)
	// Lists care type attributes.
	ListCareTypeAttributes(context.Context, *ListCareTypeAttributesRequest) (*ListCareTypeAttributesResponse, error)
	// Deletes a care type attribute.
	DeleteCareTypeAttribute(context.Context, *DeleteCareTypeAttributeRequest) (*DeleteCareTypeAttributeResponse, error)
	mustEmbedUnimplementedCareTypeServiceServer()
}

// UnimplementedCareTypeServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCareTypeServiceServer struct{}

func (UnimplementedCareTypeServiceServer) CreateCareType(context.Context, *CreateCareTypeRequest) (*CreateCareTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCareType not implemented")
}
func (UnimplementedCareTypeServiceServer) GetCareType(context.Context, *GetCareTypeRequest) (*GetCareTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCareType not implemented")
}
func (UnimplementedCareTypeServiceServer) ListCareTypes(context.Context, *ListCareTypesRequest) (*ListCareTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCareTypes not implemented")
}
func (UnimplementedCareTypeServiceServer) UpdateCareType(context.Context, *UpdateCareTypeRequest) (*UpdateCareTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCareType not implemented")
}
func (UnimplementedCareTypeServiceServer) DeleteCareType(context.Context, *DeleteCareTypeRequest) (*DeleteCareTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCareType not implemented")
}
func (UnimplementedCareTypeServiceServer) CreateCareTypeAttribute(context.Context, *CreateCareTypeAttributeRequest) (*CreateCareTypeAttributeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCareTypeAttribute not implemented")
}
func (UnimplementedCareTypeServiceServer) ListCareTypeAttributes(context.Context, *ListCareTypeAttributesRequest) (*ListCareTypeAttributesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCareTypeAttributes not implemented")
}
func (UnimplementedCareTypeServiceServer) DeleteCareTypeAttribute(context.Context, *DeleteCareTypeAttributeRequest) (*DeleteCareTypeAttributeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCareTypeAttribute not implemented")
}
func (UnimplementedCareTypeServiceServer) mustEmbedUnimplementedCareTypeServiceServer() {}
func (UnimplementedCareTypeServiceServer) testEmbeddedByValue()                         {}

// UnsafeCareTypeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CareTypeServiceServer will
// result in compilation errors.
type UnsafeCareTypeServiceServer interface {
	mustEmbedUnimplementedCareTypeServiceServer()
}

func RegisterCareTypeServiceServer(s grpc.ServiceRegistrar, srv CareTypeServiceServer) {
	// If the following call pancis, it indicates UnimplementedCareTypeServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CareTypeService_ServiceDesc, srv)
}

func _CareTypeService_CreateCareType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCareTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CareTypeServiceServer).CreateCareType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CareTypeService_CreateCareType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CareTypeServiceServer).CreateCareType(ctx, req.(*CreateCareTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CareTypeService_GetCareType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCareTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CareTypeServiceServer).GetCareType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CareTypeService_GetCareType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CareTypeServiceServer).GetCareType(ctx, req.(*GetCareTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CareTypeService_ListCareTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCareTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CareTypeServiceServer).ListCareTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CareTypeService_ListCareTypes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CareTypeServiceServer).ListCareTypes(ctx, req.(*ListCareTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CareTypeService_UpdateCareType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCareTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CareTypeServiceServer).UpdateCareType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CareTypeService_UpdateCareType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CareTypeServiceServer).UpdateCareType(ctx, req.(*UpdateCareTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CareTypeService_DeleteCareType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCareTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CareTypeServiceServer).DeleteCareType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CareTypeService_DeleteCareType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CareTypeServiceServer).DeleteCareType(ctx, req.(*DeleteCareTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CareTypeService_CreateCareTypeAttribute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCareTypeAttributeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CareTypeServiceServer).CreateCareTypeAttribute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CareTypeService_CreateCareTypeAttribute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CareTypeServiceServer).CreateCareTypeAttribute(ctx, req.(*CreateCareTypeAttributeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CareTypeService_ListCareTypeAttributes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCareTypeAttributesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CareTypeServiceServer).ListCareTypeAttributes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CareTypeService_ListCareTypeAttributes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CareTypeServiceServer).ListCareTypeAttributes(ctx, req.(*ListCareTypeAttributesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CareTypeService_DeleteCareTypeAttribute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCareTypeAttributeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CareTypeServiceServer).DeleteCareTypeAttribute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CareTypeService_DeleteCareTypeAttribute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CareTypeServiceServer).DeleteCareTypeAttribute(ctx, req.(*DeleteCareTypeAttributeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CareTypeService_ServiceDesc is the grpc.ServiceDesc for CareTypeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CareTypeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.offering.v1.CareTypeService",
	HandlerType: (*CareTypeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCareType",
			Handler:    _CareTypeService_CreateCareType_Handler,
		},
		{
			MethodName: "GetCareType",
			Handler:    _CareTypeService_GetCareType_Handler,
		},
		{
			MethodName: "ListCareTypes",
			Handler:    _CareTypeService_ListCareTypes_Handler,
		},
		{
			MethodName: "UpdateCareType",
			Handler:    _CareTypeService_UpdateCareType_Handler,
		},
		{
			MethodName: "DeleteCareType",
			Handler:    _CareTypeService_DeleteCareType_Handler,
		},
		{
			MethodName: "CreateCareTypeAttribute",
			Handler:    _CareTypeService_CreateCareTypeAttribute_Handler,
		},
		{
			MethodName: "ListCareTypeAttributes",
			Handler:    _CareTypeService_ListCareTypeAttributes_Handler,
		},
		{
			MethodName: "DeleteCareTypeAttribute",
			Handler:    _CareTypeService_DeleteCareTypeAttribute_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/offering/v1/care_type_service.proto",
}
