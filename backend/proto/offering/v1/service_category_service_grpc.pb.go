// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/offering/v1/service_category_service.proto

package offeringpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ServiceCategoryService_ListCategories_FullMethodName = "/backend.proto.offering.v1.ServiceCategoryService/ListCategories"
	ServiceCategoryService_SaveCategories_FullMethodName = "/backend.proto.offering.v1.ServiceCategoryService/SaveCategories"
)

// ServiceCategoryServiceClient is the client API for ServiceCategoryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// category for managing Service resources.
type ServiceCategoryServiceClient interface {
	// list categories
	ListCategories(ctx context.Context, in *ListCategoriesRequest, opts ...grpc.CallOption) (*ListCategoriesResponse, error)
	// save categories
	SaveCategories(ctx context.Context, in *SaveCategoriesRequest, opts ...grpc.CallOption) (*SaveCategoriesResponse, error)
}

type serviceCategoryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceCategoryServiceClient(cc grpc.ClientConnInterface) ServiceCategoryServiceClient {
	return &serviceCategoryServiceClient{cc}
}

func (c *serviceCategoryServiceClient) ListCategories(ctx context.Context, in *ListCategoriesRequest, opts ...grpc.CallOption) (*ListCategoriesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCategoriesResponse)
	err := c.cc.Invoke(ctx, ServiceCategoryService_ListCategories_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceCategoryServiceClient) SaveCategories(ctx context.Context, in *SaveCategoriesRequest, opts ...grpc.CallOption) (*SaveCategoriesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SaveCategoriesResponse)
	err := c.cc.Invoke(ctx, ServiceCategoryService_SaveCategories_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceCategoryServiceServer is the server API for ServiceCategoryService service.
// All implementations must embed UnimplementedServiceCategoryServiceServer
// for forward compatibility.
//
// category for managing Service resources.
type ServiceCategoryServiceServer interface {
	// list categories
	ListCategories(context.Context, *ListCategoriesRequest) (*ListCategoriesResponse, error)
	// save categories
	SaveCategories(context.Context, *SaveCategoriesRequest) (*SaveCategoriesResponse, error)
	mustEmbedUnimplementedServiceCategoryServiceServer()
}

// UnimplementedServiceCategoryServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedServiceCategoryServiceServer struct{}

func (UnimplementedServiceCategoryServiceServer) ListCategories(context.Context, *ListCategoriesRequest) (*ListCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCategories not implemented")
}
func (UnimplementedServiceCategoryServiceServer) SaveCategories(context.Context, *SaveCategoriesRequest) (*SaveCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveCategories not implemented")
}
func (UnimplementedServiceCategoryServiceServer) mustEmbedUnimplementedServiceCategoryServiceServer() {
}
func (UnimplementedServiceCategoryServiceServer) testEmbeddedByValue() {}

// UnsafeServiceCategoryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceCategoryServiceServer will
// result in compilation errors.
type UnsafeServiceCategoryServiceServer interface {
	mustEmbedUnimplementedServiceCategoryServiceServer()
}

func RegisterServiceCategoryServiceServer(s grpc.ServiceRegistrar, srv ServiceCategoryServiceServer) {
	// If the following call pancis, it indicates UnimplementedServiceCategoryServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ServiceCategoryService_ServiceDesc, srv)
}

func _ServiceCategoryService_ListCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceCategoryServiceServer).ListCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceCategoryService_ListCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceCategoryServiceServer).ListCategories(ctx, req.(*ListCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceCategoryService_SaveCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceCategoryServiceServer).SaveCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceCategoryService_SaveCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceCategoryServiceServer).SaveCategories(ctx, req.(*SaveCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServiceCategoryService_ServiceDesc is the grpc.ServiceDesc for ServiceCategoryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServiceCategoryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.offering.v1.ServiceCategoryService",
	HandlerType: (*ServiceCategoryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListCategories",
			Handler:    _ServiceCategoryService_ListCategories_Handler,
		},
		{
			MethodName: "SaveCategories",
			Handler:    _ServiceCategoryService_SaveCategories_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/offering/v1/service_category_service.proto",
}
