// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/offering/v1/service_service.proto

package offeringpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ServiceService_CreateService_FullMethodName         = "/backend.proto.offering.v1.ServiceService/CreateService"
	ServiceService_GetService_FullMethodName            = "/backend.proto.offering.v1.ServiceService/GetService"
	ServiceService_UpdateService_FullMethodName         = "/backend.proto.offering.v1.ServiceService/UpdateService"
	ServiceService_DeleteService_FullMethodName         = "/backend.proto.offering.v1.ServiceService/DeleteService"
	ServiceService_ListServices_FullMethodName          = "/backend.proto.offering.v1.ServiceService/ListServices"
	ServiceService_ListAvailableServices_FullMethodName = "/backend.proto.offering.v1.ServiceService/ListAvailableServices"
	ServiceService_BatchUpdateServices_FullMethodName   = "/backend.proto.offering.v1.ServiceService/BatchUpdateServices"
	ServiceService_UpdateOBService_FullMethodName       = "/backend.proto.offering.v1.ServiceService/UpdateOBService"
	ServiceService_BatchGetServices_FullMethodName      = "/backend.proto.offering.v1.ServiceService/BatchGetServices"
)

// ServiceServiceClient is the client API for ServiceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// (-- api-linter: core::0131::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0133::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0134::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0135::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// Service for managing Service resources.
type ServiceServiceClient interface {
	// 创建一个服务
	CreateService(ctx context.Context, in *CreateServiceRequest, opts ...grpc.CallOption) (*CreateServiceResponse, error)
	// 获取服务
	GetService(ctx context.Context, in *GetServiceRequest, opts ...grpc.CallOption) (*GetServiceResponse, error)
	// 更新服务
	UpdateService(ctx context.Context, in *UpdateServiceRequest, opts ...grpc.CallOption) (*UpdateServiceResponse, error)
	// 删除服务
	DeleteService(ctx context.Context, in *DeleteServiceRequest, opts ...grpc.CallOption) (*DeleteServiceResponse, error)
	// list services
	ListServices(ctx context.Context, in *ListServicesRequest, opts ...grpc.CallOption) (*ListServicesResponse, error)
	// ListAvailableServices 查询指定 business 的可用服务列表
	//
	// 用于预约场景，支持根据 pet 特征进行服务匹配筛选。
	// 当请求中包含 pet_ids 时，系统会根据以下 pet 配置信息过滤服务：
	// - type/breed（宠物类型/品种，如: Dog/Golden Doodle, Cat/American
	// Shorthair）
	// - code（宠物编码，商家自定义的标识）
	// - weight/size（体型，如：small, medium, large, giant）
	// - coat type（毛发类型，如：short, medium, long, double coat）
	//
	// 如果未传递 pet_ids，则返回该 business 下所有可用的服务。
	ListAvailableServices(ctx context.Context, in *ListAvailableServicesRequest, opts ...grpc.CallOption) (*ListAvailableServicesResponse, error)
	// BatchUpdateServices 批量更新服务信息
	//
	// 支持批量更新服务的名称、排序值等基本信息
	BatchUpdateServices(ctx context.Context, in *BatchUpdateServicesRequest, opts ...grpc.CallOption) (*BatchUpdateServicesResponse, error)
	// 更新 ob service
	UpdateOBService(ctx context.Context, in *UpdateOBServiceRequest, opts ...grpc.CallOption) (*UpdateOBServiceResponse, error)
	// 批量获取服务
	BatchGetServices(ctx context.Context, in *BatchGetServicesRequest, opts ...grpc.CallOption) (*BatchGetServicesResponse, error)
}

type serviceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceServiceClient(cc grpc.ClientConnInterface) ServiceServiceClient {
	return &serviceServiceClient{cc}
}

func (c *serviceServiceClient) CreateService(ctx context.Context, in *CreateServiceRequest, opts ...grpc.CallOption) (*CreateServiceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateServiceResponse)
	err := c.cc.Invoke(ctx, ServiceService_CreateService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceServiceClient) GetService(ctx context.Context, in *GetServiceRequest, opts ...grpc.CallOption) (*GetServiceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceResponse)
	err := c.cc.Invoke(ctx, ServiceService_GetService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceServiceClient) UpdateService(ctx context.Context, in *UpdateServiceRequest, opts ...grpc.CallOption) (*UpdateServiceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateServiceResponse)
	err := c.cc.Invoke(ctx, ServiceService_UpdateService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceServiceClient) DeleteService(ctx context.Context, in *DeleteServiceRequest, opts ...grpc.CallOption) (*DeleteServiceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteServiceResponse)
	err := c.cc.Invoke(ctx, ServiceService_DeleteService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceServiceClient) ListServices(ctx context.Context, in *ListServicesRequest, opts ...grpc.CallOption) (*ListServicesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListServicesResponse)
	err := c.cc.Invoke(ctx, ServiceService_ListServices_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceServiceClient) ListAvailableServices(ctx context.Context, in *ListAvailableServicesRequest, opts ...grpc.CallOption) (*ListAvailableServicesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAvailableServicesResponse)
	err := c.cc.Invoke(ctx, ServiceService_ListAvailableServices_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceServiceClient) BatchUpdateServices(ctx context.Context, in *BatchUpdateServicesRequest, opts ...grpc.CallOption) (*BatchUpdateServicesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchUpdateServicesResponse)
	err := c.cc.Invoke(ctx, ServiceService_BatchUpdateServices_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceServiceClient) UpdateOBService(ctx context.Context, in *UpdateOBServiceRequest, opts ...grpc.CallOption) (*UpdateOBServiceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateOBServiceResponse)
	err := c.cc.Invoke(ctx, ServiceService_UpdateOBService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceServiceClient) BatchGetServices(ctx context.Context, in *BatchGetServicesRequest, opts ...grpc.CallOption) (*BatchGetServicesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchGetServicesResponse)
	err := c.cc.Invoke(ctx, ServiceService_BatchGetServices_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceServiceServer is the server API for ServiceService service.
// All implementations must embed UnimplementedServiceServiceServer
// for forward compatibility.
//
// (-- api-linter: core::0131::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0133::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0134::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// (-- api-linter: core::0135::response-message-name=disabled
//
//	aip.dev/not-precedent: 统一用 Response message. --)
//
// Service for managing Service resources.
type ServiceServiceServer interface {
	// 创建一个服务
	CreateService(context.Context, *CreateServiceRequest) (*CreateServiceResponse, error)
	// 获取服务
	GetService(context.Context, *GetServiceRequest) (*GetServiceResponse, error)
	// 更新服务
	UpdateService(context.Context, *UpdateServiceRequest) (*UpdateServiceResponse, error)
	// 删除服务
	DeleteService(context.Context, *DeleteServiceRequest) (*DeleteServiceResponse, error)
	// list services
	ListServices(context.Context, *ListServicesRequest) (*ListServicesResponse, error)
	// ListAvailableServices 查询指定 business 的可用服务列表
	//
	// 用于预约场景，支持根据 pet 特征进行服务匹配筛选。
	// 当请求中包含 pet_ids 时，系统会根据以下 pet 配置信息过滤服务：
	// - type/breed（宠物类型/品种，如: Dog/Golden Doodle, Cat/American
	// Shorthair）
	// - code（宠物编码，商家自定义的标识）
	// - weight/size（体型，如：small, medium, large, giant）
	// - coat type（毛发类型，如：short, medium, long, double coat）
	//
	// 如果未传递 pet_ids，则返回该 business 下所有可用的服务。
	ListAvailableServices(context.Context, *ListAvailableServicesRequest) (*ListAvailableServicesResponse, error)
	// BatchUpdateServices 批量更新服务信息
	//
	// 支持批量更新服务的名称、排序值等基本信息
	BatchUpdateServices(context.Context, *BatchUpdateServicesRequest) (*BatchUpdateServicesResponse, error)
	// 更新 ob service
	UpdateOBService(context.Context, *UpdateOBServiceRequest) (*UpdateOBServiceResponse, error)
	// 批量获取服务
	BatchGetServices(context.Context, *BatchGetServicesRequest) (*BatchGetServicesResponse, error)
	mustEmbedUnimplementedServiceServiceServer()
}

// UnimplementedServiceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedServiceServiceServer struct{}

func (UnimplementedServiceServiceServer) CreateService(context.Context, *CreateServiceRequest) (*CreateServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateService not implemented")
}
func (UnimplementedServiceServiceServer) GetService(context.Context, *GetServiceRequest) (*GetServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetService not implemented")
}
func (UnimplementedServiceServiceServer) UpdateService(context.Context, *UpdateServiceRequest) (*UpdateServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateService not implemented")
}
func (UnimplementedServiceServiceServer) DeleteService(context.Context, *DeleteServiceRequest) (*DeleteServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteService not implemented")
}
func (UnimplementedServiceServiceServer) ListServices(context.Context, *ListServicesRequest) (*ListServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServices not implemented")
}
func (UnimplementedServiceServiceServer) ListAvailableServices(context.Context, *ListAvailableServicesRequest) (*ListAvailableServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAvailableServices not implemented")
}
func (UnimplementedServiceServiceServer) BatchUpdateServices(context.Context, *BatchUpdateServicesRequest) (*BatchUpdateServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateServices not implemented")
}
func (UnimplementedServiceServiceServer) UpdateOBService(context.Context, *UpdateOBServiceRequest) (*UpdateOBServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOBService not implemented")
}
func (UnimplementedServiceServiceServer) BatchGetServices(context.Context, *BatchGetServicesRequest) (*BatchGetServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetServices not implemented")
}
func (UnimplementedServiceServiceServer) mustEmbedUnimplementedServiceServiceServer() {}
func (UnimplementedServiceServiceServer) testEmbeddedByValue()                        {}

// UnsafeServiceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceServiceServer will
// result in compilation errors.
type UnsafeServiceServiceServer interface {
	mustEmbedUnimplementedServiceServiceServer()
}

func RegisterServiceServiceServer(s grpc.ServiceRegistrar, srv ServiceServiceServer) {
	// If the following call pancis, it indicates UnimplementedServiceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ServiceService_ServiceDesc, srv)
}

func _ServiceService_CreateService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServiceServer).CreateService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceService_CreateService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServiceServer).CreateService(ctx, req.(*CreateServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceService_GetService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServiceServer).GetService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceService_GetService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServiceServer).GetService(ctx, req.(*GetServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceService_UpdateService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServiceServer).UpdateService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceService_UpdateService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServiceServer).UpdateService(ctx, req.(*UpdateServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceService_DeleteService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServiceServer).DeleteService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceService_DeleteService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServiceServer).DeleteService(ctx, req.(*DeleteServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceService_ListServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServiceServer).ListServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceService_ListServices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServiceServer).ListServices(ctx, req.(*ListServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceService_ListAvailableServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAvailableServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServiceServer).ListAvailableServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceService_ListAvailableServices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServiceServer).ListAvailableServices(ctx, req.(*ListAvailableServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceService_BatchUpdateServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServiceServer).BatchUpdateServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceService_BatchUpdateServices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServiceServer).BatchUpdateServices(ctx, req.(*BatchUpdateServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceService_UpdateOBService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOBServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServiceServer).UpdateOBService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceService_UpdateOBService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServiceServer).UpdateOBService(ctx, req.(*UpdateOBServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceService_BatchGetServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServiceServer).BatchGetServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceService_BatchGetServices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServiceServer).BatchGetServices(ctx, req.(*BatchGetServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServiceService_ServiceDesc is the grpc.ServiceDesc for ServiceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServiceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.offering.v1.ServiceService",
	HandlerType: (*ServiceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateService",
			Handler:    _ServiceService_CreateService_Handler,
		},
		{
			MethodName: "GetService",
			Handler:    _ServiceService_GetService_Handler,
		},
		{
			MethodName: "UpdateService",
			Handler:    _ServiceService_UpdateService_Handler,
		},
		{
			MethodName: "DeleteService",
			Handler:    _ServiceService_DeleteService_Handler,
		},
		{
			MethodName: "ListServices",
			Handler:    _ServiceService_ListServices_Handler,
		},
		{
			MethodName: "ListAvailableServices",
			Handler:    _ServiceService_ListAvailableServices_Handler,
		},
		{
			MethodName: "BatchUpdateServices",
			Handler:    _ServiceService_BatchUpdateServices_Handler,
		},
		{
			MethodName: "UpdateOBService",
			Handler:    _ServiceService_UpdateOBService_Handler,
		},
		{
			MethodName: "BatchGetServices",
			Handler:    _ServiceService_BatchGetServices_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/offering/v1/service_service.proto",
}
