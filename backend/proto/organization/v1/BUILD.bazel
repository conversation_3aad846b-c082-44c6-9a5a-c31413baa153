load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "organizationpb_proto",
    srcs = ["organization.proto"],
    visibility = ["//visibility:public"],
)

go_proto_library(
    name = "organizationpb_go_proto",
    importpath = "github.com/MoeGolibrary/moego/backend/proto/organization/v1",
    proto = ":organizationpb_proto",
    visibility = ["//visibility:public"],
)

go_library(
    name = "organization",
    embed = [":organizationpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/organization/v1",
    visibility = ["//visibility:public"],
)
