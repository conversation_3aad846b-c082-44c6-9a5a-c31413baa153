// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/test_account/v1/test_account_service.proto

package testaccountpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TestAccountService_BorrowTestAccount_FullMethodName   = "/backend.proto.test_account.v1.TestAccountService/BorrowTestAccount"
	TestAccountService_ReturnTestAccount_FullMethodName   = "/backend.proto.test_account.v1.TestAccountService/ReturnTestAccount"
	TestAccountService_CreateTestAccount_FullMethodName   = "/backend.proto.test_account.v1.TestAccountService/CreateTestAccount"
	TestAccountService_ReleaseTestAccounts_FullMethodName = "/backend.proto.test_account.v1.TestAccountService/ReleaseTestAccounts"
	TestAccountService_ListTestAccounts_FullMethodName    = "/backend.proto.test_account.v1.TestAccountService/ListTestAccounts"
)

// TestAccountServiceClient is the client API for TestAccountService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// test account service
type TestAccountServiceClient interface {
	// Borrow a test account
	BorrowTestAccount(ctx context.Context, in *BorrowTestAccountRequest, opts ...grpc.CallOption) (*BorrowTestAccountResponse, error)
	// Return a test account via contract_id
	ReturnTestAccount(ctx context.Context, in *ReturnTestAccountRequest, opts ...grpc.CallOption) (*ReturnTestAccountResponse, error)
	// Create a test account
	CreateTestAccount(ctx context.Context, in *CreateTestAccountRequest, opts ...grpc.CallOption) (*TestAccount, error)
	// Release test accounts
	ReleaseTestAccounts(ctx context.Context, in *ReleaseTestAccountsRequest, opts ...grpc.CallOption) (*ReleaseTestAccountsResponse, error)
	// List test accounts
	ListTestAccounts(ctx context.Context, in *ListTestAccountsRequest, opts ...grpc.CallOption) (*ListTestAccountsResponse, error)
}

type testAccountServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTestAccountServiceClient(cc grpc.ClientConnInterface) TestAccountServiceClient {
	return &testAccountServiceClient{cc}
}

func (c *testAccountServiceClient) BorrowTestAccount(ctx context.Context, in *BorrowTestAccountRequest, opts ...grpc.CallOption) (*BorrowTestAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BorrowTestAccountResponse)
	err := c.cc.Invoke(ctx, TestAccountService_BorrowTestAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testAccountServiceClient) ReturnTestAccount(ctx context.Context, in *ReturnTestAccountRequest, opts ...grpc.CallOption) (*ReturnTestAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReturnTestAccountResponse)
	err := c.cc.Invoke(ctx, TestAccountService_ReturnTestAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testAccountServiceClient) CreateTestAccount(ctx context.Context, in *CreateTestAccountRequest, opts ...grpc.CallOption) (*TestAccount, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TestAccount)
	err := c.cc.Invoke(ctx, TestAccountService_CreateTestAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testAccountServiceClient) ReleaseTestAccounts(ctx context.Context, in *ReleaseTestAccountsRequest, opts ...grpc.CallOption) (*ReleaseTestAccountsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReleaseTestAccountsResponse)
	err := c.cc.Invoke(ctx, TestAccountService_ReleaseTestAccounts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testAccountServiceClient) ListTestAccounts(ctx context.Context, in *ListTestAccountsRequest, opts ...grpc.CallOption) (*ListTestAccountsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTestAccountsResponse)
	err := c.cc.Invoke(ctx, TestAccountService_ListTestAccounts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TestAccountServiceServer is the server API for TestAccountService service.
// All implementations must embed UnimplementedTestAccountServiceServer
// for forward compatibility.
//
// test account service
type TestAccountServiceServer interface {
	// Borrow a test account
	BorrowTestAccount(context.Context, *BorrowTestAccountRequest) (*BorrowTestAccountResponse, error)
	// Return a test account via contract_id
	ReturnTestAccount(context.Context, *ReturnTestAccountRequest) (*ReturnTestAccountResponse, error)
	// Create a test account
	CreateTestAccount(context.Context, *CreateTestAccountRequest) (*TestAccount, error)
	// Release test accounts
	ReleaseTestAccounts(context.Context, *ReleaseTestAccountsRequest) (*ReleaseTestAccountsResponse, error)
	// List test accounts
	ListTestAccounts(context.Context, *ListTestAccountsRequest) (*ListTestAccountsResponse, error)
	mustEmbedUnimplementedTestAccountServiceServer()
}

// UnimplementedTestAccountServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTestAccountServiceServer struct{}

func (UnimplementedTestAccountServiceServer) BorrowTestAccount(context.Context, *BorrowTestAccountRequest) (*BorrowTestAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BorrowTestAccount not implemented")
}
func (UnimplementedTestAccountServiceServer) ReturnTestAccount(context.Context, *ReturnTestAccountRequest) (*ReturnTestAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReturnTestAccount not implemented")
}
func (UnimplementedTestAccountServiceServer) CreateTestAccount(context.Context, *CreateTestAccountRequest) (*TestAccount, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTestAccount not implemented")
}
func (UnimplementedTestAccountServiceServer) ReleaseTestAccounts(context.Context, *ReleaseTestAccountsRequest) (*ReleaseTestAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseTestAccounts not implemented")
}
func (UnimplementedTestAccountServiceServer) ListTestAccounts(context.Context, *ListTestAccountsRequest) (*ListTestAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTestAccounts not implemented")
}
func (UnimplementedTestAccountServiceServer) mustEmbedUnimplementedTestAccountServiceServer() {}
func (UnimplementedTestAccountServiceServer) testEmbeddedByValue()                            {}

// UnsafeTestAccountServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TestAccountServiceServer will
// result in compilation errors.
type UnsafeTestAccountServiceServer interface {
	mustEmbedUnimplementedTestAccountServiceServer()
}

func RegisterTestAccountServiceServer(s grpc.ServiceRegistrar, srv TestAccountServiceServer) {
	// If the following call pancis, it indicates UnimplementedTestAccountServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TestAccountService_ServiceDesc, srv)
}

func _TestAccountService_BorrowTestAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BorrowTestAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestAccountServiceServer).BorrowTestAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestAccountService_BorrowTestAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestAccountServiceServer).BorrowTestAccount(ctx, req.(*BorrowTestAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestAccountService_ReturnTestAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReturnTestAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestAccountServiceServer).ReturnTestAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestAccountService_ReturnTestAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestAccountServiceServer).ReturnTestAccount(ctx, req.(*ReturnTestAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestAccountService_CreateTestAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTestAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestAccountServiceServer).CreateTestAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestAccountService_CreateTestAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestAccountServiceServer).CreateTestAccount(ctx, req.(*CreateTestAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestAccountService_ReleaseTestAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseTestAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestAccountServiceServer).ReleaseTestAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestAccountService_ReleaseTestAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestAccountServiceServer).ReleaseTestAccounts(ctx, req.(*ReleaseTestAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestAccountService_ListTestAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTestAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestAccountServiceServer).ListTestAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestAccountService_ListTestAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestAccountServiceServer).ListTestAccounts(ctx, req.(*ListTestAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TestAccountService_ServiceDesc is the grpc.ServiceDesc for TestAccountService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TestAccountService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.test_account.v1.TestAccountService",
	HandlerType: (*TestAccountServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BorrowTestAccount",
			Handler:    _TestAccountService_BorrowTestAccount_Handler,
		},
		{
			MethodName: "ReturnTestAccount",
			Handler:    _TestAccountService_ReturnTestAccount_Handler,
		},
		{
			MethodName: "CreateTestAccount",
			Handler:    _TestAccountService_CreateTestAccount_Handler,
		},
		{
			MethodName: "ReleaseTestAccounts",
			Handler:    _TestAccountService_ReleaseTestAccounts_Handler,
		},
		{
			MethodName: "ListTestAccounts",
			Handler:    _TestAccountService_ListTestAccounts_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/test_account/v1/test_account_service.proto",
}
