package service

import (
	"context"
	"fmt"
	"time"

	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/offering"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/util"
	"github.com/samber/lo"
)

//go:generate mockgen -package=mocks -destination=mocks/mock_group_class_service.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/service GroupClassService
type GroupClassService interface {
	BatchCreate(context.Context, dto.CreateGroupClassDTO) (*model.GroupClassDetail, []*model.StaffTimeSlot, error)
	ListUncheckInPets(context.Context, dto.ListUncheckInPetsDTO) ([]dto.GroupClassAttendanceDTO, error)
	CheckInGroupClassSession(context.Context, dto.CheckInSessionDTO) ([]dto.CheckInSessionResultDTO, error)
	GetByPetAndInstance(context.Context, int64, int64) (*model.GroupClassDetail, error)
	ListGroupClassDetails(context.Context, dto.ListGroupClassDetailsDTO) ([]*model.GroupClassDetail, error)
	ListGroupClassAttendances(context.Context, dto.ListGroupClassAttendanceDTO) ([]*model.GroupClassAttendance, error)
	UpdateGroupClassSession(context.Context, dto.UpdateGroupClassSessionDTO) error
}

type groupClassService struct {
	query *query.Query

	repository            fulfillment.GroupClassDetailRepository
	fulfillmentRepository fulfillment.Repository
	timeSlotRepository    fulfillment.StaffTimeSlotRepository
	groupClassRepository  offering.GroupClassRepository
	attendanceRepository  fulfillment.GroupClassAttendanceRepository
	staffTimeSlotService  StaffTimeSlotService
}

// UpdateGroupClassSession
// 1. 先根据 instanceID 获取到 group class detail 信息
// 2. 再根据 CareType + DetailID 查询 staff time slot 信息
// 3. 过滤出 StartDatetime == beforeInterval.StartDateTime 的记录
// 4. 更新 staff time slot 的 StartDateTime 和 EndDateTime 信息
// 5. 刷新 fulfillment 信息
func (s *groupClassService) UpdateGroupClassSession(ctx context.Context, sessionDTO dto.UpdateGroupClassSessionDTO) error {
	// 1. get group class detail
	details, err := s.repository.ListByFilter(ctx, filter.ListGroupClassDetailFilter{
		GroupClassInstanceIDs: []int64{sessionDTO.GroupClassInstanceID},
	})
	if err != nil || len(details) == 0 {
		return err
	}
	detailIDs := lo.Map(details, func(detail *model.GroupClassDetail, _ int) int64 {
		return detail.ID
	})
	// 2. get staff time slot(group class session)
	timeSlots, err := s.timeSlotRepository.ListByFilter(ctx, filter.ListStaffTimeSlotFilter{
		CareTypes: []offeringpb.ServiceItemType{offeringpb.ServiceItemType_GROUP_CLASS},
		DetailIDs: detailIDs,
	})
	if err != nil || len(timeSlots) == 0 {
		return err
	}
	// 3. filter out the session to be updated
	tobeUpdated := lo.Filter(timeSlots, func(slot *model.StaffTimeSlot, _ int) bool {
		return time.Time.Equal(slot.StartDatetime, sessionDTO.BeforeInterval.StartTime.AsTime()) &&
			time.Time.Equal(slot.EndDatetime, sessionDTO.BeforeInterval.EndTime.AsTime())
	})
	updates := lo.Map(tobeUpdated, func(slot *model.StaffTimeSlot, _ int) dto.UpdateStaffTimeSlotDTO {
		return dto.UpdateStaffTimeSlotDTO{
			StaffTimeSlotID: slot.ID,
			StartDatetime:   lo.ToPtr(sessionDTO.AfterInterval.StartTime.AsTime()),
			EndDatetime:     lo.ToPtr(sessionDTO.AfterInterval.EndTime.AsTime()),
		}
	})
	// 4. update staff time slot
	if err = s.staffTimeSlotService.BatchUpdate(ctx, updates); err != nil {
		return err
	}
	return nil
}

func (s *groupClassService) ListGroupClassAttendances(ctx context.Context, listDTO dto.ListGroupClassAttendanceDTO) ([]*model.GroupClassAttendance, error) {
	return s.attendanceRepository.ListByFilter(ctx, filter.ListGroupClassAttendanceFilter{
		FulfillmentIDs:       listDTO.FulfillmentIDs,
		GroupClassSessionIDs: listDTO.GroupClassSessionIDs,
	})
}

func (s *groupClassService) ListGroupClassDetails(ctx context.Context, listDTO dto.ListGroupClassDetailsDTO) ([]*model.GroupClassDetail, error) {
	return s.repository.ListByFilter(ctx, filter.ListGroupClassDetailFilter{
		FulfillmentIDs:        listDTO.FulfillmentIDs,
		GroupClassInstanceIDs: listDTO.GroupClassInstanceIDs,
		PetIDs:                listDTO.PetIDs,
		Statuses:              listDTO.Statuses,
	})
}

func (s *groupClassService) GetByPetAndInstance(ctx context.Context, petID int64, instanceID int64) (*model.GroupClassDetail, error) {
	return s.repository.GetByPetAndInstance(ctx, petID, instanceID)
}

func (s *groupClassService) CheckInGroupClassSession(ctx context.Context, sessionDTO dto.CheckInSessionDTO) ([]dto.CheckInSessionResultDTO, error) {
	// 1. list uncheck in pets and sessions
	toBeAttendances, err := s.ListUncheckInPets(ctx, dto.ListUncheckInPetsDTO{
		CompanyID:           sessionDTO.CompanyID,
		BusinessID:          sessionDTO.BusinessID,
		GroupClassSessionID: sessionDTO.GroupClassSessionID,
		PetIDs:              sessionDTO.PetIDs,
	})
	if err != nil || len(toBeAttendances) == 0 {
		return nil, err
	}

	// 2. create group class attendance
	attendances := lo.Map(toBeAttendances, func(attendance dto.GroupClassAttendanceDTO, _ int) *model.GroupClassAttendance {
		return &model.GroupClassAttendance{
			CompanyID:           sessionDTO.CompanyID,
			BusinessID:          sessionDTO.BusinessID,
			FulfillmentID:       attendance.FulfillmentID,
			GroupClassDetailID:  attendance.GroupClassDetailID,
			GroupClassSessionID: attendance.GroupClassSessionID,
			PetID:               attendance.PetID,
			CheckInTime:         time.Now(),
		}
	})
	err = s.attendanceRepository.BatchCreate(ctx, attendances)
	if err != nil {
		return nil, err
	}
	sessionIds := lo.Uniq(lo.Map(attendances, func(attendance *model.GroupClassAttendance, _ int) int64 {
		return attendance.GroupClassSessionID
	}))
	sessions, err := s.groupClassRepository.ListSessions(ctx, filter.ListSessionsFilter{SessionIDs: sessionIds})
	if err != nil {
		return nil, err
	}
	idToSession := lo.KeyBy(sessions, func(session *offeringpb.GroupClassSession) int64 {
		return session.Id
	})

	// 3. update group class status
	detailIDs := lo.Uniq(lo.Map(attendances, func(attendance *model.GroupClassAttendance, _ int) int64 {
		return attendance.GroupClassDetailID
	}))
	fulfillmentIDs := lo.Uniq(lo.Map(attendances, func(attendance *model.GroupClassAttendance, _ int) int64 {
		return attendance.FulfillmentID
	}))
	for _, fulfillmentID := range fulfillmentIDs {
		// fulfillment 的签到记录
		fulfillmentAttendances, err := s.attendanceRepository.ListByFilter(ctx, filter.ListGroupClassAttendanceFilter{
			FulfillmentIDs: []int64{fulfillmentID},
		})
		if err != nil {
			return nil, err
		}
		// fulfillment 的课程详情
		fulfillmentSessions, err := s.timeSlotRepository.ListByFilter(ctx, filter.ListStaffTimeSlotFilter{
			FulfillmentIDs: []int64{fulfillmentID},
			CareTypes:      []offeringpb.ServiceItemType{offeringpb.ServiceItemType_GROUP_CLASS},
			DetailIDs:      detailIDs,
		})
		if err != nil {
			return nil, err
		}

		// 课程详情对应的签到记录
		detailIDToCheckedInAttendances := lo.GroupBy(fulfillmentAttendances, func(attendance *model.GroupClassAttendance) int64 {
			return attendance.GroupClassDetailID
		})
		// 课程详情对应的 sessions
		detailIDToSessions := lo.GroupBy(fulfillmentSessions, func(slot *model.StaffTimeSlot) int64 {
			return slot.DetailID
		})
		var updatedClasses []dto.UpdateGroupClassDetailDTO
		for detailID, checkedInSessions := range detailIDToCheckedInAttendances {
			classSessions := detailIDToSessions[detailID]
			if checkedInSessions == nil || classSessions == nil {
				continue
			}
			// 签到数量达到课程数量，则更新课程状态
			if len(checkedInSessions) == len(classSessions) {
				updatedClasses = append(updatedClasses, dto.UpdateGroupClassDetailDTO{
					GroupClassDetailID: detailID,
					Status:             lo.ToPtr(fulfillmentpb.GroupClassDetailModel_COMPLETED),
				})
			} else {
				updatedClasses = append(updatedClasses, dto.UpdateGroupClassDetailDTO{
					GroupClassDetailID: detailID,
					Status:             lo.ToPtr(fulfillmentpb.GroupClassDetailModel_IN_PROGRESS),
				})
			}
		}
		err = s.repository.BatchUpdateSelective(ctx, updatedClasses)
		if err != nil {
			return nil, err
		}
	}

	return lo.Map(attendances, func(attendance *model.GroupClassAttendance, _ int) dto.CheckInSessionResultDTO {
		return dto.CheckInSessionResultDTO{
			GroupClassInstanceID: idToSession[attendance.GroupClassSessionID].GroupClassInstanceId,
			GroupClassSessionID:  attendance.GroupClassSessionID,
			PetID:                attendance.PetID,
		}
	}), nil
}

func (s *groupClassService) ListUncheckInPets(ctx context.Context, listDTO dto.ListUncheckInPetsDTO) ([]dto.GroupClassAttendanceDTO, error) {
	// 1. get session date
	var sessionDate time.Time
	if listDTO.GroupClassSessionID != nil {
		session, err := s.groupClassRepository.GetSession(ctx, *listDTO.GroupClassSessionID)
		if err != nil {
			return nil, err
		}
		sessionDate = session.Interval.StartTime.AsTime()
	} else if listDTO.SessionDate != nil {
		sessionDate = *listDTO.SessionDate
	} else {
		sessionDate = time.Now()
	}

	// 2. list staff time slot
	timeSlotFilter := filter.ListStaffTimeSlotFilter{
		CompanyID:    lo.ToPtr(listDTO.CompanyID),
		BusinessIDs:  []int64{listDTO.BusinessID},
		StartTimeMin: lo.ToPtr(util.GetStartOfDay(sessionDate)),
		StartTimeMax: lo.ToPtr(util.GetEndOfDay(sessionDate)),
		CareTypes:    []offeringpb.ServiceItemType{offeringpb.ServiceItemType_GROUP_CLASS},
		PetIDs:       listDTO.PetIDs,
	}
	staffTimeSlots, err := s.timeSlotRepository.ListByFilter(ctx, timeSlotFilter)
	if err != nil {
		return nil, err
	}
	if len(staffTimeSlots) == 0 {
		return []dto.GroupClassAttendanceDTO{}, nil
	}

	// 3. list group class detail
	groupClassDetailIDs := lo.Uniq(lo.Map(staffTimeSlots, func(slot *model.StaffTimeSlot, _ int) int64 {
		return slot.DetailID
	}))
	detailFilter := filter.ListGroupClassDetailFilter{
		IDs: groupClassDetailIDs,
	}
	details, err := s.repository.ListByFilter(ctx, detailFilter)
	if err != nil {
		return nil, err
	}

	// 4. list group class sessions by instance id
	instanceIDs := lo.Uniq(lo.Map(details, func(detail *model.GroupClassDetail, _ int) int64 {
		return detail.GroupClassInstanceID
	}))
	sessions, err := s.groupClassRepository.ListSessions(ctx, filter.ListSessionsFilter{InstanceIDs: instanceIDs})
	if err != nil {
		return nil, err
	}
	// 过滤出指定日期的需要 check in 的 session
	checkInSessions := lo.Filter(sessions, func(session *offeringpb.GroupClassSession, _ int) bool {
		return session.Interval.StartTime.AsTime().Format(time.DateOnly) == sessionDate.Format(time.DateOnly)
	})
	instanceIdToSession := lo.KeyBy(checkInSessions, func(session *offeringpb.GroupClassSession) int64 {
		return session.GroupClassInstanceId
	})
	// 一个 timeslot 对应一个 session
	idToDetail := lo.KeyBy(details, func(detail *model.GroupClassDetail) int64 {
		return detail.ID
	})
	petSessions := lo.Map(staffTimeSlots, func(timeSlot *model.StaffTimeSlot, _ int) dto.GroupClassAttendanceDTO {
		detail := idToDetail[timeSlot.DetailID]
		session := instanceIdToSession[detail.GroupClassInstanceID]

		return dto.GroupClassAttendanceDTO{
			FulfillmentID:       detail.FulfillmentID,
			GroupClassDetailID:  detail.ID,
			GroupClassSessionID: session.Id,
			PetID:               detail.PetID,
		}
	})

	// 5. list group class attendance to get checked in pets
	fulfillmentIDs := lo.Uniq(lo.Map(staffTimeSlots, func(slot *model.StaffTimeSlot, _ int) int64 {
		return slot.FulfillmentID
	}))
	attendanceFilter := filter.ListGroupClassAttendanceFilter{
		FulfillmentIDs: fulfillmentIDs,
	}
	attendances, err := s.attendanceRepository.ListByFilter(ctx, attendanceFilter)
	if err != nil {
		return nil, err
	}
	checkedInPets := lo.Map(attendances, func(attendance *model.GroupClassAttendance, _ int) dto.GroupClassAttendanceDTO {
		return dto.GroupClassAttendanceDTO{
			FulfillmentID:       attendance.FulfillmentID,
			GroupClassDetailID:  attendance.GroupClassDetailID,
			GroupClassSessionID: attendance.GroupClassSessionID,
			PetID:               attendance.PetID,
		}
	})

	checkedInMap := make(map[string]bool)
	for _, item := range checkedInPets {
		checkedInMap[s.buildAttendanceKey(item)] = true
	}

	return lo.Filter(petSessions, func(item dto.GroupClassAttendanceDTO, _ int) bool {
		return !checkedInMap[s.buildAttendanceKey(item)]
	}), nil
}

func (s *groupClassService) buildAttendanceKey(item dto.GroupClassAttendanceDTO) string {
	return fmt.Sprintf("%d-%d-%d-%d", item.FulfillmentID, item.GroupClassDetailID, item.GroupClassSessionID, item.PetID)
}

func (s *groupClassService) BatchCreate(
	ctx context.Context, createDTO dto.CreateGroupClassDTO) (*model.GroupClassDetail, []*model.StaffTimeSlot, error) {
	instance := createDTO.GroupClassInstance
	if instance == nil {
		return nil, nil, nil
	}
	sessions, err := s.groupClassRepository.ListSessions(ctx, filter.ListSessionsFilter{InstanceIDs: []int64{instance.Id}})
	if err != nil {
		return nil, nil, err
	}

	// 1. create training group class detail
	groupClassDetail := &model.GroupClassDetail{
		FulfillmentID:        createDTO.FulfillmentID,
		PetID:                createDTO.PetID,
		GroupClassID:         instance.GroupClassId,
		GroupClassInstanceID: instance.Id,
		Status:               fulfillmentpb.GroupClassDetailModel_NOT_STARTED,
		CreatedAt:            lo.ToPtr(time.Now()),
		UpdatedAt:            lo.ToPtr(time.Now()),
	}
	err = s.repository.Create(ctx, groupClassDetail)
	if err != nil {
		return nil, nil, err
	}

	// 2. create staff time slot by training session
	staffTimeSlots := lo.Map(sessions, func(session *offeringpb.GroupClassSession, _ int) *model.StaffTimeSlot {
		return &model.StaffTimeSlot{
			CompanyID:     createDTO.CompanyID,
			BusinessID:    createDTO.BusinessID,
			FulfillmentID: createDTO.FulfillmentID,
			CareType:      offeringpb.ServiceItemType_GROUP_CLASS,
			DetailID:      groupClassDetail.ID,
			StaffID:       instance.StaffId,
			CustomerID:    createDTO.CustomerID,
			PetID:         createDTO.PetID,
			StartDatetime: session.Interval.StartTime.AsTime(),
			EndDatetime:   session.Interval.EndTime.AsTime(),
		}
	})
	err = s.timeSlotRepository.BatchCreate(ctx, staffTimeSlots)
	if err != nil {
		return nil, nil, err
	}

	return groupClassDetail, staffTimeSlots, nil
}

func NewGroupClassService(
	q *query.Query,
	repository fulfillment.GroupClassDetailRepository,
	fulfillmentRepository fulfillment.Repository,
	staffTimeSlotRepository fulfillment.StaffTimeSlotRepository,
	groupClassRepository offering.GroupClassRepository,
	attendanceRepository fulfillment.GroupClassAttendanceRepository,
	staffTimeSlotService StaffTimeSlotService,
) GroupClassService {
	return &groupClassService{
		query:                 q,
		repository:            repository,
		fulfillmentRepository: fulfillmentRepository,
		timeSlotRepository:    staffTimeSlotRepository,
		groupClassRepository:  groupClassRepository,
		attendanceRepository:  attendanceRepository,
		staffTimeSlotService:  staffTimeSlotService,
	}
}
