package service

import (
	"context"
	"testing"
	"time"

	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	fulfillmentMocks "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/mocks"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	offeringMocks "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/offering/mocks"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	serviceMocks "github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/mocks"
	"github.com/stretchr/testify/assert"
	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func TestGroupClassService_ListUncheckInPets_FilterPendingPayment(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mocks
	mockGroupClassDetailRepo := fulfillmentMocks.NewMockGroupClassDetailRepository(ctrl)
	mockFulfillmentRepo := fulfillmentMocks.NewMockRepository(ctrl)
	mockTimeSlotRepo := fulfillmentMocks.NewMockStaffTimeSlotRepository(ctrl)
	mockGroupClassRepo := offeringMocks.NewMockGroupClassRepository(ctrl)
	mockAttendanceRepo := fulfillmentMocks.NewMockGroupClassAttendanceRepository(ctrl)
	mockStaffTimeSlotService := serviceMocks.NewMockStaffTimeSlotService(ctrl)

	service := &groupClassService{
		repository:            mockGroupClassDetailRepo,
		fulfillmentRepository: mockFulfillmentRepo,
		timeSlotRepository:    mockTimeSlotRepo,
		groupClassRepository:  mockGroupClassRepo,
		attendanceRepository:  mockAttendanceRepo,
		staffTimeSlotService:  mockStaffTimeSlotService,
	}

	ctx := context.Background()
	sessionDate := time.Now()
	sessionID := int64(123)

	// Test data
	listDTO := dto.ListUncheckInPetsDTO{
		CompanyID:           1,
		BusinessID:          2,
		GroupClassSessionID: &sessionID,
	}

	// Mock session
	session := &offeringpb.GroupClassSession{
		Id: sessionID,
		Interval: &offeringpb.TimeInterval{
			StartTime: timestamppb.New(sessionDate),
			EndTime:   timestamppb.New(sessionDate.Add(time.Hour)),
		},
		GroupClassInstanceId: 456,
	}

	// Mock staff time slots
	staffTimeSlots := []*model.StaffTimeSlot{
		{
			ID:            1,
			FulfillmentID: 100, // This will have PENDING_PAYMENT status
			DetailID:      10,
			PetID:         1001,
		},
		{
			ID:            2,
			FulfillmentID: 200, // This will have CONFIRMED status
			DetailID:      20,
			PetID:         1002,
		},
	}

	// Mock group class details
	details := []*model.GroupClassDetail{
		{
			ID:                   10,
			FulfillmentID:        100,
			PetID:                1001,
			GroupClassInstanceID: 456,
		},
		{
			ID:                   20,
			FulfillmentID:        200,
			PetID:                1002,
			GroupClassInstanceID: 456,
		},
	}

	// Mock fulfillments - one with PENDING_PAYMENT, one with CONFIRMED
	fulfillments := []*model.Fulfillment{
		{
			ID:     100,
			Status: fulfillmentpb.Status_PENDING_PAYMENT, // This should be filtered out
		},
		{
			ID:     200,
			Status: fulfillmentpb.Status_CONFIRMED, // This should be included
		},
	}

	// Set up expectations
	mockGroupClassRepo.EXPECT().
		GetSession(ctx, sessionID).
		Return(session, nil)

	mockTimeSlotRepo.EXPECT().
		ListByFilter(ctx, gomock.Any()).
		Return(staffTimeSlots, nil)

	mockGroupClassDetailRepo.EXPECT().
		ListByFilter(ctx, gomock.Any()).
		Return(details, nil)

	mockGroupClassRepo.EXPECT().
		ListSessions(ctx, gomock.Any()).
		Return([]*offeringpb.GroupClassSession{session}, nil)

	mockFulfillmentRepo.EXPECT().
		List(ctx, gomock.Any(), nil).
		Return(fulfillments, nil)

	mockAttendanceRepo.EXPECT().
		ListByFilter(ctx, gomock.Any()).
		Return([]*model.GroupClassAttendance{}, nil)

	// Execute
	result, err := service.ListUncheckInPets(ctx, listDTO)

	// Verify
	assert.NoError(t, err)
	assert.Len(t, result, 1, "Should only return 1 pet (the one without PENDING_PAYMENT status)")
	assert.Equal(t, int64(200), result[0].FulfillmentID, "Should only include fulfillment with CONFIRMED status")
	assert.Equal(t, int64(1002), result[0].PetID, "Should only include pet 1002")
}
