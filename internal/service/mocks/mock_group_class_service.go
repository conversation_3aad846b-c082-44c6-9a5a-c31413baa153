// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/service (interfaces: GroupClassService)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	model "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	dto "github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	gomock "github.com/golang/mock/gomock"
)

// MockGroupClassService is a mock of GroupClassService interface.
type MockGroupClassService struct {
	ctrl     *gomock.Controller
	recorder *MockGroupClassServiceMockRecorder
}

// MockGroupClassServiceMockRecorder is the mock recorder for MockGroupClassService.
type MockGroupClassServiceMockRecorder struct {
	mock *MockGroupClassService
}

// NewMockGroupClassService creates a new mock instance.
func NewMockGroupClassService(ctrl *gomock.Controller) *MockGroupClassService {
	mock := &MockGroupClassService{ctrl: ctrl}
	mock.recorder = &MockGroupClassServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGroupClassService) EXPECT() *MockGroupClassServiceMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockGroupClassService) BatchCreate(arg0 context.Context, arg1 dto.CreateGroupClassDTO) (*model.GroupClassDetail, []*model.StaffTimeSlot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", arg0, arg1)
	ret0, _ := ret[0].(*model.GroupClassDetail)
	ret1, _ := ret[1].([]*model.StaffTimeSlot)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockGroupClassServiceMockRecorder) BatchCreate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockGroupClassService)(nil).BatchCreate), arg0, arg1)
}

// CheckInGroupClassSession mocks base method.
func (m *MockGroupClassService) CheckInGroupClassSession(arg0 context.Context, arg1 dto.CheckInSessionDTO) ([]dto.CheckInSessionResultDTO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckInGroupClassSession", arg0, arg1)
	ret0, _ := ret[0].([]dto.CheckInSessionResultDTO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckInGroupClassSession indicates an expected call of CheckInGroupClassSession.
func (mr *MockGroupClassServiceMockRecorder) CheckInGroupClassSession(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInGroupClassSession", reflect.TypeOf((*MockGroupClassService)(nil).CheckInGroupClassSession), arg0, arg1)
}

// GetByPetAndInstance mocks base method.
func (m *MockGroupClassService) GetByPetAndInstance(arg0 context.Context, arg1, arg2 int64) (*model.GroupClassDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByPetAndInstance", arg0, arg1, arg2)
	ret0, _ := ret[0].(*model.GroupClassDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByPetAndInstance indicates an expected call of GetByPetAndInstance.
func (mr *MockGroupClassServiceMockRecorder) GetByPetAndInstance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByPetAndInstance", reflect.TypeOf((*MockGroupClassService)(nil).GetByPetAndInstance), arg0, arg1, arg2)
}

// ListGroupClassAttendances mocks base method.
func (m *MockGroupClassService) ListGroupClassAttendances(arg0 context.Context, arg1 dto.ListGroupClassAttendanceDTO) ([]*model.GroupClassAttendance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListGroupClassAttendances", arg0, arg1)
	ret0, _ := ret[0].([]*model.GroupClassAttendance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListGroupClassAttendances indicates an expected call of ListGroupClassAttendances.
func (mr *MockGroupClassServiceMockRecorder) ListGroupClassAttendances(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListGroupClassAttendances", reflect.TypeOf((*MockGroupClassService)(nil).ListGroupClassAttendances), arg0, arg1)
}

// ListGroupClassDetails mocks base method.
func (m *MockGroupClassService) ListGroupClassDetails(arg0 context.Context, arg1 dto.ListGroupClassDetailsDTO) ([]*model.GroupClassDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListGroupClassDetails", arg0, arg1)
	ret0, _ := ret[0].([]*model.GroupClassDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListGroupClassDetails indicates an expected call of ListGroupClassDetails.
func (mr *MockGroupClassServiceMockRecorder) ListGroupClassDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListGroupClassDetails", reflect.TypeOf((*MockGroupClassService)(nil).ListGroupClassDetails), arg0, arg1)
}

// ListUncheckInPets mocks base method.
func (m *MockGroupClassService) ListUncheckInPets(arg0 context.Context, arg1 dto.ListUncheckInPetsDTO) ([]dto.GroupClassAttendanceDTO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListUncheckInPets", arg0, arg1)
	ret0, _ := ret[0].([]dto.GroupClassAttendanceDTO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListUncheckInPets indicates an expected call of ListUncheckInPets.
func (mr *MockGroupClassServiceMockRecorder) ListUncheckInPets(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUncheckInPets", reflect.TypeOf((*MockGroupClassService)(nil).ListUncheckInPets), arg0, arg1)
}

// UpdateGroupClassSession mocks base method.
func (m *MockGroupClassService) UpdateGroupClassSession(arg0 context.Context, arg1 dto.UpdateGroupClassSessionDTO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGroupClassSession", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateGroupClassSession indicates an expected call of UpdateGroupClassSession.
func (mr *MockGroupClassServiceMockRecorder) UpdateGroupClassSession(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGroupClassSession", reflect.TypeOf((*MockGroupClassService)(nil).UpdateGroupClassSession), arg0, arg1)
}
