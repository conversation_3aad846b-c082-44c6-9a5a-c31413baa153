// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/service (interfaces: StaffTimeSlotService)
//
// Generated by this command:
//
//	mockgen -package=mocks -destination=mocks/mock_staff_time_slot_service.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/service StaffTimeSlotService
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	dto "github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	gomock "go.uber.org/mock/gomock"
)

// MockStaffTimeSlotService is a mock of StaffTimeSlotService interface.
type MockStaffTimeSlotService struct {
	ctrl     *gomock.Controller
	recorder *MockStaffTimeSlotServiceMockRecorder
	isgomock struct{}
}

// MockStaffTimeSlotServiceMockRecorder is the mock recorder for MockStaffTimeSlotService.
type MockStaffTimeSlotServiceMockRecorder struct {
	mock *MockStaffTimeSlotService
}

// NewMockStaffTimeSlotService creates a new mock instance.
func NewMockStaffTimeSlotService(ctrl *gomock.Controller) *MockStaffTimeSlotService {
	mock := &MockStaffTimeSlotService{ctrl: ctrl}
	mock.recorder = &MockStaffTimeSlotServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStaffTimeSlotService) EXPECT() *MockStaffTimeSlotServiceMockRecorder {
	return m.recorder
}

// BatchUpdate mocks base method.
func (m *MockStaffTimeSlotService) BatchUpdate(arg0 context.Context, arg1 []dto.UpdateStaffTimeSlotDTO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdate", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdate indicates an expected call of BatchUpdate.
func (mr *MockStaffTimeSlotServiceMockRecorder) BatchUpdate(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdate", reflect.TypeOf((*MockStaffTimeSlotService)(nil).BatchUpdate), arg0, arg1)
}
