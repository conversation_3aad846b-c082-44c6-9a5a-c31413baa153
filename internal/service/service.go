package service

import (
	"context"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/go-lib/merror"
	"github.com/MoeGolibrary/go-lib/zlog"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/clients"
	"github.com/MoeGolibrary/moego-svc-offering/internal/constant"
	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
	"github.com/MoeGolibrary/moego-svc-offering/internal/utils"
)

type ServiceHandler interface {
	CreateNewService(ctx context.Context, companyId int64, serviceDTO *do.ServiceDO) (int64, error)
	GetServiceWithFilterPaginated(ctx context.Context, companyId int64, filter do.ServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest, orderBy offeringpb.ServiceOrderByType) (int64, []*do.ServiceDO, error)
	CreateCategories(ctx context.Context, categories []*do.Category) ([]*do.Category, error)
	GetCategoryWithFilter(ctx context.Context, companyId int64, filter do.CategoryQueryFilter) ([]*do.Category, error)
	UpdateService(ctx context.Context, companyId int64, serviceUpdateOpt *do.ServiceUpdateOpt) error
	GetServiceListWithOverrideRules(ctx context.Context, companyId int64, businessId *int64, petIdWithServiceIdListMap map[int64][]int64) (map[int64][]do.ServiceWithOverrideRules, error)
	GetPetCustomizedServiceList(ctx context.Context, companyId, petId int64) ([]do.ServiceWithPetCustomized, error)
	GetApplicableServices(ctx context.Context, companyId, petId int64, serviceType offeringpb.ServiceType, filter *do.ApplicableServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest) (int64, []do.ServiceWithOverrideRules, error)
	OverrideServiceByPet(ctx context.Context, companyId int64, serviceWithPetOverride map[int64][]do.PetOverrideRule) error
	GetServiceBriefListByIds(ctx context.Context, serviceIds []int64) ([]do.ServiceBrief, error)
	GetSupportedServiceItemTypes(ctx context.Context, companyId int64) ([]offeringpb.ServiceItemType, error)
	GetServiceDetail(ctx context.Context, serviceId int64) (*do.ServiceDO, error)
	BatchGetOverriddenServices(ctx context.Context, companyId int64, serviceIdWithOverrideConditions map[int64]do.ServiceOverrideConditions) ([]do.ServiceWithOverrideRules, error)

	//service by staff
	BatchCreateAvailabilityStaff(ctx context.Context, serviceId int64, staffIdList []int64) error
	BatchCreateStaffOverrideRule(ctx context.Context, companyId, serviceId int64, staffOverrideRuleList []do.StaffOverrideRule) error
	GetAvailAbleStaffList(ctx context.Context, serviceId int64) ([]int64, error)
	addServiceStaffInfo(ctx context.Context, companyId int64, serviceList []*do.ServiceDO) ([]*do.ServiceDO, error)

	GetServiceBundleSaleList(ctx context.Context, companyId int64, serviceIds []int64) ([]*models.ServiceBundleSaleMapping, error)
	GetMaxServicePriceByLodgingType(ctx context.Context, companyId int64, businessId int64, lodgingTypeId int64) (*money.Money, error)
}

type serviceHandler struct {
	serviceRepository                     repository.ServiceRepository
	categoryRepository                    repository.CategoryRepository
	serviceStaffAvailabilityRepository    repository.ServiceStaffAvailabilityRepository
	serviceStaffOverrideRuleRepository    repository.ServiceStaffOverrideRuleRepository
	serviceBusinessOverrideRuleRepository repository.ServiceBusinessOverrideRuleRepository
	servicePetOverrideRuleRepository      repository.ServicePetOverrideRuleRepository
	servicePetCodeAvailabilityRepository  repository.ServicePetCodeAvailabilityRepository
	serviceBundleSaleRepository           repository.ServiceBundleSaleMappingRepository
	groupClassInstanceRepository          repository.GroupClassInstanceRepository

	pricingRuleRecordHandler PricingRuleRecordHandler

	staffServiceClient   clients.StaffClient
	companyServiceClient clients.CompanyClient

	offeringDB *gorm.DB
}

func (h *serviceHandler) BatchGetOverriddenServices(ctx context.Context, companyId int64, serviceIdWithOverrideConditions map[int64]do.ServiceOverrideConditions) ([]do.ServiceWithOverrideRules, error) {
	// list service base info from company setting
	serviceIds := lo.Keys(serviceIdWithOverrideConditions)
	basicServiceList, err := h.serviceRepository.GetBriefServiceListWithServiceIds(ctx, serviceIds)
	if err != nil {
		return nil, err
	}

	// query location override rules
	serviceIdWithBusinessOverrideConditions := make(map[int64][]do.BusinessOverrideCondition)
	for serviceId, conditions := range serviceIdWithOverrideConditions {
		if len(conditions.BusinessOverrideConditions) == 0 {
			continue
		}
		serviceIdWithBusinessOverrideConditions[serviceId] = conditions.BusinessOverrideConditions
	}
	serviceBusinessOverrideRules, err := h.serviceBusinessOverrideRuleRepository.ListServiceBusinessOverrideRules(ctx, companyId, serviceIdWithBusinessOverrideConditions)
	if err != nil {
		return nil, err
	}

	// query location & staff override rules(only when businessId and staffId is set)
	serviceIdWithStaffOverrideConditions := make(map[int64][]do.StaffOverrideCondition)
	for serviceId, conditions := range serviceIdWithOverrideConditions {
		if len(conditions.StaffOverrideConditions) == 0 {
			continue
		}
		serviceIdWithStaffOverrideConditions[serviceId] = conditions.StaffOverrideConditions
	}
	serviceStaffOverrideRules, err := h.serviceStaffOverrideRuleRepository.ListStaffOverrideRules(ctx, companyId, serviceIdWithStaffOverrideConditions)
	if err != nil {
		return nil, err
	}

	// query location & staff override rules(only when businessId and staffId is set)
	serviceIdWithPetOverrideConditions := make(map[int64][]do.PetOverrideCondition)
	for serviceId, conditions := range serviceIdWithOverrideConditions {
		if len(conditions.PetOverrideConditions) == 0 {
			continue
		}
		serviceIdWithPetOverrideConditions[serviceId] = conditions.PetOverrideConditions
	}
	servicePetOverrideRules, err := h.servicePetOverrideRuleRepository.ListServicePetOverrideRules(ctx, companyId, serviceIdWithPetOverrideConditions)
	if err != nil {
		return nil, err
	}

	// group by service id from basicServiceList
	basicServiceMap := lo.SliceToMap(basicServiceList, func(service do.ServiceBrief) (int64, do.ServiceBrief) {
		return service.ServiceId, service
	})

	// query zone by pricing rules
	serviceIdWithAddressOverrideConditions := make(map[int64][]do.AddressOverrideCondition)
	for serviceId, conditions := range serviceIdWithOverrideConditions {
		if conditions.AddressOverrideCondition == nil {
			continue
		}
		// fill in service type and item type
		for i := range conditions.AddressOverrideCondition {
			conditions.AddressOverrideCondition[i].ServiceType = basicServiceMap[serviceId].ServiceType
			conditions.AddressOverrideCondition[i].ServiceItemType = basicServiceMap[serviceId].ServiceItemType
		}
		serviceIdWithAddressOverrideConditions[serviceId] = conditions.AddressOverrideCondition
	}
	serviceAddressOverrideRules, err := h.pricingRuleRecordHandler.ListServiceAddressOverrideRules(ctx, companyId, serviceIdWithAddressOverrideConditions)
	if err != nil {
		return nil, err
	}

	result := make([]do.ServiceWithOverrideRules, 0)
	for _, basicService := range basicServiceList {
		result = append(result, converter.ConvertToServiceWithOverrideRules(basicService, serviceBusinessOverrideRules[basicService.ServiceId], servicePetOverrideRules[basicService.ServiceId], serviceStaffOverrideRules[basicService.ServiceId], serviceAddressOverrideRules[basicService.ServiceId]))
	}
	return result, nil
}

func (h *serviceHandler) GetServiceDetail(ctx context.Context, serviceId int64) (*do.ServiceDO, error) {
	serviceDetail, err := h.serviceRepository.GetServiceDetail(ctx, serviceId)
	if err != nil {
		zlog.Error(ctx, "failed to get service detail", zap.Error(err))
		return nil, err
	}

	serviceDetail.Availability.StaffOverrideList, err = h.serviceStaffOverrideRuleRepository.GetStaffOverrideRulesByServiceId(ctx, serviceId)
	if err != nil {
		return nil, err
	}

	serviceDetail.Availability.AvailableStaffIdList, err = h.serviceStaffAvailabilityRepository.GetAvailableStaffsByServiceID(ctx, serviceId)
	if err != nil {
		return nil, err
	}

	return serviceDetail, nil
}

func (h *serviceHandler) GetSupportedServiceItemTypes(ctx context.Context, companyId int64) ([]offeringpb.ServiceItemType, error) {
	services, err := h.serviceRepository.GetAllBriefServices(ctx, companyId)
	if err != nil {
		zlog.Error(ctx, "failed to get normal service item types", zap.Error(err))
		return nil, err
	}
	supportedServiceItemTypes := lo.Uniq(lo.Map(services, func(service do.ServiceBrief, _ int) offeringpb.ServiceItemType {
		return service.ServiceItemType
	}))

	return supportedServiceItemTypes, err
}

func (h *serviceHandler) OverrideServiceByPet(ctx context.Context, companyId int64, serviceWithPetOverride map[int64][]do.PetOverrideRule) error {
	return h.serviceRepository.OverrideServiceByPet(ctx, companyId, serviceWithPetOverride)
}

func (h *serviceHandler) GetApplicableServices(ctx context.Context, companyId, petId int64, serviceType offeringpb.ServiceType, filter *do.ApplicableServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest) (int64, []do.ServiceWithOverrideRules, error) {
	switch serviceType {
	case offeringpb.ServiceType_SERVICE:
		return h.getServices(ctx, companyId, petId, filter, paginationRequest)
	case offeringpb.ServiceType_ADDON:
		return h.getAddons(ctx, companyId, petId, filter, paginationRequest)
	default:
		return 0, nil, status.Error(codes.InvalidArgument, "invalid service type")
	}
}

func (h *serviceHandler) getServices(ctx context.Context, companyId int64, petId int64, filter *do.ApplicableServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest) (int64, []do.ServiceWithOverrideRules, error) {
	var serviceWithOverrideRules []do.ServiceWithOverrideRules
	var total int64
	var err error
	total, serviceWithOverrideRules, err = h.serviceRepository.GetApplicableService(ctx, companyId, petId, filter, paginationRequest)

	if err != nil {
		zlog.Error(ctx, "failed to get applicable services", zap.Error(err))
		return total, nil, err
	}

	// get service staff override rules
	serviceIds := lo.Map(serviceWithOverrideRules, func(service do.ServiceWithOverrideRules, _ int) int64 {
		return service.ServiceID
	})
	staffOverrideRulesByServiceId, err := h.serviceStaffOverrideRuleRepository.GetStaffOverrideRules(ctx, companyId, filter.BusinessId, serviceIds)
	if err != nil {
		zlog.Error(ctx, "failed to get staff override rules", zap.Error(err))
		return total, serviceWithOverrideRules, err
	}

	allStaffIdList, err := h.staffServiceClient.ListAllStaffIds(ctx, companyId, filter.BusinessId)
	if err != nil {
		zlog.Error(ctx, "failed to get all staff list", zap.Error(err))
		return total, serviceWithOverrideRules, err
	}

	serviceAvailableStaffDo, err := h.serviceStaffAvailabilityRepository.GetAvailableStaffRulesByServiceIDList(ctx, serviceIds)
	if err != nil {
		return total, serviceWithOverrideRules, err
	}
	// Group serviceAvailableStaffDo by serviceId to get map[int64]do.ServiceStaffAvailabilityDO
	serviceWithStaffIdListMap := lo.GroupBy(serviceAvailableStaffDo, func(item do.ServiceStaffAvailabilityDO) int64 {
		return item.ServiceId
	})

	// get available staff id list
	for i := range serviceWithOverrideRules {
		var availableStaffIdList []int64
		if serviceWithOverrideRules[i].AvailableForAllStaff {
			availableStaffIdList = allStaffIdList
		} else {
			availableStaffIdList = lo.Map(serviceWithStaffIdListMap[serviceWithOverrideRules[i].ServiceID], func(item do.ServiceStaffAvailabilityDO, _ int) int64 {
				return item.StaffId
			})
		}
		serviceWithOverrideRules[i].AvailableStaffIdList = availableStaffIdList

		// get staff override rules
		serviceWithOverrideRules[i].StaffOverrideRule = staffOverrideRulesByServiceId[serviceWithOverrideRules[i].ServiceID]
	}

	// query zone by pricing rules
	if filter.Zipcode != nil && *filter.Zipcode != "" && filter.Coordinate != nil {
		serviceIdWithAddressOverrideConditions := make(map[int64][]do.AddressOverrideCondition)
		for _, service := range serviceWithOverrideRules {
			serviceIdWithAddressOverrideConditions[service.ServiceID] = []do.AddressOverrideCondition{
				{
					Zipcode:         *filter.Zipcode,
					Coordinate:      filter.Coordinate,
					ServiceType:     service.ServiceType,
					ServiceItemType: service.ServiceItemType,
				},
			}
		}
		serviceAddressOverrideRules, err := h.pricingRuleRecordHandler.ListServiceAddressOverrideRules(ctx, companyId, serviceIdWithAddressOverrideConditions)
		if err != nil {
			zlog.Error(ctx, "failed to get service address override rules", zap.Error(err))
			return total, serviceWithOverrideRules, err
		}

		// apply address override rules to services
		for i := range serviceWithOverrideRules {
			if addressRules, exists := serviceAddressOverrideRules[serviceWithOverrideRules[i].ServiceID]; exists {
				serviceWithOverrideRules[i].AddressOverrideRules = addressRules
			}
		}
	}

	serviceWithOverrideRules, err = h.addServiceBundleSaleForOverride(ctx, companyId, serviceWithOverrideRules)
	if err != nil {
		zlog.Error(ctx, "failed to add service bundle sale", zap.Error(err))
		return total, serviceWithOverrideRules, err
	}

	return total, serviceWithOverrideRules, err
}

func (h *serviceHandler) getAddons(ctx context.Context, companyId int64, petId int64, filter *do.ApplicableServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest) (int64, []do.ServiceWithOverrideRules, error) {
	var serviceWithOverrideRules []do.ServiceWithOverrideRules
	var total int64
	var err error
	total, serviceWithOverrideRules, err = h.serviceRepository.GetApplicableAddon(ctx, companyId, petId, filter, paginationRequest)

	if err != nil {
		zlog.Error(ctx, "failed to get applicable services", zap.Error(err))
		return total, nil, err
	}

	// query zone by pricing rules
	if filter.Zipcode != nil && *filter.Zipcode != "" && filter.Coordinate != nil {
		serviceIdWithAddressOverrideConditions := make(map[int64][]do.AddressOverrideCondition)
		for _, service := range serviceWithOverrideRules {
			serviceIdWithAddressOverrideConditions[service.ServiceID] = []do.AddressOverrideCondition{
				{
					Zipcode:         *filter.Zipcode,
					Coordinate:      filter.Coordinate,
					ServiceType:     service.ServiceType,
					ServiceItemType: service.ServiceItemType,
				},
			}
		}
		serviceAddressOverrideRules, err := h.pricingRuleRecordHandler.ListServiceAddressOverrideRules(ctx, companyId, serviceIdWithAddressOverrideConditions)
		if err != nil {
			zlog.Error(ctx, "failed to get service address override rules", zap.Error(err))
			return total, serviceWithOverrideRules, err
		}

		// apply address override rules to services
		for i := range serviceWithOverrideRules {
			if addressRules, exists := serviceAddressOverrideRules[serviceWithOverrideRules[i].ServiceID]; exists {
				serviceWithOverrideRules[i].AddressOverrideRules = addressRules
			}
		}
	}

	return total, serviceWithOverrideRules, err
}

func (h *serviceHandler) GetServiceBriefListByIds(ctx context.Context, serviceIds []int64) ([]do.ServiceBrief, error) {
	return h.serviceRepository.GetBriefServiceListWithServiceIds(ctx, serviceIds)
}

func (h *serviceHandler) GetPetCustomizedServiceList(ctx context.Context, companyId, petId int64) ([]do.ServiceWithPetCustomized, error) {
	customizedRecords, err := h.serviceRepository.GetCustomizedServiceList(ctx, companyId, petId)
	if err != nil {
		zlog.Error(ctx, "failed to get customized service list", zap.Error(err))
		return nil, err
	}
	if len(customizedRecords) == 0 {
		return nil, nil
	}
	customizedRecordsByServiceId := lo.GroupBy(customizedRecords, func(record do.PetCustomizedRecord) int64 {
		return record.ServiceId
	})
	serviceIdList := lo.Keys(customizedRecordsByServiceId)

	serviceList, err := h.serviceRepository.GetBriefServiceListWithServiceIds(ctx, serviceIdList)
	if err != nil {
		zlog.Error(ctx, "failed to get service list", zap.Error(err))
		return nil, err
	}

	return lo.Map(serviceList, func(service do.ServiceBrief, _ int) do.ServiceWithPetCustomized {
		serviceWithPetCustomized := do.ServiceWithPetCustomized{
			ServiceBrief: service,
		}
		for _, record := range customizedRecordsByServiceId[service.ServiceId] {
			if record.SaveType == constant.SaveTypePrice {
				serviceWithPetCustomized.CustomizedPrice = record.Price
			} else {
				serviceWithPetCustomized.CustomizedDuration = record.Duration
			}
		}
		return serviceWithPetCustomized
	}), nil
}

func (h *serviceHandler) GetServiceListWithOverrideRules(ctx context.Context, companyId int64, businessId *int64, petIdWithServiceIdListMap map[int64][]int64) (map[int64][]do.ServiceWithOverrideRules, error) {
	serviceIdWithPetIdList := make(map[int64][]int64)
	for petId, serviceIdList := range petIdWithServiceIdListMap {
		for _, serviceId := range serviceIdList {
			serviceIdWithPetIdList[serviceId] = append(serviceIdWithPetIdList[serviceId], petId)
		}
	}
	_, serviceList, err := h.serviceRepository.GetServiceWithOverrideRule(ctx, companyId, businessId, serviceIdWithPetIdList)
	if err != nil {
		zlog.Error(ctx, "failed to get service list with override rules", zap.Error(err))
		return nil, err
	}
	serviceMapWithServiceId := lo.SliceToMap(serviceList, func(service do.ServiceWithOverrideRules) (int64, do.ServiceWithOverrideRules) {
		return service.ServiceID, service
	})

	serviceWithOverrideRulesMap := make(map[int64][]do.ServiceWithOverrideRules)
	for petId, serviceIdList := range petIdWithServiceIdListMap {
		for _, serviceId := range serviceIdList {
			serviceWithOverrideRulesMap[petId] = append(serviceWithOverrideRulesMap[petId], serviceMapWithServiceId[serviceId])
		}
	}
	return serviceWithOverrideRulesMap, nil
}

func (h *serviceHandler) UpdateService(ctx context.Context, companyID int64, serviceUpdateOpt *do.ServiceUpdateOpt) error {
	// validate
	if err := h.validateServiceToUpdate(ctx, companyID, serviceUpdateOpt); err != nil {
		zlog.Error(ctx, "failed to validate service update", zap.Error(err))
		return err
	}

	// update service
	if err := h.serviceRepository.UpdateService(ctx, companyID, serviceUpdateOpt); err != nil {
		zlog.Error(ctx, "failed to update service", zap.Error(err))
		return err
	}

	if _, err := h.serviceStaffAvailabilityRepository.UpdateServiceStaffAvailability(ctx, serviceUpdateOpt.ServiceID, serviceUpdateOpt.Availability.AvailableStaffIdList); err != nil {
		zlog.Error(ctx, "failed to update service staff availability", zap.Error(err))
		return err
	}

	if _, err := h.serviceStaffOverrideRuleRepository.UpdateServiceStaffOverrideRules(ctx, companyID, serviceUpdateOpt.ServiceID, serviceUpdateOpt.Availability.StaffOverrideList); err != nil {
		zlog.Error(ctx, "failed to update service staff override rule", zap.Error(err))
		return err
	}

	if serviceUpdateOpt.Availability.PetCodeFilter != nil {
		if err := h.servicePetCodeAvailabilityRepository.Upsert(ctx, nil, converter.ConvertPetCodeAvailabilityToDB(serviceUpdateOpt.ServiceID, serviceUpdateOpt.Availability.PetCodeFilter)); err != nil {
			zlog.Error(ctx, "failed to create service pet code filter", zap.Error(err))
			return err
		}
	}

	if len(serviceUpdateOpt.BundleServiceIds) > 0 {
		// Start a transaction for bundle sale operations
		err := h.offeringDB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			if err := h.serviceBundleSaleRepository.Delete(ctx, tx, companyID, serviceUpdateOpt.ServiceID); err != nil {
				zlog.Error(ctx, "failed to delete service bundle sale", zap.Error(err))
				return err
			}
			if err := h.serviceBundleSaleRepository.BatchInsert(ctx, tx, companyID, serviceUpdateOpt.ServiceID, serviceUpdateOpt.BundleServiceIds); err != nil {
				zlog.Error(ctx, "failed to create service bundle sale", zap.Error(err))
				return err
			}
			return nil
		})
		if err != nil {
			return err
		}
	} else if len(serviceUpdateOpt.BundleServiceIds) == 0 {
		// 如果传入了空的 BundleServiceIds，说明需要清空，执行 delete
		err := h.offeringDB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			if err := h.serviceBundleSaleRepository.Delete(ctx, tx, companyID, serviceUpdateOpt.ServiceID); err != nil {
				zlog.Error(ctx, "failed to delete service bundle sale", zap.Error(err))
				return err
			}
			return nil
		})
		if err != nil {
			return err
		}
	}

	return nil
}

func (h *serviceHandler) validateServiceToUpdate(ctx context.Context, companyId int64, serviceUpdateOpt *do.ServiceUpdateOpt) error {
	serviceId := serviceUpdateOpt.ServiceID

	// validate service exists
	existService, err := h.serviceRepository.GetService(ctx, serviceId)
	if err != nil {
		if errors.Is(err, repository.ErrRecordNotFound) {
			zlog.Warn(ctx, "service not found", zap.Int64("serviceID", serviceId))
			return status.Error(codes.NotFound, "service not found")
		}
		zlog.Error(ctx, "failed to get service list", zap.Error(err))
		return err
	}
	if existService.CompanyID != companyId {
		zlog.Warn(ctx, "service not found", zap.Int64("serviceID", serviceId))
		return status.Error(codes.NotFound, "service not found")
	}

	// validate category id exists
	if serviceUpdateOpt.CategoryId != nil && *serviceUpdateOpt.CategoryId > 0 {
		categoryId := *serviceUpdateOpt.CategoryId
		serviceCategory, err := h.categoryRepository.GetCategory(ctx, categoryId)
		if err != nil {
			if errors.Is(err, repository.ErrRecordNotFound) {
				zlog.Warn(ctx, "service category not found", zap.Int64("categoryID", categoryId), zap.Error(err))
				return status.Error(codes.NotFound, "service category not found")
			}
			zlog.Error(ctx, "failed to get service category", zap.Int64("categoryID", categoryId), zap.Error(err))
			return status.Error(codes.Internal, "failed to get service category")
		}
		if serviceCategory.CompanyID != companyId {
			zlog.Warn(ctx, "service category not found", zap.Int64("categoryID", categoryId), zap.Error(err))
			return status.Error(codes.NotFound, "service category not found")
		}
	}

	// validate service name not exists
	if serviceUpdateOpt.Name != nil && !strings.EqualFold(*serviceUpdateOpt.Name, existService.Name) {
		newName := *serviceUpdateOpt.Name
		exist, err := h.serviceRepository.IsServiceNameExist(ctx, companyId, do.NewUniqueService(*serviceUpdateOpt.Name, existService.GetServiceType(), existService.GetServiceItemType()))
		if err != nil {
			zlog.Error(ctx, "failed to check service name", zap.String("serviceName", newName), zap.Error(err))
			return status.Error(codes.Internal, "failed to check service name")
		}
		if exist {
			zlog.Warn(ctx, "service name already exists", zap.String("serviceName", newName))
			return status.Error(codes.AlreadyExists, "service name already exists")

		}
	}

	return nil

}

func (h *serviceHandler) CreateCategories(ctx context.Context, categories []*do.Category) ([]*do.Category, error) {
	categories, err := h.categoryRepository.CreateCategories(ctx, categories)
	if err != nil {
		zlog.Error(ctx, "failed to create categories", zap.Error(err))
		return nil, err
	}
	return categories, nil
}

func (h *serviceHandler) GetCategoryWithFilter(ctx context.Context, companyId int64, filter do.CategoryQueryFilter) ([]*do.Category, error) {
	categoryList, err := h.categoryRepository.GetCategoryWithFilter(ctx, companyId, filter)
	if err != nil {
		zlog.Error(ctx, "failed to get category list", zap.Error(err))
		return nil, err
	}

	return categoryList, nil
}

func (h *serviceHandler) GetServiceWithFilterPaginated(ctx context.Context, companyId int64, filter do.ServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest, orderBy offeringpb.ServiceOrderByType) (int64, []*do.ServiceDO, error) {
	// get services
	total, serviceList, err := h.serviceRepository.GetServicesWithFilterPaginated(ctx, companyId, filter, paginationRequest, orderBy)
	if err != nil {
		zlog.Error(ctx, "failed to get service list", zap.Error(err))
		return 0, nil, err
	}

	if len(serviceList) > 0 {
		serviceList, err = h.addServiceStaffInfo(ctx, companyId, serviceList)
		if err != nil {
			zlog.Error(ctx, "failed to add service staff override rules", zap.Error(err))
			return total, serviceList, err
		}

		serviceList, err = h.addServicePetCodeFilter(ctx, serviceList)
		if err != nil {
			zlog.Error(ctx, "failed to add service pet code filter", zap.Error(err))
			return total, serviceList, err
		}

		serviceList, err = h.addServiceBundleSale(ctx, companyId, serviceList)
		if err != nil {
			zlog.Error(ctx, "failed to add service bundle sale", zap.Error(err))
			return total, serviceList, err
		}
	}

	return total, serviceList, nil
}

func (h *serviceHandler) CreateNewService(ctx context.Context, companyId int64, serviceDTO *do.ServiceDO) (int64, error) {
	// valid
	if err := h.validateService(ctx, companyId, serviceDTO); err != nil {
		zlog.Error(ctx, "failed to validate service", zap.Error(err))
		return 0, err
	}

	// create service
	serviceId, err := h.serviceRepository.AddNewService(ctx, companyId, serviceDTO)
	if err != nil {
		zlog.Error(ctx, "failed to create service", zap.Error(err))
		return 0, err
	}

	if len(serviceDTO.Availability.AvailableStaffIdList) > 0 {
		if err := h.BatchCreateAvailabilityStaff(ctx, serviceId, serviceDTO.Availability.AvailableStaffIdList); err != nil {
			zlog.Error(ctx, "failed to create service staff availability", zap.Error(err))
			return serviceId, err
		}
	}

	if len(serviceDTO.Availability.StaffOverrideList) > 0 {
		if err := h.BatchCreateStaffOverrideRule(ctx, companyId, serviceId, serviceDTO.Availability.StaffOverrideList); err != nil {
			zlog.Error(ctx, "failed to create service staff override rule", zap.Error(err))
			return serviceId, err
		}
	}

	// TODO: update in a transaction
	if serviceDTO.Availability.PetCodeFilter != nil {
		if err := h.servicePetCodeAvailabilityRepository.Upsert(ctx, nil, converter.ConvertPetCodeAvailabilityToDB(serviceId, serviceDTO.Availability.PetCodeFilter)); err != nil {
			zlog.Error(ctx, "failed to create service pet code filter", zap.Error(err))
			return serviceId, err
		}
	}

	if len(serviceDTO.BundleServiceIds) > 0 {
		// Start a transaction for bundle sale operations
		err := h.offeringDB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			if err := h.serviceBundleSaleRepository.Delete(ctx, tx, companyId, serviceId); err != nil {
				zlog.Error(ctx, "failed to delete service bundle sale", zap.Error(err))
				return err
			}
			if err := h.serviceBundleSaleRepository.BatchInsert(ctx, tx, companyId, serviceId, serviceDTO.BundleServiceIds); err != nil {
				zlog.Error(ctx, "failed to create service bundle sale", zap.Error(err))
				return err
			}
			return nil
		})
		if err != nil {
			return serviceId, err
		}
	}

	return serviceId, nil
}

func (h *serviceHandler) validateService(ctx context.Context, companyID int64, serviceDTO *do.ServiceDO) error {
	// validate params for availability
	if err := serviceDTO.ValidateParams(); err != nil {
		return err
	}

	// validate category id exists
	if serviceDTO.CategoryId > 0 {
		serviceCategory, err := h.categoryRepository.GetCategoryByID(ctx, serviceDTO.CategoryId)
		if err != nil {
			zlog.Error(ctx, "failed to get service category", zap.Int64("categoryID", serviceDTO.CategoryId), zap.Error(err))
			return status.Error(codes.Internal, "failed to get service category")
		}
		if serviceCategory.CompanyID != companyID {
			zlog.Warn(ctx, "service category not found", zap.Int64("categoryID", serviceDTO.CategoryId), zap.Error(err))
			return status.Error(codes.NotFound, "service category not found")
		}
	}

	// validate service name not exists
	exist, err := h.serviceRepository.IsServiceNameExist(ctx, companyID, serviceDTO)
	if err != nil {
		zlog.Error(ctx, "failed to check service name", zap.String("serviceName", serviceDTO.Name), zap.Error(err))
		return status.Error(codes.Internal, "failed to check service name")
	}
	if exist {
		zlog.Warn(ctx, "service name already exists", zap.String("serviceName", serviceDTO.Name))
		return status.Error(codes.AlreadyExists, "service name already exists")
	}

	// validate target service id in auto rollover rule
	if serviceDTO.AutoRolloverRule != nil && serviceDTO.AutoRolloverRule.Enabled {
		exist, err := h.serviceRepository.IsServiceIdInCompany(ctx, serviceDTO.AutoRolloverRule.TargetServiceId, companyID, false)
		if err != nil {
			return err
		}
		if !exist {
			return merror.NewBizError(errorspb.Code_CODE_PARAMS_ERROR, "invalid auto rollover rule: target service id not in company")
		}
	}

	//// validate staff id exist
	//if serviceDTO.Availability.AvailableStaffIdList != nil && len(serviceDTO.Availability.AvailableStaffIdList) > 0 {
	//	exist, err := h.serviceRepository
	//}

	return nil
}

func (h *serviceHandler) BatchCreateAvailabilityStaff(ctx context.Context, serviceId int64, staffIdList []int64) error {
	if _, err := h.serviceStaffAvailabilityRepository.BatchCreateStaffAvailability(ctx, serviceId, staffIdList); err != nil {
		return err
	}
	return nil
}

func (h *serviceHandler) BatchCreateStaffOverrideRule(ctx context.Context, companyId, serviceId int64, staffOverrideRuleList []do.StaffOverrideRule) error {
	if _, err := h.serviceStaffOverrideRuleRepository.BatchCreateStaffOverrideRule(ctx, companyId, serviceId, staffOverrideRuleList); err != nil {
		return err
	}
	return nil
}

func (h *serviceHandler) GetAvailAbleStaffList(ctx context.Context, serviceId int64) ([]int64, error) {
	return h.serviceStaffAvailabilityRepository.GetAvailableStaffsByServiceID(ctx, serviceId)
}

// addServiceStaffInfo add service available staff and staff override rules to serviceList
func (h *serviceHandler) addServiceStaffInfo(ctx context.Context, companyId int64, serviceList []*do.ServiceDO) ([]*do.ServiceDO, error) {
	serviceIds := lo.Map(serviceList, func(service *do.ServiceDO, _ int) int64 {
		return service.ServiceID
	})

	serviceWithOverrideRulesMap, err := h.serviceStaffOverrideRuleRepository.GetStaffOverrideRules(ctx, companyId, nil, serviceIds)
	if err != nil {
		return nil, err
	}

	serviceAvailableStaffDo, err := h.serviceStaffAvailabilityRepository.GetAvailableStaffRulesByServiceIDList(ctx, serviceIds)
	if err != nil {
		return nil, err
	}

	// Group serviceAvailableStaffDo by serviceId to get map[int64][]int64
	serviceWithStaffIdListMap := lo.GroupBy(serviceAvailableStaffDo, func(serviceStaffAvailability do.ServiceStaffAvailabilityDO) int64 {
		return serviceStaffAvailability.ServiceId
	})

	serviceStaffIdMap := make(map[int64][]int64)
	for serviceId, staffList := range serviceWithStaffIdListMap {
		serviceStaffIdMap[serviceId] = lo.Map(staffList, func(staff do.ServiceStaffAvailabilityDO, _ int) int64 {
			return staff.StaffId
		})
	}

	// Add []LocationStaffOverrideRule from serviceWithOverrideRulesMap to serviceList
	for _, service := range serviceList {
		if staffIds, ok := serviceStaffIdMap[service.ServiceID]; ok {
			service.Availability.AvailableStaffIdList = staffIds
		}
		staffOverrideRules, ok := serviceWithOverrideRulesMap[service.ServiceID]
		if ok {
			service.Availability.StaffOverrideList = staffOverrideRules
		}
	}

	return serviceList, nil
}

// TODO: 拆分 Service repository 接口，应该返回 models.MoeGroomingService 而不是 do.ServiceDO
func (h *serviceHandler) addServicePetCodeFilter(ctx context.Context, serviceList []*do.ServiceDO) ([]*do.ServiceDO, error) {
	result := make([]*do.ServiceDO, 0)
	petCodeFilters, err := h.servicePetCodeAvailabilityRepository.List(ctx, &models.ServicePetCodeFilterWhereOpt{
		ServiceIdIn: lo.Map(serviceList, func(service *do.ServiceDO, _ int) int64 {
			return service.ServiceID
		}),
	})
	if err != nil {
		zlog.Error(ctx, "failed to get pet code filter", zap.Error(err))
		return nil, err
	}
	petCodeFilterMap := lo.SliceToMap(petCodeFilters, func(petCodeFilter *models.MoeServicePetCodeFilter) (int64, *models.MoeServicePetCodeFilter) {
		return petCodeFilter.ServiceID, petCodeFilter
	})
	for _, service := range serviceList {
		service.Availability.PetCodeFilter = converter.ConvertPetCodeAvailabilityFromDB(petCodeFilterMap[service.ServiceID])
		result = append(result, service)
	}

	return result, nil
}

func (h *serviceHandler) addServiceBundleSale(ctx context.Context, companyId int64, serviceList []*do.ServiceDO) ([]*do.ServiceDO, error) {
	result := make([]*do.ServiceDO, 0)
	bundleSales, err := h.serviceBundleSaleRepository.List(ctx, companyId, lo.Map(serviceList, func(service *do.ServiceDO, _ int) int64 {
		return service.ServiceID
	}))
	if err != nil {
		zlog.Error(ctx, "failed to get bundle sale", zap.Error(err))
		return nil, err
	}
	// group by parent service id
	bundleSaleMap := lo.GroupBy(bundleSales, func(bundleSale *models.ServiceBundleSaleMapping) int64 {
		return bundleSale.ServiceID
	})
	for _, service := range serviceList {
		service.BundleServiceIds = lo.Map(bundleSaleMap[service.ServiceID], func(bundleSale *models.ServiceBundleSaleMapping, _ int) int64 {
			return bundleSale.BundleServiceID
		})
		result = append(result, service)
	}

	return result, nil
}

func (h *serviceHandler) addServiceBundleSaleForOverride(ctx context.Context, companyId int64, serviceList []do.ServiceWithOverrideRules) ([]do.ServiceWithOverrideRules, error) {
	result := make([]do.ServiceWithOverrideRules, 0)
	bundleSales, err := h.serviceBundleSaleRepository.List(ctx, companyId, lo.Map(serviceList, func(service do.ServiceWithOverrideRules, _ int) int64 {
		return service.ServiceID
	}))
	if err != nil {
		zlog.Error(ctx, "failed to get bundle sale", zap.Error(err))
		return nil, err
	}
	// group by parent service id
	bundleSaleMap := lo.GroupBy(bundleSales, func(bundleSale *models.ServiceBundleSaleMapping) int64 {
		return bundleSale.ServiceID
	})
	for _, service := range serviceList {
		service.BundleServiceIds = lo.Map(bundleSaleMap[service.ServiceID], func(bundleSale *models.ServiceBundleSaleMapping, _ int) int64 {
			return bundleSale.BundleServiceID
		})
		result = append(result, service)
	}

	return result, nil
}

func (h *serviceHandler) GetServiceBundleSaleList(ctx context.Context, companyId int64, serviceIds []int64) ([]*models.ServiceBundleSaleMapping, error) {
	return h.serviceBundleSaleRepository.List(ctx, companyId, serviceIds)
}

func (h *serviceHandler) GetMaxServicePriceByLodgingType(ctx context.Context, companyId int64, businessId int64, lodgingTypeId int64) (*money.Money, error) {
	_, services, err := h.serviceRepository.GetServicesWithFilterPaginated(ctx, companyId, do.ServiceQueryFilter{
		BusinessIDList:   []int64{businessId},
		ServiceItemTypes: []offeringpb.ServiceItemType{offeringpb.ServiceItemType_BOARDING, offeringpb.ServiceItemType_DAYCARE}, // 这里应该以 required lodging 作为判断逻辑，但是目前数据不准确，重构前先使用 serviceItemType 简单处理
		ServiceTypes:     []offeringpb.ServiceType{offeringpb.ServiceType_SERVICE},
		Inactive:         proto.Bool(false),
	}, nil, offeringpb.ServiceOrderByType_DEFAULT)
	if err != nil {
		return &money.Money{}, err
	}

	maxPrice := 0.0
	for _, service := range services {
		if !service.Availability.ApplicableForLodging(lodgingTypeId) {
			continue
		}

		if service.Price > maxPrice {
			maxPrice = service.Price
		}
	}

	preferenceSetting, err := h.companyServiceClient.GetCompanyPreferenceSetting(ctx, companyId)
	if err != nil {
		return &money.Money{}, err
	}

	return utils.ConvertToPBMoney(maxPrice, preferenceSetting.GetCurrencyCode()), nil
}

func NewServiceHandler() ServiceHandler {
	return &serviceHandler{
		serviceRepository:                     repository.NewServiceRepository(resource.GetGroomingDB()),
		categoryRepository:                    repository.NewCategoryRepository(resource.GetGroomingDB()),
		serviceStaffAvailabilityRepository:    repository.NewServiceStaffAvailabilityRepository(resource.GetOfferingDB()),
		serviceStaffOverrideRuleRepository:    repository.NewServiceStaffOverrideRuleRepository(resource.GetOfferingDB()),
		serviceBusinessOverrideRuleRepository: repository.NewServiceBusinessOverrideRuleRepository(resource.GetGroomingDB()),
		servicePetOverrideRuleRepository:      repository.NewServicePetOverrideRuleRepository(resource.GetGroomingDB()),
		servicePetCodeAvailabilityRepository:  repository.NewServicePetCodeAvailabilityRepository(resource.GetGroomingDB()),
		serviceBundleSaleRepository:           repository.NewServiceBundleSaleRepository(resource.GetOfferingDB()),
		groupClassInstanceRepository:          repository.NewGroupClassInstanceRepository(resource.GetOfferingDB()),

		pricingRuleRecordHandler: NewPricingRuleRecordHandler(),

		staffServiceClient:   clients.NewStaffClient(resource.GetStaffServiceClient()),
		companyServiceClient: clients.NewCompanyClient(resource.GetCompanyServiceClient()),

		offeringDB: resource.GetOfferingDB(),
	}
}
