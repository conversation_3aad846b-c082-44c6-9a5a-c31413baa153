package com.moego.server.grooming.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class PetDetailParams {

    private Integer groomingId;

    private Integer petId;

    private Integer staffId;

    private Integer serviceId;

    private Integer serviceType;

    private BigDecimal servicePrice;

    private Integer serviceTime;

    private Integer startTime;

    private Boolean star;

    @Schema(
            description = "定制service price应用范围: " + "    2: do not save \n"
                    + "    1: this appt and new \n"
                    + "    3: this and following \n"
                    + "    4: all upcoming \n")
    private Integer scopeTypePrice;

    @Schema(
            description = "定制service duration应用范围" + "    2: do not save \n"
                    + "    1: this appt and new \n"
                    + "    3: this and following \n"
                    + "    4: all upcoming \n")
    private Integer scopeTypeTime;

    private Boolean enableOperation;

    /**
     * work mode
     * 0 - parallel
     * 1 - sequence
     */
    private Integer workMode;

    private List<@Valid GroomingServiceOperationDTO> operationList;

    private String startDate;

    private String endDate;

    private ServiceItemEnum serviceItemEnum;

    private Integer endTime;

    @Nullable
    private Long lodgingId;

    /**
     * ServiceOverrideType 用的是 protobuf enum，会导致前端生成两份 enum，这里返回 int
     */
    @Nullable
    @Schema(type = "integer", format = "int32")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private ServiceOverrideType priceOverrideType;

    @Nullable
    @Schema(type = "integer", format = "int32")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private ServiceOverrideType durationOverrideType;

    private List<FeedingParams> feedings;

    private List<MedicationParams> medications;

    /**
     * Add-on required fields, default value is 1
     */
    @Nullable
    private Integer quantityPerDay;

    @Data
    public static class FeedingParams {
        private String feedingAmount;
        private String feedingUnit;
        private String feedingType;
        private String feedingSource;
        private String feedingInstruction;
        private String feedingNote;
        private List<ScheduleTimeParams> feedingTimes;
    }

    @Data
    public static class MedicationParams {
        private String medicationAmount;
        private String medicationUnit;
        private String medicationName;
        private String medicationNote;
        private List<ScheduleTimeParams> medicationTimes;
    }

    @Data
    public static class ScheduleTimeParams {
        private Integer scheduleTime;
        private Map<String, String> extraJson;
    }
}
