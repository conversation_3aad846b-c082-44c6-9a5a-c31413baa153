package com.moego.server.grooming.mapper;

import com.moego.server.grooming.dto.CustomerServiceQueryDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGroomingCustomerServicesMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_customer_services
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_customer_services
     *
     * @mbg.generated
     */
    int insert(MoeGroomingCustomerServices record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_customer_services
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingCustomerServices record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_customer_services
     *
     * @mbg.generated
     */
    MoeGroomingCustomerServices selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_customer_services
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingCustomerServices record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_customer_services
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeGroomingCustomerServices record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_customer_services
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingCustomerServices record);

    // cid check done
    List<MoeGroomingCustomerServices> queryCustomerServiceByProperties(
            @Param("customerServiceQueryDTO") CustomerServiceQueryDTO customerServiceQueryDTO);

    /**
     * cid check done
     * cid 和 bid 只能二选一
     * @param companyId
     * @param businessId
     * @param customerId
     * @param petIds
     * @param serviceIds
     * @param saveType
     * @return
     */
    List<MoeGroomingCustomerServices> getCustomizeServices(
            @Param("companyId") Long companyId,
            @Param("businessId") Integer businessId,
            @Param("customerId") Integer customerId,
            @Param("petIds") List<Integer> petIds,
            @Param("serviceIds") List<Integer> serviceIds,
            @Param("saveType") Byte saveType);

    List<MoeGroomingCustomerServices> getCustomizeServicesByPetIds(
            @Nullable @Param("companyId") Long companyId,
            @Nonnull @Param("petIds") List<Integer> petIds,
            @Nullable @Param("serviceIds") List<Integer> serviceIds,
            @Nullable @Param("saveType") Byte saveType);

    // cid repleace done
    List<MoeGroomingCustomerServices> queryCustomizeServices(
            @Param("companyId") Long companyId,
            @Param("businessId") Integer businessId,
            @Param("petIds") List<Integer> petIds,
            @Param("serviceIds") List<Integer> serviceIds);

    int deleteByLastServiceIdNewServiceId(
            @Param("lastServiceId") Integer lastSedeleteByLastServiceIdNewServiceIdrviceId,
            @Param("newServiceId") Integer newServiceId,
            @Param("businessId") Integer businessId,
            @Param("customerId") Integer customerId,
            @Param("petId") Integer petId);

    int deleteByLastServiceIdNewServiceIdByCid(
            @Param("lastServiceId") Integer lastSedeleteByLastServiceIdNewServiceIdrviceId,
            @Param("newServiceId") Integer newServiceId,
            @Param("companyId") Long companyId,
            @Param("customerId") Integer customerId,
            @Param("petId") Integer petId);

    int batchUpdateCategoryId(
            @Param(value = "fromCategoryId") Integer fromCategoryId,
            @Param(value = "toCategoryId") Integer toCategoryId,
            @Param(value = "companyId") Long companyId);
}
