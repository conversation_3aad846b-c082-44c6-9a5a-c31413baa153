package com.moego.server.grooming.mapper;

import com.moego.server.grooming.dto.PetServiceDTO;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceExample;
import com.moego.server.grooming.service.dto.ReportServiceDto;
import com.moego.server.grooming.web.dto.ob.OBPetServiceDTO;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MoeGroomingServiceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    long countByExample(MoeGroomingServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    int insert(MoeGroomingService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    List<MoeGroomingService> selectByExample(MoeGroomingServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    MoeGroomingService selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeGroomingService record, @Param("example") MoeGroomingServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeGroomingService record, @Param("example") MoeGroomingServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingService record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingService record);

    int updateByPrimaryKeySelectiveWithBidCid(MoeGroomingService record);

    // company new interface start >>>>>>>>>>>>>

    List<MoeGroomingService> selectByBusinessIdType(
            @Param(value = "companyId") Long companyId,
            @Param(value = "businessId") Integer businessId,
            @Param(value = "type") Byte type,
            @Param(value = "inactive") Byte inactive,
            @Param(value = "serviceItemType") Integer serviceItemType);

    List<MoeGroomingService> selectCompanyServiceWithBids(
            @Param(value = "companyId") Long companyId,
            @Param(value = "businessIds") List<Integer> businessIds,
            @Param(value = "type") Byte type,
            @Param(value = "inactive") Byte inactive);

    // 已检查新旧数据兼容，对应的新数据查询为 selectByCidBidTypeKeyword
    List<MoeGroomingService> selectByBusinessIdTypeKeyword(
            @Param(value = "businessId") Integer businessId,
            @Param(value = "type") Byte type,
            @Param(value = "inactive") Byte inactive,
            @Param(value = "keyword") String keyword);

    // 新旧数据兼容之新接口
    List<MoeGroomingService> selectByCidBidTypeKeyword(
            @Param(value = "companyId") Long companyId,
            @Param(value = "businessId") Integer businessId,
            @Param(value = "type") Byte type,
            @Param(value = "inactive") Byte inactive,
            @Param(value = "keyword") String keyword);

    // 已检查新旧数据兼容，对应的新数据查询为 selectActiveServiceByBidCid
    List<PetServiceDTO> selectActiveServiceByBusinessId(
            @Param("businessId") Integer businessId, @Param("type") Integer type);

    // 新旧数据兼容之新接口
    List<PetServiceDTO> selectActiveServiceByBusinessIdNew(
            @Param("companyId") Long companyId, @Param("businessId") Integer businessId, @Param("type") Integer type);

    // 已检查新旧数据兼容，对应的新数据查询为 getMaxSortByCategoryIdForCid
    @Deprecated
    Integer getMaxSortByCategoryId(
            @Param("businessId") Integer businessId,
            @Param("categoryId") Integer categoryId,
            @Param("inactive") Byte inactive);

    // 新旧数据兼容之新接口
    Integer getMaxSortByCategoryIdForCid(
            @Param("companyId") Long companyId,
            @Param("categoryId") Integer categoryId,
            @Param("inactive") Byte inactive);

    // 已检查新旧数据兼容，对应的新数据查询为 selectOBServiceByBusinessIdNew
    List<OBPetServiceDTO> selectOBServiceByBusinessId(
            @Param("businessId") Integer businessId, @Param("type") Integer type);

    // 新旧数据兼容之新接口
    List<OBPetServiceDTO> selectOBServiceByBusinessIdNew(
            @Param("companyId") Long companyId, @Param("businessId") Integer businessId, @Param("type") Integer type);

    // 不需要数据兼容
    int batchUpdateSort(@Param(value = "serviceList") List<MoeGroomingService> serviceList);

    // 新接口 for 新数据
    int setServiceCategoryDefaultForCid(
            @Param(value = "oldCategoryId") Integer oldCategoryId, @Param(value = "companyId") Long companyId);

    int batchUpdateCategoryId(
            @Param(value = "fromCategoryId") Integer fromCategoryId,
            @Param(value = "toCategoryId") Integer toCategoryId,
            @Param(value = "companyId") Long companyId);

    /**
     * 兼容新旧两种情况，要分别传参
     * businessId 和 companyId 只能传一个
     */
    Integer getServiceCountWithName(
            @Param(value = "companyId") Long companyId,
            @Param(value = "businessId") Integer businessId,
            @Param(value = "name") String name,
            @Param(value = "type") Byte type,
            @Param(value = "updateServiceId") Integer updateServiceId);

    // 已检查新旧数据兼容，对应的新数据查询为 getServicesByBusinessIdServiceIdsNew
    List<MoeGroomingService> getServicesByBusinessIdServiceIds(
            @Param(value = "businessId") Integer businessId, @Param(value = "serviceIds") List<Integer> serviceIds);

    // 新旧数据兼容之新接口
    List<MoeGroomingService> getServicesByBusinessIdServiceIdsNew(
            @Param(value = "companyId") Long companyId,
            @Param(value = "businessId") Integer businessId,
            @Param(value = "serviceIds") List<Integer> serviceIds);

    // 新接口 仅仅给排序用
    List<MoeGroomingService> getServicesByCompanyIdServiceIds(
            @Param(value = "companyId") Long companyId, @Param(value = "serviceIds") List<Integer> serviceIds);

    // 不建议使用的旧接口，不能用来获取 price,duration,tax
    @Deprecated
    List<MoeGroomingService> getServicesByServiceIds(@Param(value = "serviceIds") List<Integer> serviceIds);

    // 已检查新旧数据兼容，对应的新数据查询为 selectServiceByBusinessIdNew
    List<PetServiceDTO> selectServiceByBusinessId(@Param("businessId") Integer businessId, @Param("type") Integer type);

    // 新旧数据兼容之新接口
    List<PetServiceDTO> selectServiceByBusinessIdNew(
            @Param("companyId") Long companyId, @Param("businessId") Integer businessId, @Param("type") Integer type);

    // 旧接口 通过 sql 兼容
    Integer selectServiceCountByTagId(@Param("companyId") Long companyId, @Param("taxId") Integer taxId);

    List<MoeGroomingService> selectAllByCompanyId(
            @Param("companyId") Long companyId, @Param("categoryId") Integer categoryId);

    List<MoeGroomingService> selectByCompanyIds(@Param("companyIds") Collection<Long> companyIds);

    // <<<<<<<<<<<<<<<<<<<<<<< company new interface end

    /**
     * 查询服务列表（包括category信息）
     *
     * @param businessId businessId
     * @param serviceIds 查询的serviceId
     * @return
     */
    List<ReportServiceDto> queryServiceWithCategoryByServiceIds(
            @Param("businessId") Integer businessId, @Param("serviceIds") Set<Integer> serviceIds);

    List<ReportServiceDto> queryServiceWithCategoryByServiceIdsNew(
            @Param("companyId") Long companyId, @Param("serviceIds") Set<Integer> serviceIds);

    int batchUpdateService(@Param(value = "serviceList") List<MoeGroomingService> serviceList);

    List<MoeGroomingService> selectByBusinessIds(@Param("businessIds") Collection<Integer> businessIds);
}
