package com.moego.server.grooming.server;

import com.github.pagehelper.Page;
import com.moego.common.distributed.LockManager;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.enums.AgreementEnum;
import com.moego.common.enums.CustomerContactEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.params.PageQuery;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.marketing.v1.DiscountCodeModel;
import com.moego.idl.models.notification.v1.AppPushDef;
import com.moego.idl.models.notification.v1.AppointmentDayDef;
import com.moego.idl.models.notification.v1.NotificationExtraDef;
import com.moego.idl.models.notification.v1.NotificationMethod;
import com.moego.idl.models.notification.v1.NotificationSource;
import com.moego.idl.models.notification.v1.NotificationType;
import com.moego.idl.models.notification.v1.PushTokenSource;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.marketing.v1.DiscountCodeServiceGrpc;
import com.moego.idl.service.marketing.v1.GetDiscountCodeByCodeInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeByCodeOutput;
import com.moego.idl.service.notification.v1.CreateInboxNotificationRequest;
import com.moego.idl.service.notification.v1.NotificationServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.LinkAccountDTO;
import com.moego.server.grooming.api.IClientPortalAppointmentServiceBase;
import com.moego.server.grooming.config.ClientPortalConfiguration;
import com.moego.server.grooming.constant.AppointmentStatusSet;
import com.moego.server.grooming.dto.AppointmentDayDTO;
import com.moego.server.grooming.dto.AppointmentDetailClientPortalDTO;
import com.moego.server.grooming.dto.AppointmentListClientPortalDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.UpdateAppointmentDTO;
import com.moego.server.grooming.dto.ob.CalculateServiceAmountDTO;
import com.moego.server.grooming.dto.ob.SubmitBookingRequestDTO;
import com.moego.server.grooming.dto.ob.UpdateBookingRequestDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.AutoAssign;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapstruct.AppointmentMapper;
import com.moego.server.grooming.mapstruct.PetDetailConverter;
import com.moego.server.grooming.params.AppointmentDetailParams;
import com.moego.server.grooming.params.AppointmentListParams;
import com.moego.server.grooming.params.BookOnlineCustomerParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import com.moego.server.grooming.params.ConfirmParams;
import com.moego.server.grooming.params.appointment.UpdateAppointmentParams;
import com.moego.server.grooming.params.ob.CalculatePaymentParams;
import com.moego.server.grooming.params.ob.CancelBookingRequestParams;
import com.moego.server.grooming.params.ob.DiscountCodeParams;
import com.moego.server.grooming.params.ob.SubmitBookingRequestParams;
import com.moego.server.grooming.params.ob.UpdateBookingRequestParams;
import com.moego.server.grooming.properties.ClientPortalProperties;
import com.moego.server.grooming.service.AutoAssignService;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.client.ClientApptService;
import com.moego.server.grooming.service.client.ClientApptServiceContext;
import com.moego.server.grooming.service.client.ClientHistoryApptService;
import com.moego.server.grooming.service.client.ClientTodayApptService;
import com.moego.server.grooming.service.client.ClientUpcomingApptService;
import com.moego.server.grooming.service.client.IBaseClientApptService;
import com.moego.server.grooming.service.dto.OBSubmitResult;
import com.moego.server.grooming.service.dto.client.ClientCancelApptDTO;
import com.moego.server.grooming.service.dto.client.ClientUpdateApptDTO;
import com.moego.server.grooming.service.dto.client.ListClientApptDTO;
import com.moego.server.grooming.service.ob.OBGroomingService;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import com.moego.server.grooming.web.vo.client.UpdateApptVO;
import com.moego.server.message.enums.MessageMethodTypeEnum;
import com.moego.server.payment.client.IPaymentCreditCardClient;
import com.moego.server.payment.dto.CardDTO;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/9/25
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class ClientPortalAppointmentServer extends IClientPortalAppointmentServiceBase {

    private final ClientApptServiceContext apptServiceContext;
    private final ClientApptService clientApptService;
    private final ClientUpcomingApptService clientUpcomingApptService;
    private final ClientHistoryApptService clientHistoryApptService;
    private final ClientTodayApptService clientTodayApptService;
    private final PetDetailMapperProxy petDetailMapper;
    private final AppointmentMapperProxy appointmentMapper;
    private final IPetClient petClient;
    private final OBGroomingService obGroomingService;
    private final MoeGroomingBookOnlineService bookOnlineService;
    private final MoeGroomingAppointmentService appointmentService;
    private final IBusinessBusinessClient businessClient;
    private final ClientPortalProperties clientPortalProperties;
    private final ICustomerCustomerClient customerClient;
    private final IPaymentCreditCardClient creditCardClient;
    private final LockManager lockManager;
    private final StringRedisTemplate redisTemplate;
    private final AutoAssignService autoAssignService;
    private final NotificationServiceGrpc.NotificationServiceBlockingStub notificationServiceBlockingStub;
    private final DiscountCodeServiceGrpc.DiscountCodeServiceBlockingStub discountCodeServiceBlockingStub;
    private final BusinessInfoHelper businessInfoHelper;

    @Autowired
    @Qualifier(ClientPortalConfiguration.CLIENT_PORTAL_EXECUTOR_SERVICE)
    private ExecutorService executorService;

    private static final String APPOINTMENT_DATE = "{appointmentDate}";
    private static final String NOTIFICATION_APPOINTMENT_DAY = "grooming:notification:" + APPOINTMENT_DATE;

    @Override
    public AppointmentListClientPortalDTO getAppointmentList(AppointmentListParams params) {
        IBaseClientApptService clientApptService = apptServiceContext.getApptService(params.getApptType());
        ListClientApptDTO listClientApptDTO = new ListClientApptDTO();
        listClientApptDTO.setCustomerIdDTOList(params.getLinkCustomers());
        listClientApptDTO.setBusinessDateTimeDTOMap(params.getBusinessDateTimeDTOMap());
        PageQuery pageQuery = new PageQuery()
                .setPageNum(params.getPagination().pageNum())
                .setPageSize(params.getPagination().pageSize())
                .setSortList(params.getSorts());
        Page<MoeGroomingAppointment> page = clientApptService.getApptList(listClientApptDTO, pageQuery);
        List<MoeGroomingAppointment> apptList = page.getResult();
        // If there is auto assign in booking request, leave the corresponding parameters blank.
        Map<Integer, AutoAssign> apptIdToAutoAssign =
                autoAssignService
                        .listAutoAssign(apptList.stream()
                                .map(MoeGroomingAppointment::getId)
                                .toList())
                        .stream()
                        .collect(Collectors.toMap(AutoAssign::getAppointmentId, Function.identity()));
        List<AppointmentListClientPortalDTO.AppointmentList> appointmentList = apptList.stream()
                .map(appt -> {
                    AppointmentListClientPortalDTO.AppointmentList appointment =
                            new AppointmentListClientPortalDTO.AppointmentList()
                                    .setId(appt.getId())
                                    .setBusinessId(appt.getBusinessId())
                                    .setCustomerId(appt.getCustomerId())
                                    .setAppointmentDate(appt.getAppointmentDate())
                                    .setAppointmentStartTime(appt.getAppointmentStartTime())
                                    .setAppointmentEndTime(appt.getAppointmentEndTime())
                                    .setNoStartTime(appt.getNoStartTime());
                    AutoAssign autoAssign = apptIdToAutoAssign.get(appt.getId());
                    if (Objects.equals(appt.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)
                            && autoAssign != null
                            && autoAssign.getAppointmentTime() != null) {
                        appointment.setNoStartTime(true);
                        appointment.setAppointmentStartTime(null);
                        appointment.setAppointmentEndTime(null);
                    }
                    return appointment;
                })
                .toList();
        return new AppointmentListClientPortalDTO()
                .setAppointmentList(appointmentList)
                .setPagination(Pagination.builder()
                        .pageNum(page.getPageNum())
                        .pageSize(page.getPageSize())
                        .total((int) page.getTotal())
                        .build());
    }

    @Override
    public AppointmentDetailClientPortalDTO getAppointmentDetail(AppointmentDetailParams params) {
        MoeGroomingAppointment appt =
                clientApptService.getClientAppt(params.getAppointmentId(), params.getLinkCustomers());
        List<MoeGroomingPetDetail> petDetailList = petDetailMapper.queryPetDetailCountByGroomingId(appt.getId());
        AppointmentDetailClientPortalDTO dto = AppointmentMapper.INSTANCE.entityToDto(appt);
        dto.setPetDetails(PetDetailConverter.INSTANCE.entityToDto(petDetailList));
        AutoAssign autoAssign = autoAssignService.getAutoAssign(appt.getId());
        if (autoAssign != null
                && Objects.equals(appt.getBookOnlineStatus(), GroomingAppointmentEnum.BOOK_ONLINE_STATUS_OB)) {
            if (autoAssign.getStaffId() != null) {
                dto.getPetDetails().forEach(petDetail -> petDetail.setStaffId(null));
            }
            if (autoAssign.getAppointmentTime() != null) {
                dto.setNoStartTime(true);
                dto.setAppointmentStartTime(null);
                dto.setAppointmentEndTime(null);
            }
        }
        return dto;
    }

    @Override
    public AppointmentDetailClientPortalDTO getLastFinishedAppointmentDetail(AppointmentDetailParams params) {
        Integer comingNextApptId = clientHistoryApptService.getLastFinishedApptId(params.getLinkCustomers());
        if (Objects.isNull(comingNextApptId)) {
            return null;
        }
        return this.getAppointmentDetail(AppointmentDetailParams.builder()
                .appointmentId(comingNextApptId)
                .linkCustomers(params.getLinkCustomers())
                .build());
    }

    @Override
    public AppointmentDetailClientPortalDTO getNextUpcomingAppointmentDetail(AppointmentDetailParams params) {
        Integer comingNextApptId = clientUpcomingApptService.getComingNextApptId(params.getLinkCustomers());
        if (Objects.isNull(comingNextApptId)) {
            return null;
        }
        return this.getAppointmentDetail(AppointmentDetailParams.builder()
                .appointmentId(comingNextApptId)
                .linkCustomers(params.getLinkCustomers())
                .build());
    }

    @Override
    public List<AppointmentDetailClientPortalDTO> getTodayAppointmentListDetail(AppointmentDetailParams params) {
        return clientTodayApptService.getTodayApptList(params);
    }

    @Override
    public Boolean preSubmitBookingRequest(SubmitBookingRequestParams params) {
        String lockKey = buildLockKey(params);
        String lockValue = buildLockValue(params);
        MoeBusinessBookOnline obBusiness = bookOnlineService.getSettingInfoByBusinessId(params.getBusinessId());
        BookOnlineSubmitParams submitParams = toSubmitParams(params);
        bookOnlineService.submitParamsCheck(obBusiness, submitParams);
        if (Objects.isNull(submitParams.getStaffId()) || !StringUtils.hasText(submitParams.getAppointmentDate())) {
            return true;
        }
        if (lockManager.lockWithRetry(lockKey, lockValue)) {
            return true;
        }
        if (Objects.equals(lockValue, lockManager.getLockValue(lockKey))) {
            lockManager.refreshLockTime(lockKey);
            return true;
        }
        throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_TIME_LOCKED);
    }

    @Override
    public SubmitBookingRequestDTO submitBookingRequest(SubmitBookingRequestParams params) {
        MoeBusinessBookOnline obBusiness = bookOnlineService.getSettingInfoByBusinessId(params.getBusinessId());
        BookOnlineSubmitParams obParams = toSubmitParams(params);
        OBSubmitResult result;
        if (Objects.isNull(obParams.getStaffId()) || !StringUtils.hasText(obParams.getAppointmentDate())) {
            result = obGroomingService.bookOnlineSubmit(obParams, obBusiness);
        } else {
            String lockKey = buildLockKey(params);
            String lockValue = buildLockValue(params);
            try {
                if (Objects.equals(lockValue, lockManager.getLockValue(lockKey))) {
                    lockManager.refreshLockTime(lockKey);
                    result = obGroomingService.bookOnlineSubmit(obParams, obBusiness);
                } else {
                    throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_TIME_LOCKED);
                }
            } catch (Exception e) {
                // 如果定金id不为空，则发起退款流程
                if (StringUtils.hasText(obParams.getPrepayGuid())) {
                    bookOnlineService.createRefundForOBDeposit(obParams.getPrepayGuid());
                }
                throw e;
            } finally {
                lockManager.unlock(lockKey, lockValue);
            }
        }
        return new SubmitBookingRequestDTO().setAutoAcceptRequest(result.getAutoAcceptRequest());
    }

    private BookOnlineSubmitParams toSubmitParams(SubmitBookingRequestParams params) {
        BookOnlineSubmitParams obParams = new BookOnlineSubmitParams();
        obParams.setFromPetParentPortal(Boolean.TRUE);
        obParams.setBusinessId(params.getBusinessId());
        obParams.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(params.getBusinessId()));
        BookOnlineCustomerParams customerParams = new BookOnlineCustomerParams();
        customerParams.setCustomerId(params.getCustomerId());
        customerParams.setAddressId(params.getAddressId());
        obParams.setCustomerData(customerParams);
        obParams.setStaffId(params.getStaffId());
        obParams.setAppointmentDate(params.getAppointmentDate());
        obParams.setAppointmentStartTime(params.getAppointmentStartTime());
        obParams.setNote(params.getAdditionalNote());
        Optional.ofNullable(params.getAgreements())
                .ifPresent(agreements -> agreements.forEach(
                        agreement -> agreement.setAgreementConfirmed(AgreementEnum.AGREEMENT_CONFIRM)));
        obParams.setAgreements(params.getAgreements());
        obParams.setPetData(params.getPetData());
        // payments
        obParams.setIsAgreePolicy(CustomerContactEnum.AGREE_CANCEL_POLICY);
        if (Objects.nonNull(params.getCardOnFile())) {
            customerParams.setHasStripeCard(params.getCardOnFile().isHasCard());
            customerParams.setChargeToken(params.getCardOnFile().getChargeToken());
            if (params.getCardOnFile().isHasCard()) {
                List<CardDTO> cardList =
                        creditCardClient.getCreditCardList(params.getBusinessId(), params.getCustomerId());
                if (CollectionUtils.isEmpty(cardList)) {
                    throw ExceptionUtil.bizException(Code.CODE_STRIPE_CARD_TOKEN_IS_EMPTY);
                }
                customerParams.setChargeToken(cardList.get(0).getId());
            }
        }
        obParams.setPreAuthDetail(params.getPreAuthDetail());
        obParams.setPrepayGuid(params.getPrepayGuid());
        obParams.setPrepayDetail(params.getPrepayDetail());
        // discount code
        if (StringUtils.hasText(params.getDiscountCode())) {
            obParams.setDiscountCodeParams(buildDiscountCodeParams(params));
        }
        return obParams;
    }

    private DiscountCodeParams buildDiscountCodeParams(SubmitBookingRequestParams params) {
        GetDiscountCodeByCodeOutput discountCodeByCodeOutput =
                discountCodeServiceBlockingStub.getDiscountCodeByCode(GetDiscountCodeByCodeInput.newBuilder()
                        .setBusinessId(params.getBusinessId())
                        .setDiscountCode(params.getDiscountCode())
                        .build());
        if (!discountCodeByCodeOutput.hasDiscountCodeModel()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid discount code");
        }
        DiscountCodeModel discountCodeModel = discountCodeByCodeOutput.getDiscountCodeModel();
        DiscountCodeParams discountCodeParams = new DiscountCodeParams();
        discountCodeParams.setDiscountCodeId(discountCodeModel.getId());
        discountCodeParams.setDiscountCode(discountCodeModel.getDiscountCode());
        BookOnlineCustomerParams customerParams = new BookOnlineCustomerParams();
        customerParams.setCustomerId(params.getCustomerId());
        CalculateServiceAmountDTO serviceAmountDTO = bookOnlineService.getServiceSubTotalAmount(
                params.getBusinessId(),
                params.getPetData(),
                params.getDiscountCode(),
                params.getStaffId(),
                customerParams);
        discountCodeParams.setDiscountAmount(serviceAmountDTO.getDiscountAmount());
        return discountCodeParams;
    }

    private String buildLockKey(SubmitBookingRequestParams params) {
        return lockManager.getResourceKey(LockManager.OB_SUBMIT, params.getStaffId() + params.getAppointmentDate());
    }

    private String buildLockValue(SubmitBookingRequestParams params) {
        return params.getBusinessId() + ":" + params.getCustomerId();
    }

    @Override
    public UpdateBookingRequestDTO updateBookingRequest(UpdateBookingRequestParams params) {
        ClientUpdateApptDTO updateApptDTO = new ClientUpdateApptDTO()
                .setBookingId(String.valueOf(params.getAppointmentId()))
                .setApptDate(params.getAppointmentDate())
                .setApptStartTime(params.getAppointmentStartTime())
                .setStaffId(params.getStaffId())
                .setCustomerIdDTOList(params.getLinkCustomers())
                .setClientId(Math.toIntExact(params.getAccountId()));
        UpdateApptVO vo = clientApptService.updateAppt(updateApptDTO);
        return new UpdateBookingRequestDTO()
                .setUpdateSuccess(vo.updateSuccess())
                .setAutoAcceptRequest(vo.autoAcceptRequest());
    }

    @Override
    public UpdateBookingRequestDTO cancelBookingRequest(CancelBookingRequestParams params) {
        ClientCancelApptDTO cancelApptDTO = new ClientCancelApptDTO()
                .setBookingId(String.valueOf(params.getAppointmentId()))
                .setClientId(Math.toIntExact(params.getAccountId()))
                .setCustomerIdDTOList(params.getLinkCustomers());
        boolean result = clientApptService.cancelAppt(cancelApptDTO);
        return new UpdateBookingRequestDTO().setUpdateSuccess(result);
    }

    @Override
    public UpdateAppointmentDTO updateAppointment(UpdateAppointmentParams params) {
        ConfirmParams confirmParams = new ConfirmParams();
        confirmParams.setId(Math.toIntExact(params.getAppointmentId()));
        confirmParams.setConfirmByType(GroomingAppointmentEnum.CONFIRM_TYPE_BY_CLIENT);
        confirmParams.setConfirmByMethod(MessageMethodTypeEnum.MESSAGE_METHOD_APP);
        appointmentService.editAppointmentConfirm(confirmParams);
        return new UpdateAppointmentDTO().setResult(true);
    }

    @Override
    public AppointmentDayDTO sendAppointmentDayNotificationToClient() {
        AtomicInteger count = new AtomicInteger();
        List<Integer> invitationBusinessIds = businessClient.listBusinessIdHasInvitationCode();
        if (CollectionUtils.isEmpty(invitationBusinessIds)) {
            return new AppointmentDayDTO().setNotificationCount(count.get());
        }
        Map<Integer, BusinessDateTimeDTO> dateMap = businessClient.listBusinessDateTime(invitationBusinessIds);
        Map<Integer, MoeBusinessDto> businessMap = businessClient.getOnlyBusinessInfoBatch(invitationBusinessIds);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        invitationBusinessIds.forEach(businessId -> futures.add(CompletableFuture.runAsync(
                () -> {
                    BusinessDateTimeDTO dateTimeDTO = dateMap.get(businessId);
                    MoeGroomingAppointmentExample example = new MoeGroomingAppointmentExample();
                    example.createCriteria()
                            .andBusinessIdEqualTo(businessId)
                            .andIsWaitingListEqualTo(GroomingAppointmentEnum.NOT_WAITING_LIST)
                            .andIsDeprecateEqualTo(GroomingAppointmentEnum.IS_DEPRECATE_FALSE)
                            .andStatusIn(AppointmentStatusSet.IN_PROGRESS_STATUS_SET.stream()
                                    .map(AppointmentStatusEnum::getValue)
                                    .toList())
                            .andIsBlockEqualTo(GroomingAppointmentEnum.UNBLOCK)
                            .andAppointmentDateEqualTo(dateTimeDTO.getCurrentDate())
                            .andAppointmentStartTimeGreaterThanOrEqualTo(dateTimeDTO.getCurrentMinutes())
                            .andAppointmentStartTimeLessThanOrEqualTo(dateTimeDTO.getCurrentMinutes()
                                    + clientPortalProperties.getNotificationAheadMinutes())
                            .andServiceTypeIncludeEqualTo(ServiceItemType.GROOMING_VALUE);
                    List<MoeGroomingAppointment> appointments = appointmentMapper.selectByExample(example);
                    if (CollectionUtils.isEmpty(appointments)) {
                        return;
                    }
                    List<Integer> customerIds = appointments.stream()
                            .map(MoeGroomingAppointment::getCustomerId)
                            .distinct()
                            .toList();
                    List<LinkAccountDTO> linkAccounts = customerClient.listLinkAccountId(customerIds);
                    Map<Integer, Long> linkAccountMap = linkAccounts.stream()
                            .collect(Collectors.toMap(LinkAccountDTO::getCustomerId, LinkAccountDTO::getAccountId));
                    appointments.forEach(appointment -> {
                        Long linkAccountId = linkAccountMap.get(appointment.getCustomerId());
                        if (Objects.isNull(linkAccountId) || Objects.equals(linkAccountId, 0L)) {
                            return;
                        }
                        // has sent
                        String key =
                                NOTIFICATION_APPOINTMENT_DAY.replace(APPOINTMENT_DATE, dateTimeDTO.getCurrentDate());
                        if (Optional.ofNullable(redisTemplate.opsForSet().isMember(key, String.valueOf(linkAccountId)))
                                .orElse(false)) {
                            return;
                        }
                        List<Integer> petIds = petDetailMapper.queryPetDetailByGroomingId(appointment.getId()).stream()
                                .map(GroomingPetDetailDTO::getPetId)
                                .distinct()
                                .toList();
                        List<CustomerPetDetailDTO> pets = petClient.getCustomerPetListByIdList(petIds);
                        CreateInboxNotificationRequest request = CreateInboxNotificationRequest.newBuilder()
                                .setSource(NotificationSource.NOTIFICATION_SOURCE_PLATFORM)
                                .setMethod(NotificationMethod.NOTIFICATION_METHOD_PET_PARENT_APP)
                                .setType(NotificationType.NOTIFICATION_TYPE_APPOINTMENT_DAY)
                                .setSenderId(0L)
                                .setReceiverId(linkAccountId)
                                .setExtra(NotificationExtraDef.newBuilder()
                                        .setAppointmentDay(AppointmentDayDef.newBuilder()
                                                .setAppointmentId(appointment.getId())
                                                .setBusinessName(businessMap
                                                        .get(appointment.getBusinessId())
                                                        .getBusinessName())
                                                .setBusinessId(appointment.getBusinessId())
                                                .setPetName(pets.stream()
                                                        .map(CustomerPetDetailDTO::getPetName)
                                                        .collect(Collectors.joining(" & ")))
                                                .build())
                                        .build())
                                .setAppPush(AppPushDef.newBuilder()
                                        .setSource(PushTokenSource.PUSH_TOKEN_SOURCE_CLIENT)
                                        .setFromTemplate(true)
                                        .build())
                                .build();
                        notificationServiceBlockingStub.createInboxNotification(request);
                        redisTemplate.opsForSet().add(key, String.valueOf(linkAccountId));
                        redisTemplate.expire(key, Duration.ofDays(1));
                        count.incrementAndGet();
                    });
                },
                executorService)));
        CompletableFuture.allOf(futures.toArray(CompletableFuture[]::new)).join();
        return new AppointmentDayDTO().setNotificationCount(count.get());
    }

    @Override
    public CalculateServiceAmountDTO calculatePaymentInfo(CalculatePaymentParams params) {
        BookOnlineCustomerParams customerParams = new BookOnlineCustomerParams();
        customerParams.setCustomerId(params.getCustomerId().intValue());
        return bookOnlineService.getServiceSubTotalAmount(
                params.getBusinessId().intValue(),
                params.getPetData(),
                params.getDiscountCode(),
                params.getStaffId(),
                customerParams);
    }
}
