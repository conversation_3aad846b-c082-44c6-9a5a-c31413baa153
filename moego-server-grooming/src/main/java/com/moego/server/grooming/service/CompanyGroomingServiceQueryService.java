package com.moego.server.grooming.service;

import static java.util.stream.Collectors.groupingBy;

import com.moego.common.enums.DeleteStatusEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.StringMoegoUtil;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.business_customer.v1.BusinessPetCoatTypeServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListPetCoatTypeRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.IsCompaniesMigrateRequest;
import com.moego.idl.service.organization.v1.IsCompanyMigrateRequest;
import com.moego.idl.service.organization.v1.IsCompanyMigrateResponse;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.CompanyIdDTO;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.customer.dto.PetCoatDTO;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentPetDetailDTO;
import com.moego.server.grooming.dto.CustomerServiceQueryDTO;
import com.moego.server.grooming.dto.GroomingServiceDTO;
import com.moego.server.grooming.dto.PetDetailInvoiceDTO;
import com.moego.server.grooming.dto.PetServiceDTO;
import com.moego.server.grooming.dto.ServiceCategoryDTO;
import com.moego.server.grooming.mapper.MoeBookOnlineServiceMapper;
import com.moego.server.grooming.mapper.MoeGroomingCustomerServicesMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceCategoryMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceCoatBindingMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceLocationMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.MoeBookOnlineService;
import com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceCategory;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceCoatBinding;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceLocation;
import com.moego.server.grooming.mapstruct.GroomingServiceMapper;
import com.moego.server.grooming.mapstruct.PetCoatMapper;
import com.moego.server.grooming.service.dto.ReportServiceDto;
import com.moego.server.grooming.web.dto.ob.OBPetServiceDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@AllArgsConstructor
public class CompanyGroomingServiceQueryService {

    private static final Integer MAXIMUM_NUMBER_OF_QUERIES = 100;
    private MoeGroomingServiceMapper moeGroomingServiceMapper;
    private PetDetailMapperProxy moeGroomingPetDetailMapper;
    private MoeGroomingServiceCategoryMapper moeGroomingServiceCategoryMapper;
    private MoeGroomingServiceCoatBindingMapper moeGroomingServiceCoatBindingMapper;
    private CompanyServiceGrpc.CompanyServiceBlockingStub companyServiceClient;
    private IBusinessBusinessClient iBusinessBusinessClient;
    private MoeBookOnlineServiceMapper moeBookOnlineServiceMapper;
    private MoeGroomingCustomerServicesMapper moeGroomingCustomerServicesMapper;
    private MoeGroomingServiceLocationMapper moeGroomingServiceLocationMapper;
    private BusinessPetCoatTypeServiceGrpc.BusinessPetCoatTypeServiceBlockingStub
            businessPetCoatTypeServiceBlockingStub;
    private ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceBlockingStub;

    private static final int SHOW_DURATION_TRUE = 1;

    public Boolean companyIsMigrate(Long companyId) {
        IsCompanyMigrateResponse response = companyServiceClient.isCompanyMigrate(
                IsCompanyMigrateRequest.newBuilder().setCompanyId(companyId).build());
        return response.getIsCompanyMigrate();
    }

    public Map<Long, Boolean> companiesIsMigrate(Set<Long> companyIdList) {
        if (CollectionUtils.isEmpty(companyIdList)) {
            return Collections.emptyMap();
        }
        List<Set<Long>> cidsList = CommonUtil.splitListByItemNum(companyIdList, MAXIMUM_NUMBER_OF_QUERIES);
        Map<Long, Boolean> resultMap = new HashMap<>();
        for (Set<Long> cids : cidsList) {
            var response = companyServiceClient.isCompaniesMigrate(IsCompaniesMigrateRequest.newBuilder()
                    .addAllCompanyIds(cids)
                    .build());
            resultMap.putAll(response.getIsCompaniesMigrateMapMap());
        }
        return resultMap;
    }

    public Map<Integer, MoeBookOnlineService> obServiceQueryByIds(
            Long companyId, Integer businessId, List<Integer> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return Collections.emptyMap();
        }
        List<MoeBookOnlineService> serviceObSettingList =
                moeBookOnlineServiceMapper.selectByUniqueIndexBidSids(companyId, businessId, serviceIds);
        Map<Integer, MoeBookOnlineService> serviceObSettingByIdMap = serviceObSettingList.stream()
                .collect(Collectors.toMap(MoeBookOnlineService::getServiceId, Function.identity()));
        Map<Long, ServiceBriefView> serviceMap = serviceBlockingStub
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .addAllServiceIds(serviceIds.stream().map(Long::valueOf).collect(Collectors.toList()))
                        .build())
                .getServicesList()
                .stream()
                .collect(Collectors.toMap(ServiceBriefView::getId, Function.identity(), (a, b) -> a));
        for (Integer serviceId : serviceIds) {
            if (serviceObSettingByIdMap.get(serviceId) != null || !serviceMap.containsKey(serviceId.longValue())) {
                continue;
            }
            // BD 默认 disable，Grooming 默认 enable。reference:
            // https://moegoworkspace.slack.com/archives/C06AXC3T4CB/p1714966602597769
            MoeBookOnlineService obService =
                    switch (serviceMap.get(serviceId.longValue()).getServiceItemType()) {
                        case BOARDING, DAYCARE, EVALUATION -> new MoeBookOnlineService() {
                            {
                                setShowBasePrice(ServiceEnum.SHOW_BASE_PRICE_TRUE);
                                setBookOnlineAvailable(ServiceEnum.BOOK_ONLINE_AVAILABLE_FALSE);
                                setIsAllStaff(ServiceEnum.IS_ALL_STAFF_TRUE);
                                setAllowBookingWithOtherCareType(true);
                                setShowDuration(SHOW_DURATION_TRUE);
                            }
                        };
                        case GROOMING -> new MoeBookOnlineService() {
                            {
                                setShowBasePrice(ServiceEnum.SHOW_BASE_PRICE_TRUE);
                                setBookOnlineAvailable(ServiceEnum.BOOK_ONLINE_AVAILABLE_TRUE);
                                setIsAllStaff(ServiceEnum.IS_ALL_STAFF_TRUE);
                                setAllowBookingWithOtherCareType(true);
                                setShowDuration(SHOW_DURATION_TRUE);
                            }
                        };
                        case DOG_WALKING -> new MoeBookOnlineService() {
                            {
                                setShowBasePrice(ServiceEnum.SHOW_BASE_PRICE_TRUE);
                                setBookOnlineAvailable(ServiceEnum.BOOK_ONLINE_AVAILABLE_FALSE);
                                setIsAllStaff(ServiceEnum.IS_ALL_STAFF_TRUE);
                                setAllowBookingWithOtherCareType(true);
                                setShowDuration(SHOW_DURATION_TRUE);
                            }
                        };
                        case GROUP_CLASS -> new MoeBookOnlineService() {
                            {
                                setShowBasePrice(ServiceEnum.SHOW_BASE_PRICE_TRUE);
                                setBookOnlineAvailable(ServiceEnum.BOOK_ONLINE_AVAILABLE_TRUE);
                                setIsAllStaff(ServiceEnum.IS_ALL_STAFF_TRUE);
                                setAllowBookingWithOtherCareType(true);
                                setShowDuration(SHOW_DURATION_TRUE);
                            }
                        };
                        default -> new MoeBookOnlineService() {
                            {
                                setShowBasePrice(ServiceEnum.SHOW_BASE_PRICE_TRUE);
                                setBookOnlineAvailable(ServiceEnum.BOOK_ONLINE_AVAILABLE_FALSE);
                                setIsAllStaff(ServiceEnum.IS_ALL_STAFF_TRUE);
                                setAllowBookingWithOtherCareType(true);
                                setShowDuration(SHOW_DURATION_TRUE);
                            }
                        };
                    };
            serviceObSettingByIdMap.put(serviceId, obService);
        }
        return serviceObSettingByIdMap;
    }

    public void obServiceQueryForPetDetailDto(
            Integer businessId, List<CustomerGroomingAppointmentPetDetailDTO> serviceDtoList) {
        var companyDto = iBusinessBusinessClient.getCompanyByBusinessId(businessId);
        var companyId = companyDto.getId().longValue();
        var serviceObSettingByIdMap = obServiceQueryByIds(
                companyId,
                businessId,
                serviceDtoList.stream()
                        .map(CustomerGroomingAppointmentPetDetailDTO::getServiceId)
                        .toList());
        for (CustomerGroomingAppointmentPetDetailDTO serviceDto : serviceDtoList) {
            MoeBookOnlineService obSetting = serviceObSettingByIdMap.get(serviceDto.getServiceId());
            serviceDto.setServiceAvailableForBookingOnline(obSetting.getBookOnlineAvailable());
        }
    }

    public void obServiceQuery(Long companyId, Integer businessId, List<MoeGroomingService> serviceList) {
        var serviceObSettingByIdMap = obServiceQueryByIds(
                companyId,
                businessId,
                serviceList.stream().map(MoeGroomingService::getId).toList());
        for (MoeGroomingService service : serviceList) {
            MoeBookOnlineService obSetting = serviceObSettingByIdMap.get(service.getId());
            service.setShowBasePrice(obSetting.getShowBasePrice());
            service.setBookOnlineAvailable(obSetting.getBookOnlineAvailable());
            service.setIsAllStaff(obSetting.getIsAllStaff());
        }
    }

    public void obServiceQueryForPetServiceDTO(Long companyId, Integer businessId, List<PetServiceDTO> serviceList) {
        var serviceObSettingByIdMap = obServiceQueryByIds(
                companyId,
                businessId,
                serviceList.stream().map(PetServiceDTO::getId).toList());
        for (PetServiceDTO service : serviceList) {
            MoeBookOnlineService obSetting = serviceObSettingByIdMap.get(service.getId());
            service.setShowBasePrice(obSetting.getShowBasePrice());
            service.setBookOnlineAvailable(obSetting.getBookOnlineAvailable());
        }
    }

    public List<PetDetailInvoiceDTO> queryPetDetailInvoiceByGroomingId(
            Long companyId, Integer businessId, Integer groomingId) {
        if (companyIsMigrate(companyId)) {
            return moeGroomingPetDetailMapper.queryPetDetailInvoiceByCidBidGid(companyId, businessId, groomingId);
        } else {
            return moeGroomingPetDetailMapper.queryPetDetailInvoiceByGroomingId(groomingId);
        }
    }

    public Boolean groomingServiceCategoryCheckNameIsExist(
            Long companyId,
            Integer businessId,
            String name,
            Byte type,
            Integer updateCategoryId,
            ServiceItemType serviceItemType) {
        if (companyIsMigrate(companyId)) {
            return moeGroomingServiceCategoryMapper.getCategoryCountWithNameByCompanyId(
                            companyId,
                            name,
                            type,
                            updateCategoryId,
                            serviceItemType == null
                                    ? ServiceItemType.GROOMING.getNumber()
                                    : serviceItemType.getNumber())
                    > 0;
        } else {
            return moeGroomingServiceCategoryMapper.getCategoryCountWithName(businessId, name, type, updateCategoryId)
                    > 0;
        }
    }

    public Map<Integer, List<String>> getServiceCoatStringBindingMapNew(Long companyId, Integer businessId) {
        if (companyId == null) {
            var companyDto = iBusinessBusinessClient.getCompanyByBusinessId(businessId);
            companyId = companyDto.getId().longValue();
        }
        if (companyIsMigrate(companyId)) {
            return getServiceCoatStringBindingMap(companyId, null);
        } else {
            return getServiceCoatStringBindingMap(companyId, businessId);
        }
    }

    public Map<Integer, List<GroomingServiceDTO>> queryServiceWithObSettingByBidList(List<Integer> businessIds) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return Map.of();
        }
        Map<Integer, MoeBusinessDto> businessDtoMap = iBusinessBusinessClient.getOnlyBusinessInfoBatch(businessIds);
        // 批量查询是否转移
        Map<Long, Boolean> companyisMigrateMap = companiesIsMigrate(businessDtoMap.values().stream()
                .map(MoeBusinessDto::getCompanyId)
                .filter(cid -> cid != null && cid > 0)
                .map(Integer::longValue)
                .collect(Collectors.toSet()));

        List<Integer> oldDataBidList = new ArrayList<>();
        Map<Integer, List<GroomingServiceDTO>> migratedBusinessData = new HashMap<>();
        for (Integer businessId : businessIds) {
            var businessDto = businessDtoMap.get(businessId);
            if (businessDto == null || businessDto.getCompanyId() == null || businessDto.getCompanyId() <= 0) {
                migratedBusinessData.put(businessId, Collections.emptyList());
                continue;
            }
            var companyId = businessDto.getCompanyId();
            var isMigrate = companyisMigrateMap.get(companyId.longValue());
            if (isMigrate != null && isMigrate) {
                // 转移后的 service，会基于 location 定制，所以只能挨个遍历查询
                var serviceList = moeGroomingServiceMapper.selectByBusinessIdType(
                        companyId.longValue(), businessId, null, (byte) 0, null);
                // override ob setting
                obServiceQuery(companyId.longValue(), businessId, serviceList);
                migratedBusinessData.put(
                        businessId,
                        serviceList.stream()
                                .map(GroomingServiceMapper.INSTANCE::entity2DTO)
                                .toList());
            } else {
                oldDataBidList.add(businessId);
            }
        }
        if (!CollectionUtils.isEmpty(oldDataBidList)) {
            // 为转移的 service，直接保留旧的方法，直接批量查询
            migratedBusinessData.putAll(moeGroomingServiceMapper.selectByBusinessIds(businessIds).stream()
                    .map(GroomingServiceMapper.INSTANCE::entity2DTO)
                    .collect(groupingBy(GroomingServiceDTO::getBusinessId)));
        }
        return migratedBusinessData;
    }

    private Map<Integer, List<String>> getServiceCoatStringBindingMap(Long companyId, Integer businessId) {
        var builder = ListPetCoatTypeRequest.newBuilder();
        builder.setCompanyId(companyId);
        if (businessId != null) {
            builder.setBusinessId(businessId);
        }
        var response = businessPetCoatTypeServiceBlockingStub.listPetCoatType(builder.build());
        List<PetCoatDTO> petCode = PetCoatMapper.INSTANCE.modelToDto(response.getCoatTypesList());
        if (CollectionUtils.isEmpty(petCode)) {
            return Map.of();
        }
        return moeGroomingServiceCoatBindingMapper.selectByCompanyId(companyId).stream()
                .collect(Collectors.toMap(
                        MoeGroomingServiceCoatBinding::getServiceId,
                        coat -> JsonUtil.toList(coat.getCoatIdList(), Integer.class).stream()
                                .map(id -> petCode.stream()
                                        .filter(pet -> pet.getId().equals(id))
                                        .findFirst()
                                        .orElse(null))
                                .filter(Objects::nonNull)
                                .map(dto -> StringMoegoUtil.compressAndLowerCase(dto.getName()))
                                .toList()));
    }

    public Map<Integer, List<Integer>> getServiceCoatBindingMapNew(Long companyId, Integer businessId) {
        if (companyIsMigrate(companyId)) {
            return getServiceCoatBindingMap(companyId, null);
        } else {
            return getServiceCoatBindingMap(companyId, businessId);
        }
    }

    /**
     * 查询 service coat binding 关系
     * key 为 service id
     * value 为 coat id list
     *
     * @return
     */
    private Map<Integer, List<Integer>> getServiceCoatBindingMap(Long companyId, Integer businessId) {
        var builder = ListPetCoatTypeRequest.newBuilder();
        builder.setCompanyId(companyId);
        if (businessId != null) {
            builder.setBusinessId(businessId);
        }
        var response = businessPetCoatTypeServiceBlockingStub.listPetCoatType(builder.build());
        List<PetCoatDTO> petCode = PetCoatMapper.INSTANCE.modelToDto(response.getCoatTypesList());
        if (CollectionUtils.isEmpty(petCode)) {
            return Map.of();
        }
        return moeGroomingServiceCoatBindingMapper.selectByCompanyId(companyId).stream()
                .collect(Collectors.toMap(
                        MoeGroomingServiceCoatBinding::getServiceId,
                        coat -> JsonUtil.toList(coat.getCoatIdList(), Integer.class).stream()
                                .map(id -> petCode.stream()
                                        .filter(pet -> pet.getId().equals(id))
                                        .findFirst()
                                        .orElse(null))
                                .filter(Objects::nonNull)
                                .map(PetCoatDTO::getId)
                                .toList()));
    }

    public List<ReportServiceDto> queryServiceWithCategoryByServiceIds(Integer businessId, Set<Integer> serviceIds) {
        CompanyIdDTO companyIdDto = iBusinessBusinessClient.getCompanyIdByBusinessId(businessId);
        Long companyId = companyIdDto.companyId();
        if (companyIsMigrate(companyId)) {
            return moeGroomingServiceMapper.queryServiceWithCategoryByServiceIdsNew(companyId, serviceIds);
        } else {
            return moeGroomingServiceMapper.queryServiceWithCategoryByServiceIds(businessId, serviceIds);
        }
    }

    public List<ServiceCategoryDTO> groomingServiceCategorySelectCategoryByCidOrBid(
            Long companyId, Integer businessId, Integer type) {
        if (companyId == null) {
            var companyDto = iBusinessBusinessClient.getCompanyByBusinessId(businessId);
            if (companyDto == null || companyDto.getId() == null) {
                return Collections.emptyList();
            }
            companyId = companyDto.getId().longValue();
        }
        if (companyIsMigrate(companyId)) {
            return moeGroomingServiceCategoryMapper.selectCategoryByCompanyId(
                    companyId, type, ServiceItemType.GROOMING.getNumber());
        } else {
            return moeGroomingServiceCategoryMapper.selectCategoryByBusinessId(businessId, type);
        }
    }

    public MoeGroomingService selectByPrimaryKey(Integer id) {
        return moeGroomingServiceMapper.selectByPrimaryKey(id);
    }

    public List<MoeGroomingService> groomingServiceSelectByBusinessIdServiceIds(
            Integer businessId, List<Integer> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return Collections.emptyList();
        }
        var companyDto = iBusinessBusinessClient.getCompanyByBusinessId(businessId);
        if (companyDto == null || companyDto.getId() == null) {
            return Collections.emptyList();
        }
        var companyId = companyDto.getId().longValue();
        if (companyIsMigrate(companyId)) {
            var serviceList =
                    moeGroomingServiceMapper.getServicesByBusinessIdServiceIdsNew(companyId, businessId, serviceIds);
            obServiceQuery(companyId, businessId, serviceList);
            // delete status update for location
            var locationOverrideList =
                    moeGroomingServiceLocationMapper.selectNoDeletedByBusinessIdSids(companyId, businessId, serviceIds);
            List<Integer> validServiceIds = locationOverrideList.stream()
                    .map(MoeGroomingServiceLocation::getServiceId)
                    .toList();
            for (MoeGroomingService service : serviceList) {
                // 没有删除
                // 但对于这个 location 不可见，也算删除
                // 不可见， is_all_location 关闭，关系未绑定
                if (!DeleteStatusEnum.STATUS_DELETE.equals(service.getStatus())
                        && !ServiceEnum.IS_ALL_LOCATION_TRUE.equals(service.getIsAllLocation())
                        && !validServiceIds.contains(service.getId())) {
                    service.setStatus(DeleteStatusEnum.STATUS_NORMAL);
                }
            }
            return serviceList;
        } else {
            return moeGroomingServiceMapper.getServicesByBusinessIdServiceIds(businessId, serviceIds);
        }
    }

    public List<PetServiceDTO> groomingServiceSelectByBusinessIdServiceIds(
            Long companyId, Integer businessId, Integer type) {
        if (companyIsMigrate(companyId)) {
            var serviceList = moeGroomingServiceMapper.selectServiceByBusinessIdNew(companyId, businessId, type);
            obServiceQueryForPetServiceDTO(companyId, businessId, serviceList);
            return serviceList;
        } else {
            return moeGroomingServiceMapper.selectServiceByBusinessId(businessId, type);
        }
    }

    // 查询 打开 obsetting 的 service
    public List<OBPetServiceDTO> groomingServiceSelectObServiceByBusinessId(
            Long companyId, Integer businessId, Integer type) {
        if (companyIsMigrate(companyId)) {
            List<OBPetServiceDTO> allServiceDTOList =
                    moeGroomingServiceMapper.selectOBServiceByBusinessIdNew(companyId, businessId, type);
            List<Integer> serviceIds =
                    allServiceDTOList.stream().map(OBPetServiceDTO::getId).toList();
            Map<Integer, MoeBookOnlineService> obServiceSettingMapById =
                    obServiceQueryByIds(companyId, businessId, serviceIds);
            // filter ob available service
            Iterator<OBPetServiceDTO> iterator = allServiceDTOList.iterator();
            while (iterator.hasNext()) {
                var serviceDto = iterator.next();
                var obServiceSetting = obServiceSettingMapById.get(serviceDto.getId());
                if (obServiceSetting == null
                        || ServiceEnum.BOOK_ONLINE_AVAILABLE_FALSE.equals(obServiceSetting.getBookOnlineAvailable())) {
                    iterator.remove();
                } else {
                    serviceDto.setShowBasePrice(obServiceSetting.getShowBasePrice());
                    serviceDto.setIsAllStaff(obServiceSetting.getIsAllStaff());
                    serviceDto.setBookOnlineAvailable(obServiceSetting.getBookOnlineAvailable());
                    serviceDto.setShowDuration(obServiceSetting.getShowDuration());
                }
            }
            return allServiceDTOList;

        } else {
            return moeGroomingServiceMapper.selectOBServiceByBusinessId(businessId, type);
        }
    }

    public List<MoeGroomingServiceCategory> groomingServiceCategorySelectByBusinessId(
            Long companyId, Integer businessId, Byte type, ServiceItemType serviceItemType) {
        if (companyIsMigrate(companyId)) {
            return moeGroomingServiceCategoryMapper.selectByCompanyId(
                    companyId,
                    type,
                    serviceItemType != null ? serviceItemType.getNumber() : ServiceItemType.GROOMING.getNumber());
        } else {
            return moeGroomingServiceCategoryMapper.selectByBusinessId(businessId, type);
        }
    }

    public List<PetServiceDTO> groomingServiceSelectActiveServiceByBusinessId(
            Long companyId, Integer businessId, Integer type) {
        if (companyIsMigrate(companyId)) {
            return moeGroomingServiceMapper.selectActiveServiceByBusinessIdNew(companyId, businessId, type);
        } else {
            return moeGroomingServiceMapper.selectActiveServiceByBusinessId(businessId, type);
        }
    }

    public List<MoeGroomingCustomerServices> queryCustomizeServices(
            Integer businessId, List<Integer> petIds, List<Integer> serviceIds) {
        var companyDto = iBusinessBusinessClient.getCompanyByBusinessId(businessId);
        var companyId = companyDto.getId().longValue();
        if (companyIsMigrate(companyId)) {
            return moeGroomingCustomerServicesMapper.queryCustomizeServices(companyId, null, petIds, serviceIds);
        } else {
            return moeGroomingCustomerServicesMapper.queryCustomizeServices(null, businessId, petIds, serviceIds);
        }
    }

    public List<MoeGroomingCustomerServices> getCustomizeServices(
            Integer businessId, Integer customerId, List<Integer> petIds, List<Integer> serviceIds, Byte saveType) {
        var companyDto = iBusinessBusinessClient.getCompanyByBusinessId(businessId);
        var companyId = companyDto.getId().longValue();
        if (companyIsMigrate(companyId)) {
            if (CollectionUtils.isEmpty(petIds)) {
                return List.of();
            }
            return moeGroomingCustomerServicesMapper.getCustomizeServicesByPetIds(
                    companyId, petIds, serviceIds, saveType);
        } else {
            return moeGroomingCustomerServicesMapper.getCustomizeServices(
                    null, businessId, customerId, petIds, serviceIds, saveType);
        }
    }

    public List<MoeGroomingCustomerServices> queryCustomerServiceByProperties(
            CustomerServiceQueryDTO customerServiceQueryDTO) {
        if (customerServiceQueryDTO.getCompanyId() == null) {
            var companyDto = iBusinessBusinessClient.getCompanyByBusinessId(customerServiceQueryDTO.getBusinessId());
            customerServiceQueryDTO.setCompanyId(companyDto.getId().longValue());
        }
        if (companyIsMigrate(customerServiceQueryDTO.getCompanyId())) {
            customerServiceQueryDTO.setBusinessId(null);
            return moeGroomingCustomerServicesMapper.queryCustomerServiceByProperties(customerServiceQueryDTO);
        } else {
            customerServiceQueryDTO.setCompanyId(null);
            return moeGroomingCustomerServicesMapper.queryCustomerServiceByProperties(customerServiceQueryDTO);
        }
    }

    public Boolean checkCompanyIdServiceId(Long companyId, Integer serviceId) {
        MoeGroomingService groomingService = moeGroomingServiceMapper.selectByPrimaryKey(serviceId); // auth check
        return (groomingService != null && companyId.equals(groomingService.getCompanyId()));
    }
}
