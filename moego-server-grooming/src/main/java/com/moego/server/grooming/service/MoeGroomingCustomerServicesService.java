package com.moego.server.grooming.service;

import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.CustomerPetEnum;
import com.moego.common.enums.DeleteStatusEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.server.grooming.dto.AddResultDTO;
import com.moego.server.grooming.dto.CustomerServiceQueryDTO;
import com.moego.server.grooming.mapper.MoeGroomingCustomerServicesMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.params.CustomerServiceParams;
import com.moego.server.grooming.service.dto.MoeGroomingServiceDto;
import com.moego.server.grooming.web.vo.CustomerDeleteSaveServiceVo;
import com.moego.server.grooming.web.vo.CustomerSaveServiceVo;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class MoeGroomingCustomerServicesService {

    @Autowired
    private MoeGroomingCustomerServicesMapper moeGroomingCustomerServicesMapper;

    @Autowired
    private CompanyGroomingServiceQueryService companyGroomingServiceQueryService;

    @Autowired
    private MoeGroomingServiceMapper moeGroomingServiceMapper;

    // 0203 @Transactional
    public ResponseResult addCustomerService(CustomerServiceParams customerServiceParams) {
        MoeGroomingService moeGroomingService = moeGroomingServiceMapper.selectByPrimaryKey(
                customerServiceParams.getServiceId()); // query name category description
        customerServiceParams.setServiceName(moeGroomingService.getName());
        customerServiceParams.setCategoryId(moeGroomingService.getCategoryId());
        customerServiceParams.setServiceDetail(moeGroomingService.getDescription());

        // 校验是否存在
        CustomerServiceQueryDTO customerServiceQueryDTO = new CustomerServiceQueryDTO();
        customerServiceQueryDTO.setBusinessId(customerServiceParams.getBusinessId());
        customerServiceQueryDTO.setCustomerId(customerServiceParams.getCustomerId());
        customerServiceQueryDTO.setServiceId(customerServiceParams.getServiceId());
        customerServiceQueryDTO.setPetId(customerServiceParams.getPetId());
        customerServiceQueryDTO.setSaveType(customerServiceParams.getSaveType().intValue());
        customerServiceQueryDTO.setCompanyId(customerServiceParams.getCompanyId());

        List<MoeGroomingCustomerServices> moeGroomingCustomerServices1 =
                companyGroomingServiceQueryService.queryCustomerServiceByProperties(customerServiceQueryDTO);
        // 如果存在则修改覆盖
        if (moeGroomingCustomerServices1 != null && moeGroomingCustomerServices1.size() > 0) {
            customerServiceParams.setId(moeGroomingCustomerServices1.get(0).getId());
            return modifyCustomerService(customerServiceParams);
        }

        MoeGroomingCustomerServices moeGroomingCustomerServices = new MoeGroomingCustomerServices();
        BeanUtils.copyProperties(customerServiceParams, moeGroomingCustomerServices);

        moeGroomingCustomerServices.setCreateTime(CommonUtil.get10Timestamp());
        moeGroomingCustomerServices.setUpdateTime(CommonUtil.get10Timestamp());
        moeGroomingCustomerServices.setStatus(Byte.parseByte("1"));

        moeGroomingCustomerServicesMapper.insertSelective(moeGroomingCustomerServices);

        AddResultDTO addResultDTO = new AddResultDTO();
        addResultDTO.setResult(true);
        addResultDTO.setId(moeGroomingCustomerServices.getId());
        return ResponseResult.success(addResultDTO);
    }

    public ResponseResult modifyCustomerService(CustomerServiceParams customerServiceParams) {
        MoeGroomingCustomerServices moeGroomingCustomerServices = new MoeGroomingCustomerServices();
        BeanUtils.copyProperties(customerServiceParams, moeGroomingCustomerServices);

        moeGroomingCustomerServices.setUpdateTime(CommonUtil.get10Timestamp());

        int i = moeGroomingCustomerServicesMapper.updateByPrimaryKeySelective(moeGroomingCustomerServices);

        return ResponseResult.success(i);
    }

    // 0203 @Transactional
    public Boolean saveCustomerService(
            Integer businessId, Long companyId, Integer staffId, CustomerSaveServiceVo saveServiceVo) {
        MoeGroomingService serviceLast =
                moeGroomingServiceMapper.selectByPrimaryKey(saveServiceVo.getLastServiceId()); // auth check
        MoeGroomingService serviceNew;
        if (!saveServiceVo.getLastServiceId().equals(saveServiceVo.getNewServiceId())) {
            serviceNew =
                    moeGroomingServiceMapper.selectByPrimaryKey(saveServiceVo.getNewServiceId()); // query name type
        } else {
            serviceNew = serviceLast;
        }
        if (serviceNew == null || serviceLast == null) {
            throw new CommonException(ResponseCodeEnum.SERVICE_NOT_FOUND);
        }
        if (!serviceNew.getCompanyId().equals(companyId)
                || !serviceLast.getCompanyId().equals(companyId)) {
            return false;
        }
        // 删除之前的
        moeGroomingCustomerServicesMapper.deleteByLastServiceIdNewServiceIdByCid(
                saveServiceVo.getLastServiceId(),
                saveServiceVo.getNewServiceId(),
                companyId,
                saveServiceVo.getCustomerId(),
                saveServiceVo.getPetId());
        // 新增
        MoeGroomingCustomerServices customerServices = new MoeGroomingCustomerServices();
        customerServices.setBusinessId(businessId);
        customerServices.setCompanyId(companyId);
        customerServices.setCustomerId(saveServiceVo.getCustomerId());
        customerServices.setPetId(saveServiceVo.getPetId());
        customerServices.setCreateBy(staffId);
        customerServices.setServiceId(saveServiceVo.getNewServiceId());
        customerServices.setServiceTime(saveServiceVo.getServiceTime());
        customerServices.setServiceFee(saveServiceVo.getServiceFee());
        customerServices.setServiceName(serviceNew.getName());
        customerServices.setServiceDetail(serviceNew.getDescription());
        customerServices.setServiceType(serviceNew.getType().intValue());
        customerServices.setCategoryId(serviceNew.getCategoryId());
        customerServices.setCreateTime(DateUtil.get10Timestamp());
        customerServices.setUpdateTime(DateUtil.get10Timestamp());
        if (BooleanEnum.VALUE_TRUE.equals(saveServiceVo.getIsSavePrice())) {
            customerServices.setSaveType(CustomerPetEnum.SERVICE_SAVE_TYPE_PRICE);
            moeGroomingCustomerServicesMapper.insertSelective(customerServices);
            log.info("savePriceResultId:" + customerServices.getId().toString());
        }
        if (BooleanEnum.VALUE_TRUE.equals(saveServiceVo.getIsSaveDuration())) {
            customerServices.setSaveType(CustomerPetEnum.SERVICE_SAVE_TYPE_TIME);
            moeGroomingCustomerServicesMapper.insertSelective(customerServices);
            log.info("saveDurationResultId:" + customerServices.getId().toString());
        }
        ActivityLogRecorder.record(
                Action.CREATE, ResourceType.PET_CUSTOMIZED_SERVICE, customerServices.getId(), customerServices);
        return true;
    }

    // 0203 @Transactional
    public Boolean deleteCustomerService(Long companyId, Integer businessId, CustomerDeleteSaveServiceVo deleteVo) {
        CustomerServiceQueryDTO customerServiceQueryDTO = new CustomerServiceQueryDTO();
        customerServiceQueryDTO.setPetId(deleteVo.getPetId());
        customerServiceQueryDTO.setServiceId(deleteVo.getServiceId());
        customerServiceQueryDTO.setCompanyId(companyId);
        List<MoeGroomingCustomerServices> moeGroomingCustomerServicesList =
                companyGroomingServiceQueryService.queryCustomerServiceByProperties(customerServiceQueryDTO);
        for (MoeGroomingCustomerServices groomingCustomerServices : moeGroomingCustomerServicesList) {
            MoeGroomingCustomerServices record = new MoeGroomingCustomerServices();
            record.setId(groomingCustomerServices.getId());
            record.setUpdateTime(CommonUtil.get10Timestamp());
            record.setStatus(DeleteStatusEnum.STATUS_DELETE);
            moeGroomingCustomerServicesMapper.updateByPrimaryKeySelective(record);
            ActivityLogRecorder.record(
                    Action.DELETE,
                    ResourceType.PET_CUSTOMIZED_SERVICE,
                    groomingCustomerServices.getId(),
                    groomingCustomerServices);
        }
        return true;
    }

    public List<MoeGroomingServiceDto> queryCustomerService(Long companyId, Integer businessId, Integer petId) {
        CustomerServiceQueryDTO customerServiceQueryDTO = new CustomerServiceQueryDTO();
        customerServiceQueryDTO.setBusinessId(businessId);
        customerServiceQueryDTO.setPetId(petId);
        customerServiceQueryDTO.setCompanyId(companyId);
        List<MoeGroomingCustomerServices> moeGroomingCustomerServicesList =
                companyGroomingServiceQueryService.queryCustomerServiceByProperties(customerServiceQueryDTO);
        Set<Integer> serviceIdList = new HashSet<>();
        Map<String, MoeGroomingCustomerServices> customerServicesMap = new HashMap<>();
        moeGroomingCustomerServicesList.forEach(moeGroomingCustomerServices -> {
            serviceIdList.add(moeGroomingCustomerServices.getServiceId());
            String mapKey = moeGroomingCustomerServices.getServiceId().toString() + "_"
                    + moeGroomingCustomerServices.getSaveType().toString();
            customerServicesMap.put(mapKey, moeGroomingCustomerServices);
        });
        if (serviceIdList.size() == 0) {
            return new ArrayList<>();
        }
        List<MoeGroomingService> serviceList =
                companyGroomingServiceQueryService.groomingServiceSelectByBusinessIdServiceIds(
                        businessId, new ArrayList<>(serviceIdList));
        List<MoeGroomingServiceDto> returnServiceList = new ArrayList<>();
        for (MoeGroomingService service : serviceList) {
            MoeGroomingServiceDto serviceDto = new MoeGroomingServiceDto();
            BeanUtils.copyProperties(service, serviceDto);
            serviceDto.setIsSaveDuration((byte) 0);
            serviceDto.setIsSavePrice((byte) 0);
            String mapPriceKey = service.getId().toString() + "_1";
            MoeGroomingCustomerServices customerServicesPrice = customerServicesMap.get(mapPriceKey);
            if (customerServicesPrice != null) {
                serviceDto.setPrice(customerServicesPrice.getServiceFee());
                serviceDto.setIsSavePrice((byte) 1);
            }
            String mapTimeKey = service.getId().toString() + "_2";
            MoeGroomingCustomerServices customerServicesTime = customerServicesMap.get(mapTimeKey);
            if (customerServicesTime != null) {
                serviceDto.setDuration(customerServicesTime.getServiceTime());
                serviceDto.setIsSaveDuration((byte) 1);
            }
            returnServiceList.add(serviceDto);
        }
        return returnServiceList;
    }

    public List<MoeGroomingCustomerServices> getCustomerServiceList(
            Integer businessId, List<Integer> petIdList, List<Integer> serviceIdList) {
        if (petIdList != null) {
            petIdList = petIdList.stream().filter(k -> k > 0).distinct().toList();
            if (CollectionUtils.isEmpty(petIdList)) {
                return new ArrayList<>();
            }
        }
        if (serviceIdList != null) {
            serviceIdList = serviceIdList.stream().filter(k -> k > 0).distinct().toList();
            if (CollectionUtils.isEmpty(serviceIdList)) {
                return new ArrayList<>();
            }
        }
        return companyGroomingServiceQueryService.queryCustomizeServices(businessId, petIdList, serviceIdList);
    }

    // 0203 @Transactional
    public ResponseResult addCustomerServices(List<CustomerServiceParams> customerServices) {
        for (CustomerServiceParams customerServiceParams : customerServices) {
            addCustomerService(customerServiceParams);
        }
        return ResponseResult.success(customerServices.size());
    }
}
