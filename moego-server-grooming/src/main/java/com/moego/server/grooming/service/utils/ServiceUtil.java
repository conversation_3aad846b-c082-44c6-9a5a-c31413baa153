package com.moego.server.grooming.service.utils;

import static com.moego.common.utils.CommonUtil.isNormal;

import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/27
 */
public class ServiceUtil {

    public static @Nullable CustomizedServiceView getCustomizedService(
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            @Nullable Integer petId,
            Integer serviceId,
            @Nullable Integer staffId) {
        return customizedServiceList.stream()
                .filter(condAndService -> {
                    var cond = condAndService.getQueryCondition();
                    var serviceIdMatched = Objects.equals(cond.getServiceId(), serviceId.longValue());
                    var petIdMatched = (isNormal(petId) && Objects.equals(petId.longValue(), cond.getPetId()))
                            || (!isNormal(petId) && !isNormal(cond.getPetId()));
                    var staffIdMatched = (isNormal(staffId) && Objects.equals(staffId.longValue(), cond.getStaffId()))
                            || (!isNormal(staffId) && !isNormal(cond.getStaffId()));
                    return serviceIdMatched && petIdMatched && staffIdMatched;
                })
                .findFirst()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .orElse(null);
    }
}
