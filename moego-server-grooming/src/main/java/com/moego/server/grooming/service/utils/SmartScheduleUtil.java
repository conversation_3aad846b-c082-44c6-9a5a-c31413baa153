package com.moego.server.grooming.service.utils;

import com.moego.server.business.dto.ParsedCloseDate;
import com.moego.server.business.dto.StaffSmartScheduleSettingDTO;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.grooming.dto.ss.ScheduleTimeSlot;
import com.moego.server.grooming.service.TimeSlot;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

@Slf4j
public class SmartScheduleUtil {
    public static final Integer SMART_SCHEDULE_BUFFER_TIME = 10;

    /**
     * 根据商家设置的startLocation、endLocation以及customer的地址信息给timeslot填充before、afterAddress
     * 用于后续调用GoogleMap API获取距离和驾驶时间
     * 1.timeslot如果有beforeAppt、afterAppt，则根据appt的customerId获取顾客地址信息
     * 2.如无beforeAppt、afterAppt或者customer无地址信息，则：
     * 1）第一个timeslot设置为商家的startLocation，最后一个timeslot设置为商家的endLocation
     * 2）其它timeslot的beforeAddress设置为前一个的beforeAddress，afterAddress设置为后一个timeslot的afterAddress
     *
     * @param timeSlots       上一步根据working hour、appt列表构造出的timeslots
     * @param staffSsSetting  staff smart schedule 设置
     * @param customerAddress 顾客地址Map
     */
    public static void fillServiceAddressV2(
            List<TimeSlot> timeSlots,
            StaffSmartScheduleSettingDTO staffSsSetting,
            Map<Integer, CustomerAddressDto> customerAddress) {
        // ss location lat and lng
        String startLocationLat = "";
        String startLocationLng = "";
        String endLocationLat = "";
        String endLocationLng = "";
        if (Objects.nonNull(staffSsSetting) && Objects.nonNull(staffSsSetting.getLocation())) {
            startLocationLat = staffSsSetting.getLocation().getStartLat();
            startLocationLng = staffSsSetting.getLocation().getStartLng();
            endLocationLat = staffSsSetting.getLocation().getEndLat();
            endLocationLng = staffSsSetting.getLocation().getEndLng();
        }
        for (int i = 0; i < timeSlots.size(); i++) {
            TimeSlot timeSlot = timeSlots.get(i);
            if (timeSlot.getBeforeApptId() < 0) {
                assert i == 0;
                // start location
                timeSlot.setBeforeLat(startLocationLat);
                timeSlot.setBeforeLng(startLocationLng);
            } else {
                boolean beforeAddressExist = false;
                if (timeSlot.getBeforeCustomerId() > 0 && !CollectionUtils.isEmpty(customerAddress)) {
                    CustomerAddressDto beforeAddress = customerAddress.get(timeSlot.getBeforeCustomerId());
                    if (beforeAddress != null && beforeAddress.getCustomerAddressId() != null) {
                        beforeAddressExist = true;
                        timeSlot.setBeforeLat(beforeAddress.getLat());
                        timeSlot.setBeforeLng(beforeAddress.getLng());
                    }
                }
                if (!beforeAddressExist) {
                    // find previous timeSlot addresses(前一个time slot已经搜索了更前面的slot，所以只需检查前面一个slot就行，无需递归检查到start slot）
                    // 复杂度 o（N）
                    if (i == 0) {
                        // start location
                        timeSlot.setBeforeLat(startLocationLat);
                        timeSlot.setBeforeLng(startLocationLng);
                    } else {
                        TimeSlot prevSlot = timeSlots.get(i - 1);
                        if (prevSlot.isBeforeAddressValid()) {
                            timeSlot.setBeforeLat(prevSlot.getBeforeLat());
                            timeSlot.setBeforeLng(prevSlot.getBeforeLng());
                        }
                    }
                }
            }
        }
        for (int i = timeSlots.size() - 1; i >= 0; i--) {
            TimeSlot timeSlot = timeSlots.get(i);
            if (timeSlot.getAfterApptId() < 0) {
                assert i == timeSlots.size() - 1;
                // end location
                timeSlot.setAfterLat(endLocationLat);
                timeSlot.setAfterLng(endLocationLng);
            } else {
                boolean afterAddressExist = false;
                if (timeSlot.getAfterCustomerId() > 0 && !CollectionUtils.isEmpty(customerAddress)) {
                    CustomerAddressDto afterAddress = customerAddress.get(timeSlot.getAfterCustomerId());
                    if (afterAddress != null && afterAddress.getCustomerAddressId() != null) {
                        afterAddressExist = true;
                        timeSlot.setAfterLat(afterAddress.getLat());
                        timeSlot.setAfterLng(afterAddress.getLng());
                    }
                }
                if (!afterAddressExist) {
                    if (i == timeSlots.size() - 1) {
                        // end location
                        timeSlot.setAfterLat(endLocationLat);
                        timeSlot.setAfterLng(endLocationLng);
                    } else {
                        // find next timeSlot addresses
                        TimeSlot nextSlot = timeSlots.get(i + 1);
                        if (nextSlot.isAfterAddressValid()) {
                            timeSlot.setAfterLat(nextSlot.getAfterLat());
                            timeSlot.setAfterLng(nextSlot.getAfterLng());
                        }
                    }
                }
            }
        }
    }

    public static List<TimeSlot> filterNonWorkingTime(List<TimeSlot> result, List<TimeRangeDto> obTimes) {
        if (obTimes != null && obTimes.size() > 1) {
            List<TimeSlot> newResultList = new ArrayList<>();
            for (int j = 0; j < result.size(); j++) {
                TimeSlot slot = result.get(j);
                List<TimeRangeDto> splitServices = new ArrayList<>();
                for (int i = 0; i < obTimes.size(); i++) {
                    TimeRangeDto range = obTimes.get(i);
                    if ( // 2
                    range.getStartTime() <= slot.getStart()
                            && range.getEndTime() > slot.getStart()
                            && range.getEndTime() <= slot.getEnd()) {
                        splitServices.add(new TimeRangeDto(slot.getStart(), range.getEndTime()));
                    } else if (
                    /* 5 */ range.getStartTime() <= slot.getStart() && range.getEndTime() > slot.getEnd()) {
                        splitServices.add(new TimeRangeDto(slot.getStart(), slot.getEnd()));
                    } else if (
                    /* 3 */ range.getStartTime() > slot.getStart() && range.getEndTime() <= slot.getEnd()) {
                        splitServices.add(new TimeRangeDto(range.getStartTime(), range.getEndTime()));
                    } else if (
                    /* 4 */
                    range.getStartTime() > slot.getStart()
                            && range.getStartTime() < slot.getEnd()
                            && range.getEndTime() > slot.getEnd()) {
                        splitServices.add(new TimeRangeDto(range.getStartTime(), slot.getEnd()));
                    }
                    // else if (range.getEndTime() <= slot.getStart() || range.getStartTime() >= slot.getEnd()) {
                    // 1 or 6
                }
                if (CollectionUtils.isEmpty(splitServices)) {
                    slot.setStart(slot.getEnd());
                    newResultList.add(slot);
                } else {
                    if (splitServices.size() == 1) {
                        slot.setStart(splitServices.get(0).getStartTime());
                        slot.setEnd(splitServices.get(0).getEndTime());
                        newResultList.add(slot);
                    } else {
                        for (TimeRangeDto rangeDto : splitServices) {
                            TimeSlot newSlot = new TimeSlot();
                            BeanUtils.copyProperties(slot, newSlot);
                            newSlot.setStart(rangeDto.getStartTime());
                            newSlot.setEnd(rangeDto.getEndTime());
                            newResultList.add(newSlot);
                        }
                    }
                }
            }
            return newResultList;
        } else {
            return result;
        }
    }

    public static boolean isInClosedDate(LocalDate currDay, List<ParsedCloseDate> ranges) {
        for (ParsedCloseDate range : ranges) {
            // [2020-05-22, 2020-05-27]  22到27的所有日期都是close day，  currDay = 2020-05-24
            // close 条件： currDay >= range.start && currDay <= range.end
            if (!currDay.isBefore(range.getStart()) && !currDay.isAfter(range.getEnd())) {
                return true;
            }
        }
        return false;
    }

    public static ScheduleTimeSlot convertTimeSlot(TimeSlot availableTimes) {
        return ScheduleTimeSlot.builder()
                .startTime(availableTimes.getStart())
                .endTime(availableTimes.getEnd())
                .driveInMinutes(availableTimes.getDriveInMinutes())
                .driveOutMinutes(availableTimes.getDriveOutMinutes())
                .driveInMiles(availableTimes.getDriveInMiles())
                .driveOutMiles(availableTimes.getDriveOutMiles())
                .isDriveFromStart(availableTimes.getBeforeApptId() < 0)
                .isDriveToEnd(availableTimes.getAfterApptId() < 0)
                .build();
    }

    public static List<ScheduleTimeSlot> convertTimeSlotList(List<TimeSlot> availableTimes) {
        return availableTimes.stream().map(SmartScheduleUtil::convertTimeSlot).collect(Collectors.toList());
    }

    public static Map<String, Map<Integer, List<TimeRangeDto>>> staffPerDayWorkingTimeIntersection(
            Map<String, Map<Integer, List<TimeRangeDto>>> curWorkingTime,
            Map<String, Map<Integer, List<TimeRangeDto>>> workingTimeToIntersect) {
        Map<String, Map<Integer, List<TimeRangeDto>>> interSectionResult = new HashMap<>();
        if (CollectionUtils.isEmpty(curWorkingTime) || CollectionUtils.isEmpty(workingTimeToIntersect)) {
            return interSectionResult;
        }
        for (Map.Entry<String, Map<Integer, List<TimeRangeDto>>> entry : curWorkingTime.entrySet()) {
            String date = entry.getKey();
            Map<Integer, List<TimeRangeDto>> staffWorkingTime = entry.getValue();
            Map<Integer, List<TimeRangeDto>> interSectionWorkingTime = workingTimeToIntersect.get(date);
            if (CollectionUtils.isEmpty(staffWorkingTime) || CollectionUtils.isEmpty(interSectionWorkingTime)) {
                continue;
            }
            for (Map.Entry<Integer, List<TimeRangeDto>> staffWorkingTimeEntry : staffWorkingTime.entrySet()) {
                Integer staffId = staffWorkingTimeEntry.getKey();
                List<TimeRangeDto> staffInterSectionResult = timeRangeListIntersection(
                        staffWorkingTimeEntry.getValue(), interSectionWorkingTime.get(staffId));
                if (!CollectionUtils.isEmpty(staffInterSectionResult)) {
                    interSectionResult
                            .computeIfAbsent(date, k -> new HashMap<>())
                            .put(staffId, staffInterSectionResult);
                }
            }
        }
        return interSectionResult;
    }

    public static List<TimeRangeDto> timeRangeListIntersection(
            List<TimeRangeDto> timeRangeList1, List<TimeRangeDto> timeRangeList2) {
        List<TimeRangeDto> interSectionResult = new ArrayList<>();
        if (CollectionUtils.isEmpty(timeRangeList1) || CollectionUtils.isEmpty(timeRangeList2)) {
            return interSectionResult;
        }
        for (TimeRangeDto toIntersect : timeRangeList2) {
            interSectionResult.addAll(timeRangeIntersect(timeRangeList1, toIntersect));
        }
        return interSectionResult;
    }

    static List<TimeRangeDto> timeRangeIntersect(List<TimeRangeDto> timeRangeList, TimeRangeDto toIntersect) {
        List<TimeRangeDto> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(timeRangeList)) {
            return result;
        }
        for (TimeRangeDto existTimeRange : timeRangeList) {
            if (existTimeRange.getEndTime() <= toIntersect.getStartTime()) {
                continue;
            }
            if (existTimeRange.getStartTime() < toIntersect.getStartTime()) {
                if (existTimeRange.getEndTime() > toIntersect.getEndTime()) {
                    result.add(new TimeRangeDto(toIntersect.getStartTime(), toIntersect.getEndTime()));
                } else {
                    result.add(new TimeRangeDto(toIntersect.getStartTime(), existTimeRange.getEndTime()));
                }
                continue;
            }
            if (existTimeRange.getStartTime() < toIntersect.getEndTime()) {
                if (existTimeRange.getEndTime() > toIntersect.getEndTime()) {
                    result.add(new TimeRangeDto(existTimeRange.getStartTime(), toIntersect.getEndTime()));
                } else {
                    result.add(new TimeRangeDto(existTimeRange.getStartTime(), existTimeRange.getEndTime()));
                }
            }
        }
        return result;
    }

    public static List<TimeSlot> timeSlotListIntersection(
            List<TimeSlot> timeSlotList, List<TimeRangeDto> timeRangeList) {
        if (CollectionUtils.isEmpty(timeRangeList)) {
            return timeSlotList;
        }
        List<TimeSlot> interSectionResult = new ArrayList<>();
        for (TimeRangeDto toIntersect : timeRangeList) {
            interSectionResult.addAll(timeSlotIntersect(timeSlotList, toIntersect));
        }
        return interSectionResult;
    }

    static List<TimeSlot> timeSlotIntersect(List<TimeSlot> timeSlots, TimeRangeDto toIntersect) {
        List<TimeSlot> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(timeSlots)) {
            return result;
        }
        for (TimeSlot timeSlot : timeSlots) {
            if (timeSlot.getEnd() <= toIntersect.getStartTime()) {
                continue;
            }
            if (timeSlot.getStart() < toIntersect.getStartTime()) {
                TimeSlot newSlot = new TimeSlot();
                BeanUtils.copyProperties(timeSlot, newSlot);
                newSlot.setStart(toIntersect.getStartTime());
                if (timeSlot.getEnd() > toIntersect.getEndTime()) {
                    newSlot.setEnd(toIntersect.getEndTime());
                }
                result.add(newSlot);
                continue;
            }
            if (timeSlot.getStart() < toIntersect.getEndTime()) {
                TimeSlot newSlot = new TimeSlot();
                BeanUtils.copyProperties(timeSlot, newSlot);
                if (timeSlot.getEnd() > toIntersect.getEndTime()) {
                    newSlot.setEnd(toIntersect.getEndTime());
                }
                result.add(newSlot);
            }
        }
        return result;
    }

    /**
     * 根据buffer time和service duration计算staff当前timeSlot下的total duration
     * @param timeSlot
     * @param serviceDuration
     * @param bufferTime
     * @return
     */
    public static Integer getTotalDuration(TimeSlot timeSlot, Integer serviceDuration, Integer bufferTime) {
        if (timeSlot == null) {
            return 0;
        }

        if (bufferTime == null || bufferTime == 0) {
            return serviceDuration;
        }

        if (bufferTime > 0) {
            // 前后都有预约 buffer_time需*2, block的情况不需要
            if ((timeSlot.getBeforeApptId() > 0 && !timeSlot.isBeforeApptIsBlock())
                    && (timeSlot.getAfterApptId() > 0 && !timeSlot.isAfterApptIsBlock())) {
                return serviceDuration + bufferTime * 2;
            } else {
                return serviceDuration + bufferTime;
            }
        } else {
            return Math.max((serviceDuration + bufferTime), 0);
        }
    }

    /**
     * 根据buffer time调整timeSlot的startTime
     * @param timeSlots
     * @param bufferTime
     * @return
     */
    public static void timeSlotOffset(List<TimeSlot> timeSlots, Integer bufferTime) {
        if (CollectionUtils.isEmpty(timeSlots) || bufferTime == null || bufferTime == 0) {
            return;
        }

        for (TimeSlot timeSlot : timeSlots) {
            // block不需要调整
            if (timeSlot.getBeforeApptId() > 0 && !timeSlot.isBeforeApptIsBlock()) {
                timeSlot.setStart(timeSlot.getStart() + bufferTime);
            }
        }
    }
}
