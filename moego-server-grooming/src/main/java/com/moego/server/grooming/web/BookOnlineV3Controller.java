package com.moego.server.grooming.web;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.common.distributed.LockManager;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.common.util.RequestUtils;
import com.moego.server.customer.client.ICustomerOnlineBookingClient;
import com.moego.server.customer.dto.OBMainSessionDTO;
import com.moego.server.customer.params.CreateOBLoginTokenParams;
import com.moego.server.grooming.enums.AppointmentSourcePlatform;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.po.BusinessCompanyPO;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.params.BookOnlineCustomerParams;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.grooming.properties.GoogleReserveProperties;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.dto.OBSubmitResult;
import com.moego.server.grooming.service.ob.OBGroomingService;
import com.moego.server.grooming.service.ob.OBLandingPageConfigService;
import com.moego.server.grooming.web.params.GoogleReserveConversionTrackingParam;
import com.moego.server.grooming.web.params.SubmitBookingRequestParams;
import com.moego.server.grooming.web.vo.SubmitBookingRequestResult;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import java.util.Collections;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @since 2024/12/24
 */
@RestController
@RequestMapping("/grooming/ob/v3/client")
@RequiredArgsConstructor
public class BookOnlineV3Controller {
    private static final Logger log = LoggerFactory.getLogger(BookOnlineV3Controller.class);

    private final MoeGroomingBookOnlineService moeGroomingBookOnlineService;
    private final LockManager moegoLockManager;
    private final OBGroomingService groomingService;
    private final ICustomerOnlineBookingClient iCustomerOnlineBookingClient;
    private final OBLandingPageConfigService landingPageConfigService;
    private final RestTemplate restTemplate;
    private final GoogleReserveProperties googleReserveProperties;
    private final AppointmentMapperProxy appointmentMapper;

    /**
     * Submit a booking request for Grooming.
     *
     * <p> 这个接口不再处理 prepay 逻辑，将 创建 BookingRequest 和 pay 拆成两个接口
     */
    @PostMapping("/submit")
    @Auth(AuthType.OB)
    public SubmitBookingRequestResult submitBookingRequest(
            @Valid @RequestBody SubmitBookingRequestParams submitBookingRequestParams,
            OBAnonymousParams anonymousParams,
            // see
            // https://developers.google.com/maps-booking/verticals/appointment-booking/guides/integration/conversion-tracking
            @Parameter(hidden = true) @CookieValue(value = "_rwg_token", required = false) String rwgToken) {
        var obSession = checkOBSession(anonymousParams);
        // check service, init add on list
        for (BookOnlinePetParams petDataDTO : submitBookingRequestParams.getPetData()) {
            if (petDataDTO.getServiceId() == null) {
                throw bizException(Code.CODE_PARAMS_ERROR, "Invalid param. Please refresh the page and try again");
            }
            if (petDataDTO.getAddOnIds() == null) {
                petDataDTO.setAddOnIds(Collections.emptyList());
            }
        }
        // 用 session 里的 customer id 覆盖前端入参传的 customer id
        var sessionCustomerId = AuthContext.get().getCustomerId();
        submitBookingRequestParams.getCustomerData().setCustomerId(sessionCustomerId);

        // 如果是 new client，那么 sessionCustomerId 为 null， submit 后，会返回一个新创建的 customer id
        OBSubmitResult obSubmitResult = submit(submitBookingRequestParams, anonymousParams);
        Integer submitCustomerId = obSubmitResult.getCustomerId();

        // Reserve with Google, send conversion event after submit
        if (StringUtils.hasText(rwgToken)) {
            // Don't block the main process
            ThreadPool.execute(() -> sendConversionEvent(rwgToken, obSubmitResult.getAppointmentId()));
        }

        if (!submitCustomerId.equals(sessionCustomerId)) {
            // submit 返回的 customer id 与会话中的 customer id 不一致，说明是 new client，需要为其创建子会话
            CreateOBLoginTokenParams params = CreateOBLoginTokenParams.builder()
                    .customerId(submitCustomerId)
                    .ip(RequestUtils.getIP())
                    .userAgent(RequestUtils.getUserAgent())
                    .refererLink(RequestUtils.getReferer())
                    .mainSession(obSession)
                    .build();

            iCustomerOnlineBookingClient.genLoginToken(params);
        }

        var result = new SubmitBookingRequestResult();
        result.setAutoAcceptRequest(obSubmitResult.getAutoAcceptRequest());
        result.setCustomerId(obSubmitResult.getCustomerId());
        result.setOrderId(obSubmitResult.getOrderId());
        return result;
    }

    private OBSubmitResult submit(
            SubmitBookingRequestParams submitBookingRequestParams, OBAnonymousParams anonymousParams) {
        BusinessCompanyPO businessCompanyPO = mustGetBusinessCompanyPO(anonymousParams);

        // 填充 business id, 查询 ob 配置
        var obParams = toBookOnlineSubmitParams(submitBookingRequestParams, businessCompanyPO);

        MoeBusinessBookOnline obBusiness =
                moeGroomingBookOnlineService.getSettingInfoByBusinessId(businessCompanyPO.getBusinessId());

        // check ob config、apptTime
        moeGroomingBookOnlineService.submitParamsCheck(obBusiness, obParams);

        var multiStaffIds = obParams.getPetData().stream()
                .map(BookOnlinePetParams::getStaffId)
                .filter(Objects::nonNull)
                .distinct()
                .sorted()
                .toList();

        // disable select time submit
        if ((Objects.isNull(obParams.getStaffId()) && CollectionUtils.isEmpty(multiStaffIds))
                || !StringUtils.hasText(obParams.getAppointmentDate())) {
            return groomingService.submitBookingRequest(obParams, obBusiness);
        }

        // by working hour / by slot submit

        if (!CollectionUtils.isEmpty(multiStaffIds)) {
            String lockKey = moegoLockManager.getResourceKey(
                    LockManager.OB_SUBMIT, multiStaffIds + obParams.getAppointmentDate());
            String randomValue = CommonUtil.getUuid();

            try {
                if (!moegoLockManager.lockWithRetry(lockKey, randomValue)) {
                    throw bizException(Code.CODE_PARALLEL_ERROR, "get lock failed for " + lockKey);
                }
                return groomingService.submitBookingRequest(obParams, obBusiness);
            } finally {
                moegoLockManager.unlock(lockKey, randomValue);
            }
        }

        String lockKey = moegoLockManager.getResourceKey(
                LockManager.OB_SUBMIT, obParams.getStaffId() + obParams.getAppointmentDate());
        String randomValue = CommonUtil.getUuid();

        try {
            if (!moegoLockManager.lockWithRetry(lockKey, randomValue)) {
                throw bizException(Code.CODE_PARALLEL_ERROR, "get lock failed for " + lockKey);
            }
            return groomingService.submitBookingRequest(obParams, obBusiness);
        } finally {
            moegoLockManager.unlock(lockKey, randomValue);
        }
    }

    private static BookOnlineSubmitParams toBookOnlineSubmitParams(
            SubmitBookingRequestParams submitBookingRequestParams, BusinessCompanyPO businessCompanyPO) {
        var bookOnlineSubmitParams = new BookOnlineSubmitParams();
        bookOnlineSubmitParams.setBusinessId(businessCompanyPO.getBusinessId());
        bookOnlineSubmitParams.setCompanyId(businessCompanyPO.getCompanyId());
        bookOnlineSubmitParams.setCustomerData(
                toBookOnlineCustomerParams(submitBookingRequestParams.getCustomerData()));
        bookOnlineSubmitParams.setPetData(submitBookingRequestParams.getPetData());
        bookOnlineSubmitParams.setAppointmentDate(submitBookingRequestParams.getAppointmentDate());
        bookOnlineSubmitParams.setAppointmentStartTime(submitBookingRequestParams.getAppointmentStartTime());
        bookOnlineSubmitParams.setOutOfArea(submitBookingRequestParams.getOutOfArea());
        bookOnlineSubmitParams.setStaffId(submitBookingRequestParams.getStaffId());
        bookOnlineSubmitParams.setOrderId(submitBookingRequestParams.getOrderId());
        bookOnlineSubmitParams.setIsAgreePolicy(submitBookingRequestParams.getIsAgreePolicy());
        bookOnlineSubmitParams.setAgreements(submitBookingRequestParams.getAgreements());
        bookOnlineSubmitParams.setNote(submitBookingRequestParams.getNote());
        bookOnlineSubmitParams.setBookOnlineCustomerAdditionalParams(
                submitBookingRequestParams.getBookOnlineCustomerAdditionalParams());
        bookOnlineSubmitParams.setPrepayGuid(submitBookingRequestParams.getPrepayGuid());
        bookOnlineSubmitParams.setPreAuthDetail(submitBookingRequestParams.getPreAuthDetail());
        bookOnlineSubmitParams.setDiscountCodeParams(submitBookingRequestParams.getDiscountCodeParams());
        return bookOnlineSubmitParams;
    }

    private static BookOnlineCustomerParams toBookOnlineCustomerParams(SubmitBookingRequestParams.Customer customer) {
        var bookOnlineCustomerParams = new BookOnlineCustomerParams();
        bookOnlineCustomerParams.setFirstName(customer.getFirstName());
        bookOnlineCustomerParams.setLastName(customer.getLastName());
        bookOnlineCustomerParams.setPhoneNumber(customer.getPhoneNumber());
        bookOnlineCustomerParams.setEmail(customer.getEmail());
        bookOnlineCustomerParams.setAddress1(customer.getAddress1());
        bookOnlineCustomerParams.setAddress2(customer.getAddress2());
        bookOnlineCustomerParams.setCity(customer.getCity());
        bookOnlineCustomerParams.setState(customer.getState());
        bookOnlineCustomerParams.setZipcode(customer.getZipcode());
        bookOnlineCustomerParams.setCountry(customer.getCountry());
        bookOnlineCustomerParams.setLat(customer.getLat());
        bookOnlineCustomerParams.setLng(customer.getLng());
        bookOnlineCustomerParams.setAnswersMap(customer.getAnswersMap());
        bookOnlineCustomerParams.setAddress(customer.getAddress());
        bookOnlineCustomerParams.setCustomerId(customer.getCustomerId());
        bookOnlineCustomerParams.setAddressId(customer.getAddressId());
        bookOnlineCustomerParams.setChargeToken(customer.getChargeToken());
        bookOnlineCustomerParams.setHasStripeCard(customer.getHasStripeCard());
        bookOnlineCustomerParams.setStripeCustomerId(customer.getStripeCustomerId());
        bookOnlineCustomerParams.setIsProfileRequestAddress(customer.getIsProfileRequestAddress());
        bookOnlineCustomerParams.setBirthday(customer.getBirthday());

        setContact(bookOnlineCustomerParams, customer);
        return bookOnlineCustomerParams;
    }

    private static void setContact(
            BookOnlineCustomerParams bookOnlineCustomerParams, SubmitBookingRequestParams.Customer customer) {

        if (Objects.nonNull(customer.getEmergencyContact())
                && !Objects.equals(
                        CommonUtil.getNumeric(customer.getEmergencyContact().getPhoneNumber()),
                        CommonUtil.getNumeric(customer.getPhoneNumber()))) {
            var contact = customer.getEmergencyContact();
            bookOnlineCustomerParams.setEmergencyContactFirstName(contact.getFirstName());
            bookOnlineCustomerParams.setEmergencyContactLastName(contact.getLastName());
            bookOnlineCustomerParams.setEmergencyContactPhone(contact.getPhoneNumber());
        }

        // pickup contact
        if (Objects.nonNull(customer.getPickupContact())
                && !Objects.equals(
                        CommonUtil.getNumeric(customer.getPickupContact().getPhoneNumber()),
                        CommonUtil.getNumeric(customer.getPhoneNumber()))) {
            var contact = customer.getPickupContact();
            bookOnlineCustomerParams.setPickupContactFirstName(contact.getFirstName());
            bookOnlineCustomerParams.setPickupContactLastName(contact.getLastName());
            bookOnlineCustomerParams.setPickupContactPhone(contact.getPhoneNumber());
        }
    }

    private BusinessCompanyPO mustGetBusinessCompanyPO(OBAnonymousParams anonymousParams) {
        var po = landingPageConfigService.getBusinessIdAndCompanyIdByAnonymous(anonymousParams);
        if (po == null) {
            throw bizException(
                    Code.CODE_PARALLEL_ERROR, "get business info failed: " + JsonUtil.toJson(anonymousParams));
        }
        return po;
    }

    /**
     * 检查 OB 主会话参数，需要有 session id，account id (<-1) 和 OB name
     *
     * @param anonymousParams ob name
     * @return OBMainSessionDTO
     */
    private OBMainSessionDTO checkOBSession(OBAnonymousParams anonymousParams) {
        var context = AuthContext.get();
        // main session id is required
        var sessionId = context.sessionId();
        if (sessionId == null) {
            throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
        }
        // main account id should be < -1
        var accountId = context.accountId();
        if (accountId == null || accountId >= -1) {
            throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
        }

        // obName is required, 优先解析 domain（自定义域名的 OB）, 然后解析 name （统一域名的 ob）
        var obName = getOBName(anonymousParams);
        if (!StringUtils.hasText(obName)) {
            throw bizException(Code.CODE_BOOK_ONLINE_NAME_INVALID);
        }

        return new OBMainSessionDTO(sessionId, accountId, obName);
    }

    private String getOBName(OBAnonymousParams anonymousParams) {
        var obName = anonymousParams.getDomain();
        if (StringUtils.hasText(obName)) {
            return obName;
        }
        return anonymousParams.getName();
    }

    private void sendConversionEvent(String rwgToken, Integer appointmentId) {
        // send conversion event
        GoogleReserveConversionTrackingParam param = new GoogleReserveConversionTrackingParam()
                .setRwgToken(rwgToken)
                .setConversionPartnerId(googleReserveProperties.getPartnerId());
        try {
            restTemplate.postForEntity(googleReserveProperties.getConversionTrackingUrl(), param, String.class);
        } catch (RestClientException e) {
            log.error(
                    "Failed to send conversion event, appointment: {}, param: {}",
                    appointmentId,
                    JsonUtil.toJson(param),
                    e);
        }
        // set source platform to RESERVE_WITH_GOOGLE
        MoeGroomingAppointment appointment = new MoeGroomingAppointment();
        appointment.setId(appointmentId);
        appointment.setSourcePlatform(AppointmentSourcePlatform.RESERVE_WITH_GOOGLE.name());
        appointmentMapper.updateByPrimaryKeySelective(appointment);
    }
}
