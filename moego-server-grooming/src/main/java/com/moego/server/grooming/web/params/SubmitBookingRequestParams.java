package com.moego.server.grooming.web.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.moego.server.grooming.params.BookOnlineAgreementParams;
import com.moego.server.grooming.params.BookOnlineCustomerAdditionalParams;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.ob.DiscountCodeParams;
import com.moego.server.grooming.params.ob.PreAuthDetailParams;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/24
 */
@Data
public class SubmitBookingRequestParams {

    @NotNull
    @Valid
    private SubmitBookingRequestParams.Customer customerData;

    @NotNull
    @Valid
    private List<@NotNull @Valid BookOnlinePetParams> petData;

    @Pattern(regexp = "^(\\d{4}-\\d{2}-\\d{2})?$")
    @Nullable // disable select time
    private String appointmentDate;

    @Max(1440)
    @Nullable // disable select time or date only
    private Integer appointmentStartTime;

    /**
     * for mobile grooming
     */
    private Byte outOfArea;

    @Nullable // disable staff selection
    private Integer staffId;

    private String orderId;

    private Byte isAgreePolicy;

    @NotNull
    List<BookOnlineAgreementParams> agreements;

    private String note;

    private BookOnlineCustomerAdditionalParams bookOnlineCustomerAdditionalParams;

    @Schema(description = "prepay guid，用于找到支付记录，绑定到新创建的订单，无支付时不需传")
    private String prepayGuid;

    private PreAuthDetailParams preAuthDetail;
    private DiscountCodeParams discountCodeParams;

    @Data
    public static class Customer {

        @Size(max = 50)
        private String firstName;

        @Size(max = 50)
        private String lastName;

        @Size(max = 30)
        private String phoneNumber;

        @Size(max = 50)
        private String email;

        @Size(max = 255)
        private String address1;

        @Size(max = 255)
        private String address2;

        @Size(max = 255)
        private String city;

        @Size(max = 255)
        private String state;

        @Size(max = 10)
        private String zipcode;

        @Size(max = 255)
        private String country;

        @Size(max = 50)
        private String lat;

        @Size(max = 50)
        private String lng;

        private Map<String, Object> answersMap;
        /**
         * used to save address which is combined by fileds above
         */
        private String address;
        /**
         * new or old customer
         */
        private Integer customerId;

        private Integer addressId;

        @Schema(description = "credit card token for stripe or square")
        @Size(max = 128)
        private String chargeToken;

        @Schema(description = "true if client supplied")
        private Boolean hasStripeCard;

        private String stripeCustomerId;

        private Boolean isProfileRequestAddress;

        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime birthday;

        @Data
        public static class Contact {
            private String firstName;
            private String lastName;
            private String phoneNumber;
        }

        private Contact emergencyContact;
        private Contact pickupContact;
    }
}
