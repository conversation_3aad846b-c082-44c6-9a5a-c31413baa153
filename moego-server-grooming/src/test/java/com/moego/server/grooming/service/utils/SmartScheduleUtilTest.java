package com.moego.server.grooming.service.utils;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.grooming.service.TimeSlot;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

class SmartScheduleUtilTest {

    /**
     * getTotalDuration 方法单元测试
     */
    @Nested
    class GetTotalDurationTests {

        @Test
        void returnZeroWhenTimeSlotIsNull() {
            // Arrange
            var serviceDuration = 30;
            Integer bufferTime = 10;
            // Act
            var actual = SmartScheduleUtil.getTotalDuration(null, serviceDuration, bufferTime);
            // Assert
            assertThat(actual).isZero();
        }

        @Test
        void returnServiceDurationWhenBufferTimeIsNull() {
            // Arrange
            var timeSlot = TimeSlot.builder().build();
            var serviceDuration = 45;
            Integer bufferTime = null;
            // Act
            var actual = SmartScheduleUtil.getTotalDuration(timeSlot, serviceDuration, bufferTime);
            // Assert
            assertThat(actual).isEqualTo(45);
        }

        @Test
        void returnServiceDurationWhenBufferTimeIsZero() {
            // Arrange
            var timeSlot = TimeSlot.builder().build();
            var serviceDuration = 60;
            var bufferTime = 0;
            // Act
            var actual = SmartScheduleUtil.getTotalDuration(timeSlot, serviceDuration, bufferTime);
            // Assert
            assertThat(actual).isEqualTo(60);
        }

        @Test
        void addDoubleBufferWhenBothSidesHaveNonBlockAppts() {
            // Arrange: before/after 均有预约且非 block，buffer>0 => +2*buffer
            var timeSlot = TimeSlot.builder()
                    .beforeApptId(1)
                    .beforeApptIsBlock(false)
                    .afterApptId(2)
                    .afterApptIsBlock(false)
                    .build();
            var serviceDuration = 30;
            var bufferTime = 10;
            var expected = 50; // 30 + 10*2
            // Act
            var actual = SmartScheduleUtil.getTotalDuration(timeSlot, serviceDuration, bufferTime);
            // Assert
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void addSingleBufferWhenOnlyOneSideHasNonBlockAppt() {
            // Arrange: 仅一侧有非 block 预约，buffer>0 => +buffer
            var timeSlot = TimeSlot.builder()
                    .beforeApptId(1)
                    .beforeApptIsBlock(false)
                    .afterApptId(0)
                    .afterApptIsBlock(false)
                    .build();
            var serviceDuration = 40;
            var bufferTime = 15;
            var expected = 55; // 40 + 15
            // Act
            var actual = SmartScheduleUtil.getTotalDuration(timeSlot, serviceDuration, bufferTime);
            // Assert
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void addSingleBufferWhenOneSideIsBlock() {
            // Arrange: before 为 block、after 为非 block，buffer>0 => +buffer
            var timeSlot = TimeSlot.builder()
                    .beforeApptId(3)
                    .beforeApptIsBlock(true)
                    .afterApptId(4)
                    .afterApptIsBlock(false)
                    .build();
            var serviceDuration = 25;
            var bufferTime = 5;
            var expected = 30; // 25 + 5
            // Act
            var actual = SmartScheduleUtil.getTotalDuration(timeSlot, serviceDuration, bufferTime);
            // Assert
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void negativeBufferTimeReducesButNotBelowZero() {
            // Arrange: buffer<0 时取 max(service+buffer, 0)
            var timeSlot = TimeSlot.builder().build();
            var serviceDuration = 30;
            var bufferTime = -5;
            var expected = 25;
            // Act
            var actual = SmartScheduleUtil.getTotalDuration(timeSlot, serviceDuration, bufferTime);
            // Assert
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void negativeBufferTimeClampedToZero() {
            // Arrange: service+buffer < 0 时返回 0
            var timeSlot = TimeSlot.builder().build();
            var serviceDuration = 30;
            var bufferTime = -40;
            var expected = 0;
            // Act
            var actual = SmartScheduleUtil.getTotalDuration(timeSlot, serviceDuration, bufferTime);
            // Assert
            assertThat(actual).isEqualTo(expected);
        }
    }

    @Test
    void staffPerDayWorkingTimeIntersection() {
        Map<String, Map<Integer, List<TimeRangeDto>>> curWorkingTime = Map.of(
                "1",
                        Map.of(
                                1, List.of(new TimeRangeDto(2, 4), new TimeRangeDto(6, 10), new TimeRangeDto(12, 15)),
                                3, List.of(new TimeRangeDto(2, 4), new TimeRangeDto(6, 10), new TimeRangeDto(12, 15)),
                                7, List.of(new TimeRangeDto(2, 4))),
                "2", Map.of(1, List.of(new TimeRangeDto(2, 4), new TimeRangeDto(6, 10), new TimeRangeDto(12, 15))),
                "3", Map.of(1, List.of(new TimeRangeDto(2, 4), new TimeRangeDto(6, 10), new TimeRangeDto(12, 15))));
        Map<String, Map<Integer, List<TimeRangeDto>>> workingTimeToIntersect = Map.of(
                "1",
                        Map.of(
                                1, List.of(new TimeRangeDto(3, 7), new TimeRangeDto(8, 11)),
                                5, List.of(new TimeRangeDto(3, 7), new TimeRangeDto(8, 11)),
                                7, List.of(new TimeRangeDto(2, 4))),
                "3", Map.of(1, List.of(new TimeRangeDto(20, 22))),
                "5", Map.of(1, List.of(new TimeRangeDto(2, 4), new TimeRangeDto(6, 10), new TimeRangeDto(12, 15))));
        Map<String, Map<Integer, List<TimeRangeDto>>> result =
                SmartScheduleUtil.staffPerDayWorkingTimeIntersection(curWorkingTime, workingTimeToIntersect);
        assertEquals(1, result.size());
        assertEquals(2, result.get("1").size());
        assertEquals(3, result.get("1").get(1).size());
        assertEquals(1, result.get("1").get(7).size());
    }

    @Test
    void timeRangeListIntersection() {
        List<TimeRangeDto> result = SmartScheduleUtil.timeRangeListIntersection(
                List.of(new TimeRangeDto(2, 4), new TimeRangeDto(6, 10), new TimeRangeDto(12, 15)),
                List.of(new TimeRangeDto(3, 7), new TimeRangeDto(8, 11)));
        assertEquals(3, result.size());
        assertEquals(3, result.get(0).getStartTime());
        assertEquals(4, result.get(0).getEndTime());
        assertEquals(6, result.get(1).getStartTime());
        assertEquals(7, result.get(1).getEndTime());
        assertEquals(8, result.get(2).getStartTime());
        assertEquals(10, result.get(2).getEndTime());
        result = SmartScheduleUtil.timeRangeListIntersection(
                List.of(new TimeRangeDto(2, 4), new TimeRangeDto(6, 10), new TimeRangeDto(12, 15)), null);
        assertEquals(0, result.size());
        result = SmartScheduleUtil.timeRangeListIntersection(
                null, List.of(new TimeRangeDto(3, 7), new TimeRangeDto(8, 11)));
        assertEquals(0, result.size());
    }

    @Test
    void timeRangeIntersect() {
        List<TimeRangeDto> result =
                SmartScheduleUtil.timeRangeIntersect(List.of(new TimeRangeDto(2, 4)), new TimeRangeDto(5, 10));
        assertEquals(0, result.size());
        result = SmartScheduleUtil.timeRangeIntersect(List.of(new TimeRangeDto(2, 5)), new TimeRangeDto(5, 10));
        assertEquals(0, result.size());
        result = SmartScheduleUtil.timeRangeIntersect(List.of(new TimeRangeDto(2, 6)), new TimeRangeDto(5, 10));
        assertEquals(1, result.size());
        assertEquals(5, result.get(0).getStartTime());
        assertEquals(6, result.get(0).getEndTime());
        result = SmartScheduleUtil.timeRangeIntersect(List.of(new TimeRangeDto(2, 10)), new TimeRangeDto(5, 10));
        assertEquals(1, result.size());
        assertEquals(5, result.get(0).getStartTime());
        assertEquals(10, result.get(0).getEndTime());
        result = SmartScheduleUtil.timeRangeIntersect(List.of(new TimeRangeDto(2, 15)), new TimeRangeDto(5, 10));
        assertEquals(1, result.size());
        assertEquals(5, result.get(0).getStartTime());
        assertEquals(10, result.get(0).getEndTime());
        result = SmartScheduleUtil.timeRangeIntersect(List.of(new TimeRangeDto(5, 15)), new TimeRangeDto(5, 10));
        assertEquals(1, result.size());
        assertEquals(5, result.get(0).getStartTime());
        assertEquals(10, result.get(0).getEndTime());
        result = SmartScheduleUtil.timeRangeIntersect(List.of(new TimeRangeDto(7, 15)), new TimeRangeDto(5, 10));
        assertEquals(1, result.size());
        assertEquals(7, result.get(0).getStartTime());
        assertEquals(10, result.get(0).getEndTime());
        result = SmartScheduleUtil.timeRangeIntersect(List.of(new TimeRangeDto(10, 15)), new TimeRangeDto(5, 10));
        assertEquals(0, result.size());
        result = SmartScheduleUtil.timeRangeIntersect(List.of(new TimeRangeDto(12, 15)), new TimeRangeDto(5, 10));
        assertEquals(0, result.size());
        result = SmartScheduleUtil.timeRangeIntersect(
                List.of(
                        new TimeRangeDto(2, 4),
                        new TimeRangeDto(2, 6),
                        new TimeRangeDto(2, 15),
                        new TimeRangeDto(7, 15),
                        new TimeRangeDto(12, 15)),
                new TimeRangeDto(5, 10));
        assertEquals(3, result.size());
        assertEquals(5, result.get(0).getStartTime());
        assertEquals(6, result.get(0).getEndTime());
        assertEquals(5, result.get(1).getStartTime());
        assertEquals(10, result.get(1).getEndTime());
        assertEquals(7, result.get(2).getStartTime());
        assertEquals(10, result.get(2).getEndTime());
        result = SmartScheduleUtil.timeRangeIntersect(null, new TimeRangeDto(5, 10));
        assertEquals(0, result.size());
    }

    @Test
    void timeSlotListIntersection() {
        List<TimeSlot> timeSlotList = List.of(
                new TimeSlot() {
                    {
                        setStart(2);
                        setEnd(4);
                    }
                },
                new TimeSlot() {
                    {
                        setStart(6);
                        setEnd(10);
                    }
                },
                new TimeSlot() {
                    {
                        setStart(12);
                        setEnd(15);
                    }
                });
        List<TimeSlot> result = SmartScheduleUtil.timeSlotListIntersection(
                timeSlotList, List.of(new TimeRangeDto(3, 7), new TimeRangeDto(8, 11)));
        assertEquals(3, result.size());
        assertEquals(3, result.get(0).getStart());
        assertEquals(4, result.get(0).getEnd());
        assertEquals(6, result.get(1).getStart());
        assertEquals(7, result.get(1).getEnd());
        assertEquals(8, result.get(2).getStart());
        assertEquals(10, result.get(2).getEnd());
        result = SmartScheduleUtil.timeSlotListIntersection(timeSlotList, null);
        assertEquals(3, result.size());
        result = SmartScheduleUtil.timeSlotListIntersection(
                null, List.of(new TimeRangeDto(3, 7), new TimeRangeDto(8, 11)));
        assertEquals(0, result.size());
    }

    @Test
    void timeSlotIntersect() {
        List<TimeSlot> timeSlotList = List.of(new TimeSlot() {
            {
                setStart(2);
                setEnd(4);
            }
        });
        List<TimeSlot> result = SmartScheduleUtil.timeSlotIntersect(timeSlotList, new TimeRangeDto(5, 10));
        assertEquals(0, result.size());
        timeSlotList = List.of(new TimeSlot() {
            {
                setStart(2);
                setEnd(5);
            }
        });
        result = SmartScheduleUtil.timeSlotIntersect(timeSlotList, new TimeRangeDto(5, 10));
        assertEquals(0, result.size());
        timeSlotList = List.of(new TimeSlot() {
            {
                setStart(2);
                setEnd(6);
            }
        });
        result = SmartScheduleUtil.timeSlotIntersect(timeSlotList, new TimeRangeDto(5, 10));
        assertEquals(1, result.size());
        assertEquals(5, result.get(0).getStart());
        assertEquals(6, result.get(0).getEnd());
        timeSlotList = List.of(new TimeSlot() {
            {
                setStart(2);
                setEnd(10);
            }
        });
        result = SmartScheduleUtil.timeSlotIntersect(timeSlotList, new TimeRangeDto(5, 10));
        assertEquals(1, result.size());
        assertEquals(5, result.get(0).getStart());
        assertEquals(10, result.get(0).getEnd());
        timeSlotList = List.of(new TimeSlot() {
            {
                setStart(2);
                setEnd(15);
            }
        });
        result = SmartScheduleUtil.timeSlotIntersect(timeSlotList, new TimeRangeDto(5, 10));
        assertEquals(1, result.size());
        assertEquals(5, result.get(0).getStart());
        assertEquals(10, result.get(0).getEnd());
        timeSlotList = List.of(new TimeSlot() {
            {
                setStart(5);
                setEnd(15);
            }
        });
        result = SmartScheduleUtil.timeSlotIntersect(timeSlotList, new TimeRangeDto(5, 10));
        assertEquals(1, result.size());
        assertEquals(5, result.get(0).getStart());
        assertEquals(10, result.get(0).getEnd());
        timeSlotList = List.of(new TimeSlot() {
            {
                setStart(7);
                setEnd(15);
            }
        });
        result = SmartScheduleUtil.timeSlotIntersect(timeSlotList, new TimeRangeDto(5, 10));
        assertEquals(1, result.size());
        assertEquals(7, result.get(0).getStart());
        assertEquals(10, result.get(0).getEnd());
        timeSlotList = List.of(new TimeSlot() {
            {
                setStart(10);
                setEnd(15);
            }
        });
        result = SmartScheduleUtil.timeSlotIntersect(timeSlotList, new TimeRangeDto(5, 10));
        assertEquals(0, result.size());
        timeSlotList = List.of(new TimeSlot() {
            {
                setStart(12);
                setEnd(15);
            }
        });
        result = SmartScheduleUtil.timeSlotIntersect(timeSlotList, new TimeRangeDto(5, 10));
        assertEquals(0, result.size());
        timeSlotList = List.of(new TimeSlot() {
            {
                setStart(2);
                setEnd(15);
            }
        });
        result = SmartScheduleUtil.timeSlotIntersect(timeSlotList, new TimeRangeDto(5, 10));
        assertEquals(1, result.size());
        assertEquals(5, result.get(0).getStart());
        assertEquals(10, result.get(0).getEnd());
        result = SmartScheduleUtil.timeSlotIntersect(null, new TimeRangeDto(5, 10));
        assertEquals(0, result.size());
    }
}
