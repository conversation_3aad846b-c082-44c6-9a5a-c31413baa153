// @since 2024-08-21 00:05:46
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/offering/v1/service_staff_api.proto

package offeringapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list staff for service
type ListServiceStaffsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// business id, if empty, will use the company setting
	// 这里需要考虑 staff working location，需要传 business_id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pet id, if not empty, will use the customized price by pet (if exist customized price)
	PetId *int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// customer id
	CustomerId *int64 `protobuf:"varint,6,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
}

func (x *ListServiceStaffsParams) Reset() {
	*x = ListServiceStaffsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_staff_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceStaffsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceStaffsParams) ProtoMessage() {}

func (x *ListServiceStaffsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_staff_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceStaffsParams.ProtoReflect.Descriptor instead.
func (*ListServiceStaffsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_staff_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListServiceStaffsParams) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ListServiceStaffsParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListServiceStaffsParams) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

func (x *ListServiceStaffsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServiceStaffsParams) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

// list staff for service result
type ListServiceStaffsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	Staffs []*ListServiceStaffsResult_StaffWithServicePriceAndDurationView `protobuf:"bytes,1,rep,name=staffs,proto3" json:"staffs,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListServiceStaffsResult) Reset() {
	*x = ListServiceStaffsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_staff_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceStaffsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceStaffsResult) ProtoMessage() {}

func (x *ListServiceStaffsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_staff_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceStaffsResult.ProtoReflect.Descriptor instead.
func (*ListServiceStaffsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_staff_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListServiceStaffsResult) GetStaffs() []*ListServiceStaffsResult_StaffWithServicePriceAndDurationView {
	if x != nil {
		return x.Staffs
	}
	return nil
}

func (x *ListServiceStaffsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list staff for evaluation service
type ListEvaluationStaffsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation id
	EvaluationId int64 `protobuf:"varint,1,opt,name=evaluation_id,json=evaluationId,proto3" json:"evaluation_id,omitempty"`
	// 这里需要考虑 staff working location，需要传 business_id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListEvaluationStaffsParams) Reset() {
	*x = ListEvaluationStaffsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_staff_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationStaffsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationStaffsParams) ProtoMessage() {}

func (x *ListEvaluationStaffsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_staff_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationStaffsParams.ProtoReflect.Descriptor instead.
func (*ListEvaluationStaffsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_staff_api_proto_rawDescGZIP(), []int{2}
}

func (x *ListEvaluationStaffsParams) GetEvaluationId() int64 {
	if x != nil {
		return x.EvaluationId
	}
	return 0
}

func (x *ListEvaluationStaffsParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListEvaluationStaffsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list staff for evaluation service result
type ListEvaluationStaffsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff list
	Staffs []*ListEvaluationStaffsResult_StaffView `protobuf:"bytes,1,rep,name=staffs,proto3" json:"staffs,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListEvaluationStaffsResult) Reset() {
	*x = ListEvaluationStaffsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_staff_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationStaffsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationStaffsResult) ProtoMessage() {}

func (x *ListEvaluationStaffsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_staff_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationStaffsResult.ProtoReflect.Descriptor instead.
func (*ListEvaluationStaffsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_staff_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListEvaluationStaffsResult) GetStaffs() []*ListEvaluationStaffsResult_StaffView {
	if x != nil {
		return x.Staffs
	}
	return nil
}

func (x *ListEvaluationStaffsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// staff with service price and duration
type ListServiceStaffsResult_StaffWithServicePriceAndDurationView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// staff avatar
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// service price
	ServicePrice float64 `protobuf:"fixed64,5,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// price override type, if not set, means no override
	PriceOverrideType *v1.ServiceOverrideType `protobuf:"varint,6,opt,name=price_override_type,json=priceOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType,oneof" json:"price_override_type,omitempty"`
	// service duration
	ServiceDuration int32 `protobuf:"varint,7,opt,name=service_duration,json=serviceDuration,proto3" json:"service_duration,omitempty"`
	// duration override type, if not set, means no override
	DurationOverrideType *v1.ServiceOverrideType `protobuf:"varint,8,opt,name=duration_override_type,json=durationOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType,oneof" json:"duration_override_type,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,9,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
}

func (x *ListServiceStaffsResult_StaffWithServicePriceAndDurationView) Reset() {
	*x = ListServiceStaffsResult_StaffWithServicePriceAndDurationView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_staff_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceStaffsResult_StaffWithServicePriceAndDurationView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceStaffsResult_StaffWithServicePriceAndDurationView) ProtoMessage() {}

func (x *ListServiceStaffsResult_StaffWithServicePriceAndDurationView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_staff_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceStaffsResult_StaffWithServicePriceAndDurationView.ProtoReflect.Descriptor instead.
func (*ListServiceStaffsResult_StaffWithServicePriceAndDurationView) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_staff_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ListServiceStaffsResult_StaffWithServicePriceAndDurationView) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *ListServiceStaffsResult_StaffWithServicePriceAndDurationView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *ListServiceStaffsResult_StaffWithServicePriceAndDurationView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *ListServiceStaffsResult_StaffWithServicePriceAndDurationView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *ListServiceStaffsResult_StaffWithServicePriceAndDurationView) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *ListServiceStaffsResult_StaffWithServicePriceAndDurationView) GetPriceOverrideType() v1.ServiceOverrideType {
	if x != nil && x.PriceOverrideType != nil {
		return *x.PriceOverrideType
	}
	return v1.ServiceOverrideType(0)
}

func (x *ListServiceStaffsResult_StaffWithServicePriceAndDurationView) GetServiceDuration() int32 {
	if x != nil {
		return x.ServiceDuration
	}
	return 0
}

func (x *ListServiceStaffsResult_StaffWithServicePriceAndDurationView) GetDurationOverrideType() v1.ServiceOverrideType {
	if x != nil && x.DurationOverrideType != nil {
		return *x.DurationOverrideType
	}
	return v1.ServiceOverrideType(0)
}

func (x *ListServiceStaffsResult_StaffWithServicePriceAndDurationView) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

// staff view
type ListEvaluationStaffsResult_StaffView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// staff avatar
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,5,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
}

func (x *ListEvaluationStaffsResult_StaffView) Reset() {
	*x = ListEvaluationStaffsResult_StaffView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_staff_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationStaffsResult_StaffView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationStaffsResult_StaffView) ProtoMessage() {}

func (x *ListEvaluationStaffsResult_StaffView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_staff_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationStaffsResult_StaffView.ProtoReflect.Descriptor instead.
func (*ListEvaluationStaffsResult_StaffView) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_staff_api_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ListEvaluationStaffsResult_StaffView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListEvaluationStaffsResult_StaffView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *ListEvaluationStaffsResult_StaffView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *ListEvaluationStaffsResult_StaffView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *ListEvaluationStaffsResult_StaffView) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

var File_moego_api_offering_v1_service_staff_api_proto protoreflect.FileDescriptor

var file_moego_api_offering_v1_service_staff_api_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa7, 0x02, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x22, 0xdf, 0x05, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6b, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x57, 0x69, 0x74, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x41, 0x6e, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x92, 0x04,
	0x0a, 0x24, 0x53, 0x74, 0x61, 0x66, 0x66, 0x57, 0x69, 0x74, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x41, 0x6e, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x23,
	0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x62, 0x0a, 0x13, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48,
	0x00, 0x52, 0x11, 0x70, 0x72, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x68, 0x0a, 0x16, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x48, 0x01, 0x52, 0x14, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x0c,
	0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42,
	0x16, 0x0a, 0x14, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x22, 0xc1, 0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0c, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xd3, 0x02, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x53, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x9b,
	0x01, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x66, 0x66, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x32, 0x88, 0x02, 0x0a,
	0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x73, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x14, 0x4c, 0x69, 0x73,
	0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x73, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x7b, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x61,
	0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_offering_v1_service_staff_api_proto_rawDescOnce sync.Once
	file_moego_api_offering_v1_service_staff_api_proto_rawDescData = file_moego_api_offering_v1_service_staff_api_proto_rawDesc
)

func file_moego_api_offering_v1_service_staff_api_proto_rawDescGZIP() []byte {
	file_moego_api_offering_v1_service_staff_api_proto_rawDescOnce.Do(func() {
		file_moego_api_offering_v1_service_staff_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_offering_v1_service_staff_api_proto_rawDescData)
	})
	return file_moego_api_offering_v1_service_staff_api_proto_rawDescData
}

var file_moego_api_offering_v1_service_staff_api_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_api_offering_v1_service_staff_api_proto_goTypes = []interface{}{
	(*ListServiceStaffsParams)(nil),                                      // 0: moego.api.offering.v1.ListServiceStaffsParams
	(*ListServiceStaffsResult)(nil),                                      // 1: moego.api.offering.v1.ListServiceStaffsResult
	(*ListEvaluationStaffsParams)(nil),                                   // 2: moego.api.offering.v1.ListEvaluationStaffsParams
	(*ListEvaluationStaffsResult)(nil),                                   // 3: moego.api.offering.v1.ListEvaluationStaffsResult
	(*ListServiceStaffsResult_StaffWithServicePriceAndDurationView)(nil), // 4: moego.api.offering.v1.ListServiceStaffsResult.StaffWithServicePriceAndDurationView
	(*ListEvaluationStaffsResult_StaffView)(nil),                         // 5: moego.api.offering.v1.ListEvaluationStaffsResult.StaffView
	(*v2.PaginationRequest)(nil),                                         // 6: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                                        // 7: moego.utils.v2.PaginationResponse
	(v1.ServiceOverrideType)(0),                                          // 8: moego.models.offering.v1.ServiceOverrideType
}
var file_moego_api_offering_v1_service_staff_api_proto_depIdxs = []int32{
	6,  // 0: moego.api.offering.v1.ListServiceStaffsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	4,  // 1: moego.api.offering.v1.ListServiceStaffsResult.staffs:type_name -> moego.api.offering.v1.ListServiceStaffsResult.StaffWithServicePriceAndDurationView
	7,  // 2: moego.api.offering.v1.ListServiceStaffsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	6,  // 3: moego.api.offering.v1.ListEvaluationStaffsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	5,  // 4: moego.api.offering.v1.ListEvaluationStaffsResult.staffs:type_name -> moego.api.offering.v1.ListEvaluationStaffsResult.StaffView
	7,  // 5: moego.api.offering.v1.ListEvaluationStaffsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	8,  // 6: moego.api.offering.v1.ListServiceStaffsResult.StaffWithServicePriceAndDurationView.price_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	8,  // 7: moego.api.offering.v1.ListServiceStaffsResult.StaffWithServicePriceAndDurationView.duration_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	0,  // 8: moego.api.offering.v1.ServiceStaffService.ListServiceStaffs:input_type -> moego.api.offering.v1.ListServiceStaffsParams
	2,  // 9: moego.api.offering.v1.ServiceStaffService.ListEvaluationStaffs:input_type -> moego.api.offering.v1.ListEvaluationStaffsParams
	1,  // 10: moego.api.offering.v1.ServiceStaffService.ListServiceStaffs:output_type -> moego.api.offering.v1.ListServiceStaffsResult
	3,  // 11: moego.api.offering.v1.ServiceStaffService.ListEvaluationStaffs:output_type -> moego.api.offering.v1.ListEvaluationStaffsResult
	10, // [10:12] is the sub-list for method output_type
	8,  // [8:10] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_api_offering_v1_service_staff_api_proto_init() }
func file_moego_api_offering_v1_service_staff_api_proto_init() {
	if File_moego_api_offering_v1_service_staff_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_offering_v1_service_staff_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceStaffsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_staff_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceStaffsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_staff_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationStaffsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_staff_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationStaffsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_staff_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceStaffsResult_StaffWithServicePriceAndDurationView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_staff_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationStaffsResult_StaffView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_offering_v1_service_staff_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_service_staff_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_offering_v1_service_staff_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_offering_v1_service_staff_api_proto_goTypes,
		DependencyIndexes: file_moego_api_offering_v1_service_staff_api_proto_depIdxs,
		MessageInfos:      file_moego_api_offering_v1_service_staff_api_proto_msgTypes,
	}.Build()
	File_moego_api_offering_v1_service_staff_api_proto = out.File
	file_moego_api_offering_v1_service_staff_api_proto_rawDesc = nil
	file_moego_api_offering_v1_service_staff_api_proto_goTypes = nil
	file_moego_api_offering_v1_service_staff_api_proto_depIdxs = nil
}
