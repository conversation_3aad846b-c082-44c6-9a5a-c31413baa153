// @since 2025-02-26 16:46:24
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/offering/v2/pricing_rule_api.proto

package offeringapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// upsert pricing rule params
type UpsertPricingRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing_rule def
	PricingRuleDef *v2.PricingRuleUpsertDef `protobuf:"bytes,1,opt,name=pricing_rule_def,json=pricingRuleDef,proto3" json:"pricing_rule_def,omitempty"`
	// apply to upcoming appointments
	ApplyToUpcomingAppointments bool `protobuf:"varint,2,opt,name=apply_to_upcoming_appointments,json=applyToUpcomingAppointments,proto3" json:"apply_to_upcoming_appointments,omitempty"`
}

func (x *UpsertPricingRuleParams) Reset() {
	*x = UpsertPricingRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertPricingRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertPricingRuleParams) ProtoMessage() {}

func (x *UpsertPricingRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertPricingRuleParams.ProtoReflect.Descriptor instead.
func (*UpsertPricingRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{0}
}

func (x *UpsertPricingRuleParams) GetPricingRuleDef() *v2.PricingRuleUpsertDef {
	if x != nil {
		return x.PricingRuleDef
	}
	return nil
}

func (x *UpsertPricingRuleParams) GetApplyToUpcomingAppointments() bool {
	if x != nil {
		return x.ApplyToUpcomingAppointments
	}
	return false
}

// upsert pricing rule result
type UpsertPricingRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the created pricing_rule
	PricingRule *v2.PricingRule `protobuf:"bytes,1,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *UpsertPricingRuleResult) Reset() {
	*x = UpsertPricingRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertPricingRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertPricingRuleResult) ProtoMessage() {}

func (x *UpsertPricingRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertPricingRuleResult.ProtoReflect.Descriptor instead.
func (*UpsertPricingRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{1}
}

func (x *UpsertPricingRuleResult) GetPricingRule() *v2.PricingRule {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// get pricing rule params
type GetPricingRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetPricingRuleParams) Reset() {
	*x = GetPricingRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPricingRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPricingRuleParams) ProtoMessage() {}

func (x *GetPricingRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPricingRuleParams.ProtoReflect.Descriptor instead.
func (*GetPricingRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetPricingRuleParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get pricing rule result
type GetPricingRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pricing rule
	PricingRule *v2.PricingRule `protobuf:"bytes,1,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *GetPricingRuleResult) Reset() {
	*x = GetPricingRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPricingRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPricingRuleResult) ProtoMessage() {}

func (x *GetPricingRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPricingRuleResult.ProtoReflect.Descriptor instead.
func (*GetPricingRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetPricingRuleResult) GetPricingRule() *v2.PricingRule {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// list pricing rule params
type ListPricingRulesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v2.ListPricingRuleFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// pagination
	Pagination *v21.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPricingRulesParams) Reset() {
	*x = ListPricingRulesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRulesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRulesParams) ProtoMessage() {}

func (x *ListPricingRulesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRulesParams.ProtoReflect.Descriptor instead.
func (*ListPricingRulesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{4}
}

func (x *ListPricingRulesParams) GetFilter() *v2.ListPricingRuleFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListPricingRulesParams) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list pricing rule result
type ListPricingRulesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pricing rule list
	PricingRules []*v2.PricingRule `protobuf:"bytes,1,rep,name=pricing_rules,json=pricingRules,proto3" json:"pricing_rules,omitempty"`
	// pagination
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPricingRulesResult) Reset() {
	*x = ListPricingRulesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRulesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRulesResult) ProtoMessage() {}

func (x *ListPricingRulesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRulesResult.ProtoReflect.Descriptor instead.
func (*ListPricingRulesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{5}
}

func (x *ListPricingRulesResult) GetPricingRules() []*v2.PricingRule {
	if x != nil {
		return x.PricingRules
	}
	return nil
}

func (x *ListPricingRulesResult) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// calculate pricing rule params
type CalculatePricingRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail list
	PetDetails []*v2.PetDetailCalculateDef `protobuf:"bytes,1,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
}

func (x *CalculatePricingRuleParams) Reset() {
	*x = CalculatePricingRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculatePricingRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePricingRuleParams) ProtoMessage() {}

func (x *CalculatePricingRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePricingRuleParams.ProtoReflect.Descriptor instead.
func (*CalculatePricingRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{6}
}

func (x *CalculatePricingRuleParams) GetPetDetails() []*v2.PetDetailCalculateDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

// calculate pricing rule result
type CalculatePricingRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail list
	PetDetails []*v2.PetDetailCalculateResultDef `protobuf:"bytes,1,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// used pricing rule list
	PricingRules []*v2.PricingRule `protobuf:"bytes,2,rep,name=pricing_rules,json=pricingRules,proto3" json:"pricing_rules,omitempty"`
}

func (x *CalculatePricingRuleResult) Reset() {
	*x = CalculatePricingRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculatePricingRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePricingRuleResult) ProtoMessage() {}

func (x *CalculatePricingRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePricingRuleResult.ProtoReflect.Descriptor instead.
func (*CalculatePricingRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{7}
}

func (x *CalculatePricingRuleResult) GetPetDetails() []*v2.PetDetailCalculateResultDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *CalculatePricingRuleResult) GetPricingRules() []*v2.PricingRule {
	if x != nil {
		return x.PricingRules
	}
	return nil
}

// preview pricing rule params
type PreviewPricingRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail list
	PetDetails []*v2.PreviewPetDetailCalculateDef `protobuf:"bytes,1,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// discount setting
	Setting *v2.DiscountSettingDef `protobuf:"bytes,2,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *PreviewPricingRuleParams) Reset() {
	*x = PreviewPricingRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewPricingRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewPricingRuleParams) ProtoMessage() {}

func (x *PreviewPricingRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewPricingRuleParams.ProtoReflect.Descriptor instead.
func (*PreviewPricingRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{8}
}

func (x *PreviewPricingRuleParams) GetPetDetails() []*v2.PreviewPetDetailCalculateDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *PreviewPricingRuleParams) GetSetting() *v2.DiscountSettingDef {
	if x != nil {
		return x.Setting
	}
	return nil
}

// preview pricing rule result
type PreviewPricingRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail list
	PetDetails []*v2.PreviewPetDetailCalculateResultDef `protobuf:"bytes,1,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// formula for preview calculation
	Formula string `protobuf:"bytes,2,opt,name=formula,proto3" json:"formula,omitempty"`
	// no result
	NoResult bool `protobuf:"varint,3,opt,name=no_result,json=noResult,proto3" json:"no_result,omitempty"`
}

func (x *PreviewPricingRuleResult) Reset() {
	*x = PreviewPricingRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewPricingRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewPricingRuleResult) ProtoMessage() {}

func (x *PreviewPricingRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewPricingRuleResult.ProtoReflect.Descriptor instead.
func (*PreviewPricingRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{9}
}

func (x *PreviewPricingRuleResult) GetPetDetails() []*v2.PreviewPetDetailCalculateResultDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *PreviewPricingRuleResult) GetFormula() string {
	if x != nil {
		return x.Formula
	}
	return ""
}

func (x *PreviewPricingRuleResult) GetNoResult() bool {
	if x != nil {
		return x.NoResult
	}
	return false
}

// delete pricing rule params
type DeletePricingRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// apply to upcoming appointments
	ApplyToUpcomingAppointments bool `protobuf:"varint,2,opt,name=apply_to_upcoming_appointments,json=applyToUpcomingAppointments,proto3" json:"apply_to_upcoming_appointments,omitempty"`
}

func (x *DeletePricingRuleParams) Reset() {
	*x = DeletePricingRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePricingRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePricingRuleParams) ProtoMessage() {}

func (x *DeletePricingRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePricingRuleParams.ProtoReflect.Descriptor instead.
func (*DeletePricingRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{10}
}

func (x *DeletePricingRuleParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeletePricingRuleParams) GetApplyToUpcomingAppointments() bool {
	if x != nil {
		return x.ApplyToUpcomingAppointments
	}
	return false
}

// delete pricing rule result
type DeletePricingRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePricingRuleResult) Reset() {
	*x = DeletePricingRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePricingRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePricingRuleResult) ProtoMessage() {}

func (x *DeletePricingRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePricingRuleResult.ProtoReflect.Descriptor instead.
func (*DeletePricingRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{11}
}

// get associated services params
type ListAssociatedServicesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rule type
	Type v2.RuleType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.offering.v2.RuleType" json:"type,omitempty"`
	// exclude pricing rule id
	ExcludePricingRuleId *int64 `protobuf:"varint,2,opt,name=exclude_pricing_rule_id,json=excludePricingRuleId,proto3,oneof" json:"exclude_pricing_rule_id,omitempty"`
}

func (x *ListAssociatedServicesParams) Reset() {
	*x = ListAssociatedServicesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAssociatedServicesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssociatedServicesParams) ProtoMessage() {}

func (x *ListAssociatedServicesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssociatedServicesParams.ProtoReflect.Descriptor instead.
func (*ListAssociatedServicesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{12}
}

func (x *ListAssociatedServicesParams) GetType() v2.RuleType {
	if x != nil {
		return x.Type
	}
	return v2.RuleType(0)
}

func (x *ListAssociatedServicesParams) GetExcludePricingRuleId() int64 {
	if x != nil && x.ExcludePricingRuleId != nil {
		return *x.ExcludePricingRuleId
	}
	return 0
}

// get associated services result
type ListAssociatedServicesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// all boarding associated
	AllBoardingAssociated bool `protobuf:"varint,1,opt,name=all_boarding_associated,json=allBoardingAssociated,proto3" json:"all_boarding_associated,omitempty"`
	// associated boarding service ids
	AssociatedBoardingServiceIds []int64 `protobuf:"varint,2,rep,packed,name=associated_boarding_service_ids,json=associatedBoardingServiceIds,proto3" json:"associated_boarding_service_ids,omitempty"`
	// all daycare associated
	AllDaycareAssociated bool `protobuf:"varint,3,opt,name=all_daycare_associated,json=allDaycareAssociated,proto3" json:"all_daycare_associated,omitempty"`
	// associated daycare service ids
	AssociatedDaycareServiceIds []int64 `protobuf:"varint,4,rep,packed,name=associated_daycare_service_ids,json=associatedDaycareServiceIds,proto3" json:"associated_daycare_service_ids,omitempty"`
	// all grooming associated
	AllGroomingAssociated bool `protobuf:"varint,5,opt,name=all_grooming_associated,json=allGroomingAssociated,proto3" json:"all_grooming_associated,omitempty"`
	// associated grooming service ids
	AssociatedGroomingServiceIds []int64 `protobuf:"varint,6,rep,packed,name=associated_grooming_service_ids,json=associatedGroomingServiceIds,proto3" json:"associated_grooming_service_ids,omitempty"`
	// all addon associated
	AllAddonAssociated bool `protobuf:"varint,7,opt,name=all_addon_associated,json=allAddonAssociated,proto3" json:"all_addon_associated,omitempty"`
	// associated addon service ids
	AssociatedAddonServiceIds []int64 `protobuf:"varint,8,rep,packed,name=associated_addon_service_ids,json=associatedAddonServiceIds,proto3" json:"associated_addon_service_ids,omitempty"`
}

func (x *ListAssociatedServicesResult) Reset() {
	*x = ListAssociatedServicesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAssociatedServicesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssociatedServicesResult) ProtoMessage() {}

func (x *ListAssociatedServicesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssociatedServicesResult.ProtoReflect.Descriptor instead.
func (*ListAssociatedServicesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{13}
}

func (x *ListAssociatedServicesResult) GetAllBoardingAssociated() bool {
	if x != nil {
		return x.AllBoardingAssociated
	}
	return false
}

func (x *ListAssociatedServicesResult) GetAssociatedBoardingServiceIds() []int64 {
	if x != nil {
		return x.AssociatedBoardingServiceIds
	}
	return nil
}

func (x *ListAssociatedServicesResult) GetAllDaycareAssociated() bool {
	if x != nil {
		return x.AllDaycareAssociated
	}
	return false
}

func (x *ListAssociatedServicesResult) GetAssociatedDaycareServiceIds() []int64 {
	if x != nil {
		return x.AssociatedDaycareServiceIds
	}
	return nil
}

func (x *ListAssociatedServicesResult) GetAllGroomingAssociated() bool {
	if x != nil {
		return x.AllGroomingAssociated
	}
	return false
}

func (x *ListAssociatedServicesResult) GetAssociatedGroomingServiceIds() []int64 {
	if x != nil {
		return x.AssociatedGroomingServiceIds
	}
	return nil
}

func (x *ListAssociatedServicesResult) GetAllAddonAssociated() bool {
	if x != nil {
		return x.AllAddonAssociated
	}
	return false
}

func (x *ListAssociatedServicesResult) GetAssociatedAddonServiceIds() []int64 {
	if x != nil {
		return x.AssociatedAddonServiceIds
	}
	return nil
}

// check configuration params
type CheckConfigurationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing_rule def
	PricingRuleDef *v2.PricingRuleUpsertDef `protobuf:"bytes,1,opt,name=pricing_rule_def,json=pricingRuleDef,proto3" json:"pricing_rule_def,omitempty"`
}

func (x *CheckConfigurationParams) Reset() {
	*x = CheckConfigurationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckConfigurationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckConfigurationParams) ProtoMessage() {}

func (x *CheckConfigurationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckConfigurationParams.ProtoReflect.Descriptor instead.
func (*CheckConfigurationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{14}
}

func (x *CheckConfigurationParams) GetPricingRuleDef() *v2.PricingRuleUpsertDef {
	if x != nil {
		return x.PricingRuleDef
	}
	return nil
}

// check configuration result
type CheckConfigurationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is valid
	IsValid bool `protobuf:"varint,1,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	// error message
	ErrorMessage *string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3,oneof" json:"error_message,omitempty"`
}

func (x *CheckConfigurationResult) Reset() {
	*x = CheckConfigurationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckConfigurationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckConfigurationResult) ProtoMessage() {}

func (x *CheckConfigurationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckConfigurationResult.ProtoReflect.Descriptor instead.
func (*CheckConfigurationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{15}
}

func (x *CheckConfigurationResult) GetIsValid() bool {
	if x != nil {
		return x.IsValid
	}
	return false
}

func (x *CheckConfigurationResult) GetErrorMessage() string {
	if x != nil && x.ErrorMessage != nil {
		return *x.ErrorMessage
	}
	return ""
}

// get discount setting params
type GetDiscountSettingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetDiscountSettingParams) Reset() {
	*x = GetDiscountSettingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountSettingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountSettingParams) ProtoMessage() {}

func (x *GetDiscountSettingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountSettingParams.ProtoReflect.Descriptor instead.
func (*GetDiscountSettingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{16}
}

// get discount setting result
type GetDiscountSettingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount setting
	Setting *v2.DiscountSettingDef `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *GetDiscountSettingResult) Reset() {
	*x = GetDiscountSettingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountSettingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountSettingResult) ProtoMessage() {}

func (x *GetDiscountSettingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountSettingResult.ProtoReflect.Descriptor instead.
func (*GetDiscountSettingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{17}
}

func (x *GetDiscountSettingResult) GetSetting() *v2.DiscountSettingDef {
	if x != nil {
		return x.Setting
	}
	return nil
}

// update discount setting params
type UpdateDiscountSettingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount setting
	Setting *v2.DiscountSettingDef `protobuf:"bytes,2,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *UpdateDiscountSettingParams) Reset() {
	*x = UpdateDiscountSettingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDiscountSettingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDiscountSettingParams) ProtoMessage() {}

func (x *UpdateDiscountSettingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDiscountSettingParams.ProtoReflect.Descriptor instead.
func (*UpdateDiscountSettingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateDiscountSettingParams) GetSetting() *v2.DiscountSettingDef {
	if x != nil {
		return x.Setting
	}
	return nil
}

// update discount setting result
type UpdateDiscountSettingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount setting
	Setting *v2.DiscountSettingDef `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *UpdateDiscountSettingResult) Reset() {
	*x = UpdateDiscountSettingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDiscountSettingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDiscountSettingResult) ProtoMessage() {}

func (x *UpdateDiscountSettingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDiscountSettingResult.ProtoReflect.Descriptor instead.
func (*UpdateDiscountSettingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateDiscountSettingResult) GetSetting() *v2.DiscountSettingDef {
	if x != nil {
		return x.Setting
	}
	return nil
}

// get pricing rule overview params
type GetPricingRuleOverviewParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetPricingRuleOverviewParams) Reset() {
	*x = GetPricingRuleOverviewParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPricingRuleOverviewParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPricingRuleOverviewParams) ProtoMessage() {}

func (x *GetPricingRuleOverviewParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPricingRuleOverviewParams.ProtoReflect.Descriptor instead.
func (*GetPricingRuleOverviewParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{20}
}

// get pricing rule overview result
type GetPricingRuleOverviewResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// multi pets
	ActiveMultiPets int64 `protobuf:"varint,1,opt,name=active_multi_pets,json=activeMultiPets,proto3" json:"active_multi_pets,omitempty"`
	// multi stays
	ActiveMultiStays int64 `protobuf:"varint,2,opt,name=active_multi_stays,json=activeMultiStays,proto3" json:"active_multi_stays,omitempty"`
	// peak date
	ActivePeakDate int64 `protobuf:"varint,3,opt,name=active_peak_date,json=activePeakDate,proto3" json:"active_peak_date,omitempty"`
	// custom fees
	ActiveCustomFees int64 `protobuf:"varint,4,opt,name=active_custom_fees,json=activeCustomFees,proto3" json:"active_custom_fees,omitempty"`
	// off-hours fee
	ActiveOffHoursFee int64 `protobuf:"varint,5,opt,name=active_off_hours_fee,json=activeOffHoursFee,proto3" json:"active_off_hours_fee,omitempty"`
	// 24 hours charge
	Active_24HoursCharge int64 `protobuf:"varint,6,opt,name=active_24_hours_charge,json=active24HoursCharge,proto3" json:"active_24_hours_charge,omitempty"`
	// feeding charge
	ActiveFeedingCharge int64 `protobuf:"varint,7,opt,name=active_feeding_charge,json=activeFeedingCharge,proto3" json:"active_feeding_charge,omitempty"`
	// medication charge
	ActiveMedicationCharge int64 `protobuf:"varint,8,opt,name=active_medication_charge,json=activeMedicationCharge,proto3" json:"active_medication_charge,omitempty"`
	// zone
	ActiveZone int64 `protobuf:"varint,9,opt,name=active_zone,json=activeZone,proto3" json:"active_zone,omitempty"`
}

func (x *GetPricingRuleOverviewResult) Reset() {
	*x = GetPricingRuleOverviewResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPricingRuleOverviewResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPricingRuleOverviewResult) ProtoMessage() {}

func (x *GetPricingRuleOverviewResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPricingRuleOverviewResult.ProtoReflect.Descriptor instead.
func (*GetPricingRuleOverviewResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP(), []int{21}
}

func (x *GetPricingRuleOverviewResult) GetActiveMultiPets() int64 {
	if x != nil {
		return x.ActiveMultiPets
	}
	return 0
}

func (x *GetPricingRuleOverviewResult) GetActiveMultiStays() int64 {
	if x != nil {
		return x.ActiveMultiStays
	}
	return 0
}

func (x *GetPricingRuleOverviewResult) GetActivePeakDate() int64 {
	if x != nil {
		return x.ActivePeakDate
	}
	return 0
}

func (x *GetPricingRuleOverviewResult) GetActiveCustomFees() int64 {
	if x != nil {
		return x.ActiveCustomFees
	}
	return 0
}

func (x *GetPricingRuleOverviewResult) GetActiveOffHoursFee() int64 {
	if x != nil {
		return x.ActiveOffHoursFee
	}
	return 0
}

func (x *GetPricingRuleOverviewResult) GetActive_24HoursCharge() int64 {
	if x != nil {
		return x.Active_24HoursCharge
	}
	return 0
}

func (x *GetPricingRuleOverviewResult) GetActiveFeedingCharge() int64 {
	if x != nil {
		return x.ActiveFeedingCharge
	}
	return 0
}

func (x *GetPricingRuleOverviewResult) GetActiveMedicationCharge() int64 {
	if x != nil {
		return x.ActiveMedicationCharge
	}
	return 0
}

func (x *GetPricingRuleOverviewResult) GetActiveZone() int64 {
	if x != nil {
		return x.ActiveZone
	}
	return 0
}

var File_moego_api_offering_v2_pricing_rule_api_proto protoreflect.FileDescriptor

var file_moego_api_offering_v2_pricing_rule_api_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xc2, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x62, 0x0a,
	0x10, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65,
	0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x55, 0x70,
	0x73, 0x65, 0x72, 0x74, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65,
	0x66, 0x12, 0x43, 0x0a, 0x1e, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x70,
	0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x61, 0x70, 0x70, 0x6c, 0x79,
	0x54, 0x6f, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x63, 0x0a, 0x17, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x48, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0b,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x2f, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x60, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x48, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x22, 0xa4,
	0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x47, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa8, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x4a, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0c,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x78, 0x0a, 0x1a, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x5a,
	0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0a,
	0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xc0, 0x01, 0x0a, 0x1a, 0x43,
	0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x56, 0x0a, 0x0b, 0x70, 0x65, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x4a, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x22, 0xc5, 0x01,
	0x0a, 0x18, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x61, 0x0a, 0x0b, 0x70, 0x65,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08,
	0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x46, 0x0a,
	0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x07, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0xb0, 0x01, 0x0a, 0x18, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x5d, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x6e,
	0x6f, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x6e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x77, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x43, 0x0a, 0x1e,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x6f, 0x55, 0x70, 0x63,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x22, 0x19, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xc3, 0x01, 0x0a,
	0x1c, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x42, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x43, 0x0a, 0x17, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x14, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x22, 0x8a, 0x04, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x36, 0x0a, 0x17, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x61, 0x6c, 0x6c, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x12, 0x45, 0x0a, 0x1f, 0x61,
	0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x1c, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x6c, 0x6c, 0x5f, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72,
	0x65, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x14, 0x61, 0x6c, 0x6c, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x73,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x12, 0x43, 0x0a, 0x1e, 0x61, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x1b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x79, 0x63,
	0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x36, 0x0a,
	0x17, 0x61, 0x6c, 0x6c, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x73,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15,
	0x61, 0x6c, 0x6c, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x65, 0x64, 0x12, 0x45, 0x0a, 0x1f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x52, 0x1c,
	0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x14,
	0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x74, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x41,
	0x64, 0x64, 0x6f, 0x6e, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x12, 0x3f,
	0x0a, 0x1c, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64,
	0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x19, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x64, 0x64, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x22,
	0x7e, 0x0a, 0x18, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x62, 0x0a, 0x10, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x55, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x22,
	0x71, 0x0a, 0x18, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x69,
	0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69,
	0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x1a, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x62,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x07, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x22, 0x6f, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x50, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x22, 0x65, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x46, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65,
	0x66, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x1e, 0x0a, 0x1c, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x4f, 0x76, 0x65, 0x72,
	0x76, 0x69, 0x65, 0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xc5, 0x03, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x4f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x5f, 0x70, 0x65, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4d, 0x75,
	0x6c, 0x74, 0x69, 0x50, 0x65, 0x74, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x5f, 0x73, 0x74, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x10, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x53, 0x74, 0x61, 0x79, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x70, 0x65, 0x61, 0x6b, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x50, 0x65, 0x61, 0x6b, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x2c, 0x0a, 0x12, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x5f, 0x66, 0x65, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x73, 0x12, 0x2f, 0x0a,
	0x14, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6f, 0x66, 0x66, 0x5f, 0x68, 0x6f, 0x75, 0x72,
	0x73, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x4f, 0x66, 0x66, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x46, 0x65, 0x65, 0x12, 0x33,
	0x0a, 0x16, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x32, 0x34, 0x5f, 0x68, 0x6f, 0x75, 0x72,
	0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x32, 0x34, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x66, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x13, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x16, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5a, 0x6f,
	0x6e, 0x65, 0x32, 0xcd, 0x0a, 0x0a, 0x12, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x73, 0x0a, 0x11, 0x55, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6a,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a, 0x10, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x14,
	0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x6c,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x76, 0x0a, 0x12, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x73, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x82, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x76, 0x0a, 0x12,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x76, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7f, 0x0a, 0x15,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x82, 0x01,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x4f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x42, 0x7b, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x32, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_offering_v2_pricing_rule_api_proto_rawDescOnce sync.Once
	file_moego_api_offering_v2_pricing_rule_api_proto_rawDescData = file_moego_api_offering_v2_pricing_rule_api_proto_rawDesc
)

func file_moego_api_offering_v2_pricing_rule_api_proto_rawDescGZIP() []byte {
	file_moego_api_offering_v2_pricing_rule_api_proto_rawDescOnce.Do(func() {
		file_moego_api_offering_v2_pricing_rule_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_offering_v2_pricing_rule_api_proto_rawDescData)
	})
	return file_moego_api_offering_v2_pricing_rule_api_proto_rawDescData
}

var file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_moego_api_offering_v2_pricing_rule_api_proto_goTypes = []interface{}{
	(*UpsertPricingRuleParams)(nil),               // 0: moego.api.offering.v2.UpsertPricingRuleParams
	(*UpsertPricingRuleResult)(nil),               // 1: moego.api.offering.v2.UpsertPricingRuleResult
	(*GetPricingRuleParams)(nil),                  // 2: moego.api.offering.v2.GetPricingRuleParams
	(*GetPricingRuleResult)(nil),                  // 3: moego.api.offering.v2.GetPricingRuleResult
	(*ListPricingRulesParams)(nil),                // 4: moego.api.offering.v2.ListPricingRulesParams
	(*ListPricingRulesResult)(nil),                // 5: moego.api.offering.v2.ListPricingRulesResult
	(*CalculatePricingRuleParams)(nil),            // 6: moego.api.offering.v2.CalculatePricingRuleParams
	(*CalculatePricingRuleResult)(nil),            // 7: moego.api.offering.v2.CalculatePricingRuleResult
	(*PreviewPricingRuleParams)(nil),              // 8: moego.api.offering.v2.PreviewPricingRuleParams
	(*PreviewPricingRuleResult)(nil),              // 9: moego.api.offering.v2.PreviewPricingRuleResult
	(*DeletePricingRuleParams)(nil),               // 10: moego.api.offering.v2.DeletePricingRuleParams
	(*DeletePricingRuleResult)(nil),               // 11: moego.api.offering.v2.DeletePricingRuleResult
	(*ListAssociatedServicesParams)(nil),          // 12: moego.api.offering.v2.ListAssociatedServicesParams
	(*ListAssociatedServicesResult)(nil),          // 13: moego.api.offering.v2.ListAssociatedServicesResult
	(*CheckConfigurationParams)(nil),              // 14: moego.api.offering.v2.CheckConfigurationParams
	(*CheckConfigurationResult)(nil),              // 15: moego.api.offering.v2.CheckConfigurationResult
	(*GetDiscountSettingParams)(nil),              // 16: moego.api.offering.v2.GetDiscountSettingParams
	(*GetDiscountSettingResult)(nil),              // 17: moego.api.offering.v2.GetDiscountSettingResult
	(*UpdateDiscountSettingParams)(nil),           // 18: moego.api.offering.v2.UpdateDiscountSettingParams
	(*UpdateDiscountSettingResult)(nil),           // 19: moego.api.offering.v2.UpdateDiscountSettingResult
	(*GetPricingRuleOverviewParams)(nil),          // 20: moego.api.offering.v2.GetPricingRuleOverviewParams
	(*GetPricingRuleOverviewResult)(nil),          // 21: moego.api.offering.v2.GetPricingRuleOverviewResult
	(*v2.PricingRuleUpsertDef)(nil),               // 22: moego.models.offering.v2.PricingRuleUpsertDef
	(*v2.PricingRule)(nil),                        // 23: moego.models.offering.v2.PricingRule
	(*v2.ListPricingRuleFilter)(nil),              // 24: moego.models.offering.v2.ListPricingRuleFilter
	(*v21.PaginationRequest)(nil),                 // 25: moego.utils.v2.PaginationRequest
	(*v21.PaginationResponse)(nil),                // 26: moego.utils.v2.PaginationResponse
	(*v2.PetDetailCalculateDef)(nil),              // 27: moego.models.offering.v2.PetDetailCalculateDef
	(*v2.PetDetailCalculateResultDef)(nil),        // 28: moego.models.offering.v2.PetDetailCalculateResultDef
	(*v2.PreviewPetDetailCalculateDef)(nil),       // 29: moego.models.offering.v2.PreviewPetDetailCalculateDef
	(*v2.DiscountSettingDef)(nil),                 // 30: moego.models.offering.v2.DiscountSettingDef
	(*v2.PreviewPetDetailCalculateResultDef)(nil), // 31: moego.models.offering.v2.PreviewPetDetailCalculateResultDef
	(v2.RuleType)(0),                              // 32: moego.models.offering.v2.RuleType
}
var file_moego_api_offering_v2_pricing_rule_api_proto_depIdxs = []int32{
	22, // 0: moego.api.offering.v2.UpsertPricingRuleParams.pricing_rule_def:type_name -> moego.models.offering.v2.PricingRuleUpsertDef
	23, // 1: moego.api.offering.v2.UpsertPricingRuleResult.pricing_rule:type_name -> moego.models.offering.v2.PricingRule
	23, // 2: moego.api.offering.v2.GetPricingRuleResult.pricing_rule:type_name -> moego.models.offering.v2.PricingRule
	24, // 3: moego.api.offering.v2.ListPricingRulesParams.filter:type_name -> moego.models.offering.v2.ListPricingRuleFilter
	25, // 4: moego.api.offering.v2.ListPricingRulesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	23, // 5: moego.api.offering.v2.ListPricingRulesResult.pricing_rules:type_name -> moego.models.offering.v2.PricingRule
	26, // 6: moego.api.offering.v2.ListPricingRulesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	27, // 7: moego.api.offering.v2.CalculatePricingRuleParams.pet_details:type_name -> moego.models.offering.v2.PetDetailCalculateDef
	28, // 8: moego.api.offering.v2.CalculatePricingRuleResult.pet_details:type_name -> moego.models.offering.v2.PetDetailCalculateResultDef
	23, // 9: moego.api.offering.v2.CalculatePricingRuleResult.pricing_rules:type_name -> moego.models.offering.v2.PricingRule
	29, // 10: moego.api.offering.v2.PreviewPricingRuleParams.pet_details:type_name -> moego.models.offering.v2.PreviewPetDetailCalculateDef
	30, // 11: moego.api.offering.v2.PreviewPricingRuleParams.setting:type_name -> moego.models.offering.v2.DiscountSettingDef
	31, // 12: moego.api.offering.v2.PreviewPricingRuleResult.pet_details:type_name -> moego.models.offering.v2.PreviewPetDetailCalculateResultDef
	32, // 13: moego.api.offering.v2.ListAssociatedServicesParams.type:type_name -> moego.models.offering.v2.RuleType
	22, // 14: moego.api.offering.v2.CheckConfigurationParams.pricing_rule_def:type_name -> moego.models.offering.v2.PricingRuleUpsertDef
	30, // 15: moego.api.offering.v2.GetDiscountSettingResult.setting:type_name -> moego.models.offering.v2.DiscountSettingDef
	30, // 16: moego.api.offering.v2.UpdateDiscountSettingParams.setting:type_name -> moego.models.offering.v2.DiscountSettingDef
	30, // 17: moego.api.offering.v2.UpdateDiscountSettingResult.setting:type_name -> moego.models.offering.v2.DiscountSettingDef
	0,  // 18: moego.api.offering.v2.PricingRuleService.UpsertPricingRule:input_type -> moego.api.offering.v2.UpsertPricingRuleParams
	2,  // 19: moego.api.offering.v2.PricingRuleService.GetPricingRule:input_type -> moego.api.offering.v2.GetPricingRuleParams
	4,  // 20: moego.api.offering.v2.PricingRuleService.ListPricingRules:input_type -> moego.api.offering.v2.ListPricingRulesParams
	6,  // 21: moego.api.offering.v2.PricingRuleService.CalculatePricingRule:input_type -> moego.api.offering.v2.CalculatePricingRuleParams
	8,  // 22: moego.api.offering.v2.PricingRuleService.PreviewPricingRule:input_type -> moego.api.offering.v2.PreviewPricingRuleParams
	10, // 23: moego.api.offering.v2.PricingRuleService.DeletePricingRule:input_type -> moego.api.offering.v2.DeletePricingRuleParams
	12, // 24: moego.api.offering.v2.PricingRuleService.ListAssociatedServices:input_type -> moego.api.offering.v2.ListAssociatedServicesParams
	14, // 25: moego.api.offering.v2.PricingRuleService.CheckConfiguration:input_type -> moego.api.offering.v2.CheckConfigurationParams
	16, // 26: moego.api.offering.v2.PricingRuleService.GetDiscountSetting:input_type -> moego.api.offering.v2.GetDiscountSettingParams
	18, // 27: moego.api.offering.v2.PricingRuleService.UpdateDiscountSetting:input_type -> moego.api.offering.v2.UpdateDiscountSettingParams
	20, // 28: moego.api.offering.v2.PricingRuleService.GetPricingRuleOverview:input_type -> moego.api.offering.v2.GetPricingRuleOverviewParams
	1,  // 29: moego.api.offering.v2.PricingRuleService.UpsertPricingRule:output_type -> moego.api.offering.v2.UpsertPricingRuleResult
	3,  // 30: moego.api.offering.v2.PricingRuleService.GetPricingRule:output_type -> moego.api.offering.v2.GetPricingRuleResult
	5,  // 31: moego.api.offering.v2.PricingRuleService.ListPricingRules:output_type -> moego.api.offering.v2.ListPricingRulesResult
	7,  // 32: moego.api.offering.v2.PricingRuleService.CalculatePricingRule:output_type -> moego.api.offering.v2.CalculatePricingRuleResult
	9,  // 33: moego.api.offering.v2.PricingRuleService.PreviewPricingRule:output_type -> moego.api.offering.v2.PreviewPricingRuleResult
	11, // 34: moego.api.offering.v2.PricingRuleService.DeletePricingRule:output_type -> moego.api.offering.v2.DeletePricingRuleResult
	13, // 35: moego.api.offering.v2.PricingRuleService.ListAssociatedServices:output_type -> moego.api.offering.v2.ListAssociatedServicesResult
	15, // 36: moego.api.offering.v2.PricingRuleService.CheckConfiguration:output_type -> moego.api.offering.v2.CheckConfigurationResult
	17, // 37: moego.api.offering.v2.PricingRuleService.GetDiscountSetting:output_type -> moego.api.offering.v2.GetDiscountSettingResult
	19, // 38: moego.api.offering.v2.PricingRuleService.UpdateDiscountSetting:output_type -> moego.api.offering.v2.UpdateDiscountSettingResult
	21, // 39: moego.api.offering.v2.PricingRuleService.GetPricingRuleOverview:output_type -> moego.api.offering.v2.GetPricingRuleOverviewResult
	29, // [29:40] is the sub-list for method output_type
	18, // [18:29] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_moego_api_offering_v2_pricing_rule_api_proto_init() }
func file_moego_api_offering_v2_pricing_rule_api_proto_init() {
	if File_moego_api_offering_v2_pricing_rule_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertPricingRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertPricingRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPricingRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPricingRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRulesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRulesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculatePricingRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculatePricingRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewPricingRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewPricingRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePricingRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePricingRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAssociatedServicesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAssociatedServicesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckConfigurationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckConfigurationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountSettingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountSettingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDiscountSettingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDiscountSettingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPricingRuleOverviewParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPricingRuleOverviewResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes[15].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_offering_v2_pricing_rule_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_offering_v2_pricing_rule_api_proto_goTypes,
		DependencyIndexes: file_moego_api_offering_v2_pricing_rule_api_proto_depIdxs,
		MessageInfos:      file_moego_api_offering_v2_pricing_rule_api_proto_msgTypes,
	}.Build()
	File_moego_api_offering_v2_pricing_rule_api_proto = out.File
	file_moego_api_offering_v2_pricing_rule_api_proto_rawDesc = nil
	file_moego_api_offering_v2_pricing_rule_api_proto_goTypes = nil
	file_moego_api_offering_v2_pricing_rule_api_proto_depIdxs = nil
}
