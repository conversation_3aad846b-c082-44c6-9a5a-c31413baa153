// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/staff_availability_models.proto

package organizationpb

import (
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AvailabilityType staff availability 配置
type AvailabilityType int32

const (
	// availability type
	AvailabilityType_AVAILABILITY_TYPE_UNSPECIFIED AvailabilityType = 0
	// availability type
	AvailabilityType_BY_TIME AvailabilityType = 1
	// availability type
	AvailabilityType_BY_SLOT AvailabilityType = 2
)

// Enum value maps for AvailabilityType.
var (
	AvailabilityType_name = map[int32]string{
		0: "AVAILABILITY_TYPE_UNSPECIFIED",
		1: "BY_TIME",
		2: "BY_SLOT",
	}
	AvailabilityType_value = map[string]int32{
		"AVAILABILITY_TYPE_UNSPECIFIED": 0,
		"BY_TIME":                       1,
		"BY_SLOT":                       2,
	}
)

func (x AvailabilityType) Enum() *AvailabilityType {
	p := new(AvailabilityType)
	*p = x
	return p
}

func (x AvailabilityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AvailabilityType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_staff_availability_models_proto_enumTypes[0].Descriptor()
}

func (AvailabilityType) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_staff_availability_models_proto_enumTypes[0]
}

func (x AvailabilityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AvailabilityType.Descriptor instead.
func (AvailabilityType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{0}
}

// ScheduleType
type ScheduleType int32

const (
	// schedule type unspecified
	ScheduleType_SCHEDULE_TYPE_UNSPECIFIED ScheduleType = 0
	// Every one week
	ScheduleType_ONE_WEEK ScheduleType = 1
	// Every two weeks
	ScheduleType_TWO_WEEK ScheduleType = 2
	// Every three weeks
	ScheduleType_THREE_WEEK ScheduleType = 3
	// Every four weeks
	ScheduleType_FOUR_WEEK ScheduleType = 4
)

// Enum value maps for ScheduleType.
var (
	ScheduleType_name = map[int32]string{
		0: "SCHEDULE_TYPE_UNSPECIFIED",
		1: "ONE_WEEK",
		2: "TWO_WEEK",
		3: "THREE_WEEK",
		4: "FOUR_WEEK",
	}
	ScheduleType_value = map[string]int32{
		"SCHEDULE_TYPE_UNSPECIFIED": 0,
		"ONE_WEEK":                  1,
		"TWO_WEEK":                  2,
		"THREE_WEEK":                3,
		"FOUR_WEEK":                 4,
	}
)

func (x ScheduleType) Enum() *ScheduleType {
	p := new(ScheduleType)
	*p = x
	return p
}

func (x ScheduleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScheduleType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_staff_availability_models_proto_enumTypes[1].Descriptor()
}

func (ScheduleType) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_staff_availability_models_proto_enumTypes[1]
}

func (x ScheduleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScheduleType.Descriptor instead.
func (ScheduleType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{1}
}

// limit type
type LimitType int32

const (
	// limit type unspecified
	LimitType_LIMIT_TYPE_UNSPECIFIED LimitType = 0
	// limit by service
	LimitType_SERVICE_LIMIT LimitType = 1
	// limit by size
	LimitType_PET_SIZE_LIMIT LimitType = 2
	// limit by breed
	LimitType_PET_BREED_LIMIT LimitType = 3
)

// Enum value maps for LimitType.
var (
	LimitType_name = map[int32]string{
		0: "LIMIT_TYPE_UNSPECIFIED",
		1: "SERVICE_LIMIT",
		2: "PET_SIZE_LIMIT",
		3: "PET_BREED_LIMIT",
	}
	LimitType_value = map[string]int32{
		"LIMIT_TYPE_UNSPECIFIED": 0,
		"SERVICE_LIMIT":          1,
		"PET_SIZE_LIMIT":         2,
		"PET_BREED_LIMIT":        3,
	}
)

func (x LimitType) Enum() *LimitType {
	p := new(LimitType)
	*p = x
	return p
}

func (x LimitType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LimitType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_staff_availability_models_proto_enumTypes[2].Descriptor()
}

func (LimitType) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_staff_availability_models_proto_enumTypes[2]
}

func (x LimitType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LimitType.Descriptor instead.
func (LimitType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{2}
}

// staff availability
type StaffAvailability struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id/business id不需要
	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// schedule type
	ScheduleType ScheduleType `protobuf:"varint,3,opt,name=schedule_type,json=scheduleType,proto3,enum=moego.models.organization.v1.ScheduleType" json:"schedule_type,omitempty"`
	// slot daily setting
	SlotAvailabilityDayList []*SlotAvailabilityDay `protobuf:"bytes,4,rep,name=slot_availability_day_list,json=slotAvailabilityDayList,proto3" json:"slot_availability_day_list,omitempty"`
	// time daily setting
	TimeAvailabilityDayList []*TimeAvailabilityDay `protobuf:"bytes,5,rep,name=time_availability_day_list,json=timeAvailabilityDayList,proto3" json:"time_availability_day_list,omitempty"`
	// slot start sunday
	SlotStartSunday string `protobuf:"bytes,6,opt,name=slot_start_sunday,json=slotStartSunday,proto3" json:"slot_start_sunday,omitempty"`
	// time start sunday
	TimeStartSunday string `protobuf:"bytes,7,opt,name=time_start_sunday,json=timeStartSunday,proto3" json:"time_start_sunday,omitempty"`
	// time schedule type
	TimeScheduleType ScheduleType `protobuf:"varint,8,opt,name=time_schedule_type,json=timeScheduleType,proto3,enum=moego.models.organization.v1.ScheduleType" json:"time_schedule_type,omitempty"`
}

func (x *StaffAvailability) Reset() {
	*x = StaffAvailability{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffAvailability) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffAvailability) ProtoMessage() {}

func (x *StaffAvailability) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffAvailability.ProtoReflect.Descriptor instead.
func (*StaffAvailability) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{0}
}

func (x *StaffAvailability) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *StaffAvailability) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *StaffAvailability) GetScheduleType() ScheduleType {
	if x != nil {
		return x.ScheduleType
	}
	return ScheduleType_SCHEDULE_TYPE_UNSPECIFIED
}

func (x *StaffAvailability) GetSlotAvailabilityDayList() []*SlotAvailabilityDay {
	if x != nil {
		return x.SlotAvailabilityDayList
	}
	return nil
}

func (x *StaffAvailability) GetTimeAvailabilityDayList() []*TimeAvailabilityDay {
	if x != nil {
		return x.TimeAvailabilityDayList
	}
	return nil
}

func (x *StaffAvailability) GetSlotStartSunday() string {
	if x != nil {
		return x.SlotStartSunday
	}
	return ""
}

func (x *StaffAvailability) GetTimeStartSunday() string {
	if x != nil {
		return x.TimeStartSunday
	}
	return ""
}

func (x *StaffAvailability) GetTimeScheduleType() ScheduleType {
	if x != nil {
		return x.TimeScheduleType
	}
	return ScheduleType_SCHEDULE_TYPE_UNSPECIFIED
}

// slot availability day
type SlotAvailabilityDay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// day of week
	DayOfWeek dayofweek.DayOfWeek `protobuf:"varint,1,opt,name=day_of_week,json=dayOfWeek,proto3,enum=google.type.DayOfWeek" json:"day_of_week,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// schedule type
	ScheduleType ScheduleType `protobuf:"varint,3,opt,name=schedule_type,json=scheduleType,proto3,enum=moego.models.organization.v1.ScheduleType" json:"schedule_type,omitempty"`
	// slot daily setting
	SlotDailySetting *SlotDailySetting `protobuf:"bytes,4,opt,name=slot_daily_setting,json=slotDailySetting,proto3" json:"slot_daily_setting,omitempty"`
	// staff available hour
	SlotHourSettingList []*SlotHourSetting `protobuf:"bytes,5,rep,name=slot_hour_setting_list,json=slotHourSettingList,proto3" json:"slot_hour_setting_list,omitempty"`
	// override date
	OverrideDate string `protobuf:"bytes,6,opt,name=override_date,json=overrideDate,proto3" json:"override_date,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,7,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *SlotAvailabilityDay) Reset() {
	*x = SlotAvailabilityDay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlotAvailabilityDay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotAvailabilityDay) ProtoMessage() {}

func (x *SlotAvailabilityDay) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotAvailabilityDay.ProtoReflect.Descriptor instead.
func (*SlotAvailabilityDay) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{1}
}

func (x *SlotAvailabilityDay) GetDayOfWeek() dayofweek.DayOfWeek {
	if x != nil {
		return x.DayOfWeek
	}
	return dayofweek.DayOfWeek(0)
}

func (x *SlotAvailabilityDay) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *SlotAvailabilityDay) GetScheduleType() ScheduleType {
	if x != nil {
		return x.ScheduleType
	}
	return ScheduleType_SCHEDULE_TYPE_UNSPECIFIED
}

func (x *SlotAvailabilityDay) GetSlotDailySetting() *SlotDailySetting {
	if x != nil {
		return x.SlotDailySetting
	}
	return nil
}

func (x *SlotAvailabilityDay) GetSlotHourSettingList() []*SlotHourSetting {
	if x != nil {
		return x.SlotHourSettingList
	}
	return nil
}

func (x *SlotAvailabilityDay) GetOverrideDate() string {
	if x != nil {
		return x.OverrideDate
	}
	return ""
}

func (x *SlotAvailabilityDay) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// time availability day
type TimeAvailabilityDay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// day of week
	DayOfWeek dayofweek.DayOfWeek `protobuf:"varint,1,opt,name=day_of_week,json=dayOfWeek,proto3,enum=google.type.DayOfWeek" json:"day_of_week,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// schedule type
	ScheduleType ScheduleType `protobuf:"varint,3,opt,name=schedule_type,json=scheduleType,proto3,enum=moego.models.organization.v1.ScheduleType" json:"schedule_type,omitempty"`
	// time daily setting
	TimeDailySetting *TimeDailySetting `protobuf:"bytes,5,opt,name=time_daily_setting,json=timeDailySetting,proto3" json:"time_daily_setting,omitempty"`
	// time available hour
	TimeHourSettingList []*TimeHourSetting `protobuf:"bytes,10,rep,name=time_hour_setting_list,json=timeHourSettingList,proto3" json:"time_hour_setting_list,omitempty"`
	// override date
	OverrideDate string `protobuf:"bytes,6,opt,name=override_date,json=overrideDate,proto3" json:"override_date,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,7,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *TimeAvailabilityDay) Reset() {
	*x = TimeAvailabilityDay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeAvailabilityDay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeAvailabilityDay) ProtoMessage() {}

func (x *TimeAvailabilityDay) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeAvailabilityDay.ProtoReflect.Descriptor instead.
func (*TimeAvailabilityDay) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{2}
}

func (x *TimeAvailabilityDay) GetDayOfWeek() dayofweek.DayOfWeek {
	if x != nil {
		return x.DayOfWeek
	}
	return dayofweek.DayOfWeek(0)
}

func (x *TimeAvailabilityDay) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *TimeAvailabilityDay) GetScheduleType() ScheduleType {
	if x != nil {
		return x.ScheduleType
	}
	return ScheduleType_SCHEDULE_TYPE_UNSPECIFIED
}

func (x *TimeAvailabilityDay) GetTimeDailySetting() *TimeDailySetting {
	if x != nil {
		return x.TimeDailySetting
	}
	return nil
}

func (x *TimeAvailabilityDay) GetTimeHourSettingList() []*TimeHourSetting {
	if x != nil {
		return x.TimeHourSettingList
	}
	return nil
}

func (x *TimeAvailabilityDay) GetOverrideDate() string {
	if x != nil {
		return x.OverrideDate
	}
	return ""
}

func (x *TimeAvailabilityDay) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// by slot daily setting def
type SlotDailySetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// slot start time
	StartTime int32 `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// slot end time
	EndTime int32 `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// slot capacity
	Capacity int32 `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"`
	// booking limit
	Limit *BookingLimitation `protobuf:"bytes,4,opt,name=limit,proto3" json:"limit,omitempty"`
	// booking limit group
	LimitationGroups []*LimitationGroup `protobuf:"bytes,5,rep,name=limitation_groups,json=limitationGroups,proto3" json:"limitation_groups,omitempty"`
}

func (x *SlotDailySetting) Reset() {
	*x = SlotDailySetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlotDailySetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotDailySetting) ProtoMessage() {}

func (x *SlotDailySetting) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotDailySetting.ProtoReflect.Descriptor instead.
func (*SlotDailySetting) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{3}
}

func (x *SlotDailySetting) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *SlotDailySetting) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *SlotDailySetting) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *SlotDailySetting) GetLimit() *BookingLimitation {
	if x != nil {
		return x.Limit
	}
	return nil
}

func (x *SlotDailySetting) GetLimitationGroups() []*LimitationGroup {
	if x != nil {
		return x.LimitationGroups
	}
	return nil
}

// by time daily setting def
type TimeDailySetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking limit
	Limit *BookingLimitation `protobuf:"bytes,1,opt,name=limit,proto3" json:"limit,omitempty"`
	// booking limit group
	LimitationGroups []*LimitationGroup `protobuf:"bytes,5,rep,name=limitation_groups,json=limitationGroups,proto3" json:"limitation_groups,omitempty"`
}

func (x *TimeDailySetting) Reset() {
	*x = TimeDailySetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeDailySetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeDailySetting) ProtoMessage() {}

func (x *TimeDailySetting) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeDailySetting.ProtoReflect.Descriptor instead.
func (*TimeDailySetting) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{4}
}

func (x *TimeDailySetting) GetLimit() *BookingLimitation {
	if x != nil {
		return x.Limit
	}
	return nil
}

func (x *TimeDailySetting) GetLimitationGroups() []*LimitationGroup {
	if x != nil {
		return x.LimitationGroups
	}
	return nil
}

// by slot hour setting def
type SlotHourSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// slot start time
	StartTime int32 `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// slot capacity
	Capacity int32 `protobuf:"varint,2,opt,name=capacity,proto3" json:"capacity,omitempty"`
	// booking limit
	Limit *BookingLimitation `protobuf:"bytes,3,opt,name=limit,proto3" json:"limit,omitempty"`
	// end time
	EndTime int32 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// booking limit group
	LimitationGroups []*LimitationGroup `protobuf:"bytes,5,rep,name=limitation_groups,json=limitationGroups,proto3" json:"limitation_groups,omitempty"`
	// limitation note
	Note string `protobuf:"bytes,6,opt,name=note,proto3" json:"note,omitempty"`
}

func (x *SlotHourSetting) Reset() {
	*x = SlotHourSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlotHourSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlotHourSetting) ProtoMessage() {}

func (x *SlotHourSetting) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlotHourSetting.ProtoReflect.Descriptor instead.
func (*SlotHourSetting) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{5}
}

func (x *SlotHourSetting) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *SlotHourSetting) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *SlotHourSetting) GetLimit() *BookingLimitation {
	if x != nil {
		return x.Limit
	}
	return nil
}

func (x *SlotHourSetting) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *SlotHourSetting) GetLimitationGroups() []*LimitationGroup {
	if x != nil {
		return x.LimitationGroups
	}
	return nil
}

func (x *SlotHourSetting) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

// by time hour setting def
type TimeHourSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start time
	StartTime int32 `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time
	EndTime int32 `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *TimeHourSetting) Reset() {
	*x = TimeHourSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeHourSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeHourSetting) ProtoMessage() {}

func (x *TimeHourSetting) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeHourSetting.ProtoReflect.Descriptor instead.
func (*TimeHourSetting) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{6}
}

func (x *TimeHourSetting) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *TimeHourSetting) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

// slot/time limit setting
type BookingLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service limits
	ServiceLimits []*BookingLimitation_ServiceLimitation `protobuf:"bytes,1,rep,name=service_limits,json=serviceLimits,proto3" json:"service_limits,omitempty"`
	// pet size limits
	PetSizeLimits []*BookingLimitation_PetSizeLimitation `protobuf:"bytes,2,rep,name=pet_size_limits,json=petSizeLimits,proto3" json:"pet_size_limits,omitempty"`
	// pet breed limits
	PetBreedLimits []*BookingLimitation_PetBreedLimitation `protobuf:"bytes,3,rep,name=pet_breed_limits,json=petBreedLimits,proto3" json:"pet_breed_limits,omitempty"`
}

func (x *BookingLimitation) Reset() {
	*x = BookingLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookingLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingLimitation) ProtoMessage() {}

func (x *BookingLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingLimitation.ProtoReflect.Descriptor instead.
func (*BookingLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{7}
}

func (x *BookingLimitation) GetServiceLimits() []*BookingLimitation_ServiceLimitation {
	if x != nil {
		return x.ServiceLimits
	}
	return nil
}

func (x *BookingLimitation) GetPetSizeLimits() []*BookingLimitation_PetSizeLimitation {
	if x != nil {
		return x.PetSizeLimits
	}
	return nil
}

func (x *BookingLimitation) GetPetBreedLimits() []*BookingLimitation_PetBreedLimitation {
	if x != nil {
		return x.PetBreedLimits
	}
	return nil
}

// limitation group
type LimitationGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service limits
	ServiceLimits []*ServiceLimitation `protobuf:"bytes,1,rep,name=service_limits,json=serviceLimits,proto3" json:"service_limits,omitempty"`
	// pet size limits
	PetSizeLimits []*PetSizeLimitation `protobuf:"bytes,2,rep,name=pet_size_limits,json=petSizeLimits,proto3" json:"pet_size_limits,omitempty"`
	// pet breed limits
	PetBreedLimits []*PetBreedLimitation `protobuf:"bytes,3,rep,name=pet_breed_limits,json=petBreedLimits,proto3" json:"pet_breed_limits,omitempty"`
	// only accept selected
	OnlyAcceptSelected bool `protobuf:"varint,4,opt,name=only_accept_selected,json=onlyAcceptSelected,proto3" json:"only_accept_selected,omitempty"`
}

func (x *LimitationGroup) Reset() {
	*x = LimitationGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitationGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitationGroup) ProtoMessage() {}

func (x *LimitationGroup) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitationGroup.ProtoReflect.Descriptor instead.
func (*LimitationGroup) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{8}
}

func (x *LimitationGroup) GetServiceLimits() []*ServiceLimitation {
	if x != nil {
		return x.ServiceLimits
	}
	return nil
}

func (x *LimitationGroup) GetPetSizeLimits() []*PetSizeLimitation {
	if x != nil {
		return x.PetSizeLimits
	}
	return nil
}

func (x *LimitationGroup) GetPetBreedLimits() []*PetBreedLimitation {
	if x != nil {
		return x.PetBreedLimits
	}
	return nil
}

func (x *LimitationGroup) GetOnlyAcceptSelected() bool {
	if x != nil {
		return x.OnlyAcceptSelected
	}
	return false
}

// service limitation
type ServiceLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service_id
	ServiceIds []int64 `protobuf:"varint,1,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// is_all_service
	IsAllService bool `protobuf:"varint,2,opt,name=is_all_service,json=isAllService,proto3" json:"is_all_service,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *ServiceLimitation) Reset() {
	*x = ServiceLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceLimitation) ProtoMessage() {}

func (x *ServiceLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceLimitation.ProtoReflect.Descriptor instead.
func (*ServiceLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{9}
}

func (x *ServiceLimitation) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *ServiceLimitation) GetIsAllService() bool {
	if x != nil {
		return x.IsAllService
	}
	return false
}

func (x *ServiceLimitation) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

// pet size limitation
type PetSizeLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet_size_id
	PetSizeIds []int64 `protobuf:"varint,1,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// is_all_size
	IsAllSize bool `protobuf:"varint,2,opt,name=is_all_size,json=isAllSize,proto3" json:"is_all_size,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *PetSizeLimitation) Reset() {
	*x = PetSizeLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetSizeLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetSizeLimitation) ProtoMessage() {}

func (x *PetSizeLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetSizeLimitation.ProtoReflect.Descriptor instead.
func (*PetSizeLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{10}
}

func (x *PetSizeLimitation) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *PetSizeLimitation) GetIsAllSize() bool {
	if x != nil {
		return x.IsAllSize
	}
	return false
}

func (x *PetSizeLimitation) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

// pet breed limitation
type PetBreedLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet_type_id
	PetTypeId int64 `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// is_all_breed
	IsAllBreed bool `protobuf:"varint,2,opt,name=is_all_breed,json=isAllBreed,proto3" json:"is_all_breed,omitempty"`
	// breed_ids
	BreedIds []int64 `protobuf:"varint,3,rep,packed,name=breed_ids,json=breedIds,proto3" json:"breed_ids,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,4,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *PetBreedLimitation) Reset() {
	*x = PetBreedLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetBreedLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetBreedLimitation) ProtoMessage() {}

func (x *PetBreedLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetBreedLimitation.ProtoReflect.Descriptor instead.
func (*PetBreedLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{11}
}

func (x *PetBreedLimitation) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *PetBreedLimitation) GetIsAllBreed() bool {
	if x != nil {
		return x.IsAllBreed
	}
	return false
}

func (x *PetBreedLimitation) GetBreedIds() []int64 {
	if x != nil {
		return x.BreedIds
	}
	return nil
}

func (x *PetBreedLimitation) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

// limitation group hit view
type LimitationGroupHitView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service limits
	ServiceLimits []*ServiceLimitation `protobuf:"bytes,1,rep,name=service_limits,json=serviceLimits,proto3" json:"service_limits,omitempty"`
	// pet size limits
	PetSizeLimits []*PetSizeLimitation `protobuf:"bytes,2,rep,name=pet_size_limits,json=petSizeLimits,proto3" json:"pet_size_limits,omitempty"`
	// pet breed limits
	PetBreedLimits []*PetBreedLimitation `protobuf:"bytes,3,rep,name=pet_breed_limits,json=petBreedLimits,proto3" json:"pet_breed_limits,omitempty"`
	// only accept selected
	OnlyAcceptSelected bool `protobuf:"varint,4,opt,name=only_accept_selected,json=onlyAcceptSelected,proto3" json:"only_accept_selected,omitempty"`
	// hit limit
	HitLimit bool `protobuf:"varint,5,opt,name=hit_limit,json=hitLimit,proto3" json:"hit_limit,omitempty"`
}

func (x *LimitationGroupHitView) Reset() {
	*x = LimitationGroupHitView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitationGroupHitView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitationGroupHitView) ProtoMessage() {}

func (x *LimitationGroupHitView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitationGroupHitView.ProtoReflect.Descriptor instead.
func (*LimitationGroupHitView) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{12}
}

func (x *LimitationGroupHitView) GetServiceLimits() []*ServiceLimitation {
	if x != nil {
		return x.ServiceLimits
	}
	return nil
}

func (x *LimitationGroupHitView) GetPetSizeLimits() []*PetSizeLimitation {
	if x != nil {
		return x.PetSizeLimits
	}
	return nil
}

func (x *LimitationGroupHitView) GetPetBreedLimits() []*PetBreedLimitation {
	if x != nil {
		return x.PetBreedLimits
	}
	return nil
}

func (x *LimitationGroupHitView) GetOnlyAcceptSelected() bool {
	if x != nil {
		return x.OnlyAcceptSelected
	}
	return false
}

func (x *LimitationGroupHitView) GetHitLimit() bool {
	if x != nil {
		return x.HitLimit
	}
	return false
}

// service limitation
type BookingLimitation_ServiceLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service_id
	ServiceIds []int64 `protobuf:"varint,1,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// is_all_service
	IsAllService bool `protobuf:"varint,2,opt,name=is_all_service,json=isAllService,proto3" json:"is_all_service,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *BookingLimitation_ServiceLimitation) Reset() {
	*x = BookingLimitation_ServiceLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookingLimitation_ServiceLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingLimitation_ServiceLimitation) ProtoMessage() {}

func (x *BookingLimitation_ServiceLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingLimitation_ServiceLimitation.ProtoReflect.Descriptor instead.
func (*BookingLimitation_ServiceLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{7, 0}
}

func (x *BookingLimitation_ServiceLimitation) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *BookingLimitation_ServiceLimitation) GetIsAllService() bool {
	if x != nil {
		return x.IsAllService
	}
	return false
}

func (x *BookingLimitation_ServiceLimitation) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

// pet size limitation
type BookingLimitation_PetSizeLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet_size_id
	PetSizeIds []int64 `protobuf:"varint,1,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// is_all_size
	IsAllSize bool `protobuf:"varint,2,opt,name=is_all_size,json=isAllSize,proto3" json:"is_all_size,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *BookingLimitation_PetSizeLimitation) Reset() {
	*x = BookingLimitation_PetSizeLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookingLimitation_PetSizeLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingLimitation_PetSizeLimitation) ProtoMessage() {}

func (x *BookingLimitation_PetSizeLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingLimitation_PetSizeLimitation.ProtoReflect.Descriptor instead.
func (*BookingLimitation_PetSizeLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{7, 1}
}

func (x *BookingLimitation_PetSizeLimitation) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *BookingLimitation_PetSizeLimitation) GetIsAllSize() bool {
	if x != nil {
		return x.IsAllSize
	}
	return false
}

func (x *BookingLimitation_PetSizeLimitation) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

// pet breed limitation
type BookingLimitation_PetBreedLimitation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet_type_id
	PetTypeId int64 `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// is_all_breed
	IsAllBreed bool `protobuf:"varint,2,opt,name=is_all_breed,json=isAllBreed,proto3" json:"is_all_breed,omitempty"`
	// breed_ids
	BreedIds []int64 `protobuf:"varint,3,rep,packed,name=breed_ids,json=breedIds,proto3" json:"breed_ids,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,4,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *BookingLimitation_PetBreedLimitation) Reset() {
	*x = BookingLimitation_PetBreedLimitation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookingLimitation_PetBreedLimitation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingLimitation_PetBreedLimitation) ProtoMessage() {}

func (x *BookingLimitation_PetBreedLimitation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingLimitation_PetBreedLimitation.ProtoReflect.Descriptor instead.
func (*BookingLimitation_PetBreedLimitation) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP(), []int{7, 2}
}

func (x *BookingLimitation_PetBreedLimitation) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *BookingLimitation_PetBreedLimitation) GetIsAllBreed() bool {
	if x != nil {
		return x.IsAllBreed
	}
	return false
}

func (x *BookingLimitation_PetBreedLimitation) GetBreedIds() []int64 {
	if x != nil {
		return x.BreedIds
	}
	return nil
}

func (x *BookingLimitation_PetBreedLimitation) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

var File_moego_models_organization_v1_staff_availability_models_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_staff_availability_models_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x79, 0x6f, 0x66, 0x77,
	0x65, 0x65, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb4, 0x04, 0x0a, 0x11, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x4f, 0x0a,
	0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x6e,
	0x0a, 0x1a, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x44, 0x61, 0x79, 0x52, 0x17, 0x73, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x6e,
	0x0a, 0x1a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x44, 0x61, 0x79, 0x52, 0x17, 0x74, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a,
	0x0a, 0x11, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x75, 0x6e,
	0x64, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x6c, 0x6f, 0x74, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x53, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x53, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x12, 0x58, 0x0a, 0x12, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10,
	0x74, 0x69, 0x6d, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x22, 0xc3, 0x03, 0x0a, 0x13, 0x53, 0x6c, 0x6f, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x12, 0x36, 0x0a, 0x0b, 0x64, 0x61, 0x79, 0x5f,
	0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x79, 0x4f,
	0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x09, 0x64, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x4f, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x5c, 0x0a, 0x12, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x64, 0x61, 0x69,
	0x6c, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x6c, 0x6f, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x10, 0x73, 0x6c, 0x6f, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x62, 0x0a, 0x16, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x5f,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x6c, 0x6f, 0x74, 0x48, 0x6f, 0x75, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x52, 0x13, 0x73, 0x6c, 0x6f, 0x74, 0x48, 0x6f, 0x75, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0xc3, 0x03, 0x0a, 0x13, 0x54, 0x69, 0x6d, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x79, 0x12, 0x36,
	0x0a, 0x0b, 0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x09, 0x64, 0x61, 0x79,
	0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x4f, 0x0a, 0x0d, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5c, 0x0a, 0x12, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x69, 0x6c,
	0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x62, 0x0a, 0x16, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x68, 0x6f, 0x75, 0x72, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x48, 0x6f, 0x75, 0x72,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x13, 0x74, 0x69, 0x6d, 0x65, 0x48, 0x6f, 0x75,
	0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0x8b, 0x02, 0x0a,
	0x10, 0x53, 0x6c, 0x6f, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x45, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x5a,
	0x0a, 0x11, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x10, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0xb5, 0x01, 0x0a, 0x10, 0x54,
	0x69, 0x6d, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12,
	0x45, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x5a, 0x0a, 0x11, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x10, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x22, 0x9e, 0x02, 0x0a, 0x0f, 0x53, 0x6c, 0x6f, 0x74, 0x48, 0x6f, 0x75, 0x72, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x12, 0x45, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x5a, 0x0a, 0x11, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x10, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x6f, 0x74, 0x65, 0x22, 0x4b, 0x0a, 0x0f, 0x54, 0x69, 0x6d, 0x65, 0x48, 0x6f, 0x75, 0x72, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0xd3, 0x05, 0x0a, 0x11, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x68, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73,
	0x12, 0x69, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x69,
	0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x70, 0x65,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x6c, 0x0a, 0x10, 0x70,
	0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x42, 0x72,
	0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x1a, 0x76, 0x0a, 0x11, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12,
	0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x1a, 0x71, 0x0a, 0x11, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x65,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x61,
	0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x1a, 0x8f, 0x01, 0x0a, 0x12, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65,
	0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0b, 0x70,
	0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x08, 0x62, 0x72, 0x65, 0x65, 0x64, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x22, 0xd0, 0x02, 0x0a, 0x0f, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x56, 0x0a, 0x0e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x73, 0x12, 0x57, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x69,
	0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x70, 0x65,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x5a, 0x0a, 0x10, 0x70,
	0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65,
	0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x6f, 0x6e, 0x6c, 0x79, 0x5f,
	0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x6f, 0x6e, 0x6c, 0x79, 0x41, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0x76, 0x0a, 0x11, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12,
	0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x22, 0x71, 0x0a, 0x11, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x65,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x61,
	0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x22, 0x8f, 0x01, 0x0a, 0x12, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65,
	0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0b, 0x70,
	0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x08, 0x62, 0x72, 0x65, 0x65, 0x64, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x22, 0xf4, 0x02, 0x0a, 0x16, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x48, 0x69, 0x74, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x56, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x57, 0x0a, 0x0f, 0x70, 0x65, 0x74,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x73, 0x12, 0x5a, 0x0a, 0x10, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x42,
	0x72, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e,
	0x70, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x30,
	0x0a, 0x14, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x6f, 0x6e,
	0x6c, 0x79, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x68, 0x69, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x2a, 0x4f, 0x0a,
	0x10, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x42, 0x59, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x42, 0x59, 0x5f, 0x53, 0x4c, 0x4f, 0x54, 0x10, 0x02, 0x2a, 0x68,
	0x0a, 0x0c, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d,
	0x0a, 0x19, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x4f, 0x4e, 0x45, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x54,
	0x57, 0x4f, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x48, 0x52,
	0x45, 0x45, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x4f, 0x55,
	0x52, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x10, 0x04, 0x2a, 0x63, 0x0a, 0x09, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4c, 0x49, 0x4d,
	0x49, 0x54, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x45, 0x54, 0x5f, 0x53, 0x49, 0x5a, 0x45,
	0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x45, 0x54, 0x5f,
	0x42, 0x52, 0x45, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x03, 0x42, 0x8a, 0x01,
	0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_staff_availability_models_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_staff_availability_models_proto_rawDescData = file_moego_models_organization_v1_staff_availability_models_proto_rawDesc
)

func file_moego_models_organization_v1_staff_availability_models_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_staff_availability_models_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_staff_availability_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_staff_availability_models_proto_rawDescData)
	})
	return file_moego_models_organization_v1_staff_availability_models_proto_rawDescData
}

var file_moego_models_organization_v1_staff_availability_models_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_organization_v1_staff_availability_models_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_moego_models_organization_v1_staff_availability_models_proto_goTypes = []interface{}{
	(AvailabilityType)(0),                        // 0: moego.models.organization.v1.AvailabilityType
	(ScheduleType)(0),                            // 1: moego.models.organization.v1.ScheduleType
	(LimitType)(0),                               // 2: moego.models.organization.v1.LimitType
	(*StaffAvailability)(nil),                    // 3: moego.models.organization.v1.StaffAvailability
	(*SlotAvailabilityDay)(nil),                  // 4: moego.models.organization.v1.SlotAvailabilityDay
	(*TimeAvailabilityDay)(nil),                  // 5: moego.models.organization.v1.TimeAvailabilityDay
	(*SlotDailySetting)(nil),                     // 6: moego.models.organization.v1.SlotDailySetting
	(*TimeDailySetting)(nil),                     // 7: moego.models.organization.v1.TimeDailySetting
	(*SlotHourSetting)(nil),                      // 8: moego.models.organization.v1.SlotHourSetting
	(*TimeHourSetting)(nil),                      // 9: moego.models.organization.v1.TimeHourSetting
	(*BookingLimitation)(nil),                    // 10: moego.models.organization.v1.BookingLimitation
	(*LimitationGroup)(nil),                      // 11: moego.models.organization.v1.LimitationGroup
	(*ServiceLimitation)(nil),                    // 12: moego.models.organization.v1.ServiceLimitation
	(*PetSizeLimitation)(nil),                    // 13: moego.models.organization.v1.PetSizeLimitation
	(*PetBreedLimitation)(nil),                   // 14: moego.models.organization.v1.PetBreedLimitation
	(*LimitationGroupHitView)(nil),               // 15: moego.models.organization.v1.LimitationGroupHitView
	(*BookingLimitation_ServiceLimitation)(nil),  // 16: moego.models.organization.v1.BookingLimitation.ServiceLimitation
	(*BookingLimitation_PetSizeLimitation)(nil),  // 17: moego.models.organization.v1.BookingLimitation.PetSizeLimitation
	(*BookingLimitation_PetBreedLimitation)(nil), // 18: moego.models.organization.v1.BookingLimitation.PetBreedLimitation
	(dayofweek.DayOfWeek)(0),                     // 19: google.type.DayOfWeek
}
var file_moego_models_organization_v1_staff_availability_models_proto_depIdxs = []int32{
	1,  // 0: moego.models.organization.v1.StaffAvailability.schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	4,  // 1: moego.models.organization.v1.StaffAvailability.slot_availability_day_list:type_name -> moego.models.organization.v1.SlotAvailabilityDay
	5,  // 2: moego.models.organization.v1.StaffAvailability.time_availability_day_list:type_name -> moego.models.organization.v1.TimeAvailabilityDay
	1,  // 3: moego.models.organization.v1.StaffAvailability.time_schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	19, // 4: moego.models.organization.v1.SlotAvailabilityDay.day_of_week:type_name -> google.type.DayOfWeek
	1,  // 5: moego.models.organization.v1.SlotAvailabilityDay.schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	6,  // 6: moego.models.organization.v1.SlotAvailabilityDay.slot_daily_setting:type_name -> moego.models.organization.v1.SlotDailySetting
	8,  // 7: moego.models.organization.v1.SlotAvailabilityDay.slot_hour_setting_list:type_name -> moego.models.organization.v1.SlotHourSetting
	19, // 8: moego.models.organization.v1.TimeAvailabilityDay.day_of_week:type_name -> google.type.DayOfWeek
	1,  // 9: moego.models.organization.v1.TimeAvailabilityDay.schedule_type:type_name -> moego.models.organization.v1.ScheduleType
	7,  // 10: moego.models.organization.v1.TimeAvailabilityDay.time_daily_setting:type_name -> moego.models.organization.v1.TimeDailySetting
	9,  // 11: moego.models.organization.v1.TimeAvailabilityDay.time_hour_setting_list:type_name -> moego.models.organization.v1.TimeHourSetting
	10, // 12: moego.models.organization.v1.SlotDailySetting.limit:type_name -> moego.models.organization.v1.BookingLimitation
	11, // 13: moego.models.organization.v1.SlotDailySetting.limitation_groups:type_name -> moego.models.organization.v1.LimitationGroup
	10, // 14: moego.models.organization.v1.TimeDailySetting.limit:type_name -> moego.models.organization.v1.BookingLimitation
	11, // 15: moego.models.organization.v1.TimeDailySetting.limitation_groups:type_name -> moego.models.organization.v1.LimitationGroup
	10, // 16: moego.models.organization.v1.SlotHourSetting.limit:type_name -> moego.models.organization.v1.BookingLimitation
	11, // 17: moego.models.organization.v1.SlotHourSetting.limitation_groups:type_name -> moego.models.organization.v1.LimitationGroup
	16, // 18: moego.models.organization.v1.BookingLimitation.service_limits:type_name -> moego.models.organization.v1.BookingLimitation.ServiceLimitation
	17, // 19: moego.models.organization.v1.BookingLimitation.pet_size_limits:type_name -> moego.models.organization.v1.BookingLimitation.PetSizeLimitation
	18, // 20: moego.models.organization.v1.BookingLimitation.pet_breed_limits:type_name -> moego.models.organization.v1.BookingLimitation.PetBreedLimitation
	12, // 21: moego.models.organization.v1.LimitationGroup.service_limits:type_name -> moego.models.organization.v1.ServiceLimitation
	13, // 22: moego.models.organization.v1.LimitationGroup.pet_size_limits:type_name -> moego.models.organization.v1.PetSizeLimitation
	14, // 23: moego.models.organization.v1.LimitationGroup.pet_breed_limits:type_name -> moego.models.organization.v1.PetBreedLimitation
	12, // 24: moego.models.organization.v1.LimitationGroupHitView.service_limits:type_name -> moego.models.organization.v1.ServiceLimitation
	13, // 25: moego.models.organization.v1.LimitationGroupHitView.pet_size_limits:type_name -> moego.models.organization.v1.PetSizeLimitation
	14, // 26: moego.models.organization.v1.LimitationGroupHitView.pet_breed_limits:type_name -> moego.models.organization.v1.PetBreedLimitation
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_staff_availability_models_proto_init() }
func file_moego_models_organization_v1_staff_availability_models_proto_init() {
	if File_moego_models_organization_v1_staff_availability_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffAvailability); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlotAvailabilityDay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeAvailabilityDay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlotDailySetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeDailySetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlotHourSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeHourSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookingLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitationGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetSizeLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetBreedLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitationGroupHitView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookingLimitation_ServiceLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookingLimitation_PetSizeLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_staff_availability_models_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookingLimitation_PetBreedLimitation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_staff_availability_models_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_staff_availability_models_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_staff_availability_models_proto_depIdxs,
		EnumInfos:         file_moego_models_organization_v1_staff_availability_models_proto_enumTypes,
		MessageInfos:      file_moego_models_organization_v1_staff_availability_models_proto_msgTypes,
	}.Build()
	File_moego_models_organization_v1_staff_availability_models_proto = out.File
	file_moego_models_organization_v1_staff_availability_models_proto_rawDesc = nil
	file_moego_models_organization_v1_staff_availability_models_proto_goTypes = nil
	file_moego_models_organization_v1_staff_availability_models_proto_depIdxs = nil
}
