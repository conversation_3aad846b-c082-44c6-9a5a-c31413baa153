// @since 2025-08-25 18:46:47
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/organization/v1/service_area_service.proto

package organizationsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// search service_area request
type SearchServiceAreasRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// search locations
	Locations []*SearchServiceAreasRequest_SearchLocation `protobuf:"bytes,2,rep,name=locations,proto3" json:"locations,omitempty"`
	// candidate area ids
	CandidateAreaIds []int64 `protobuf:"varint,3,rep,packed,name=candidate_area_ids,json=candidateAreaIds,proto3" json:"candidate_area_ids,omitempty"`
}

func (x *SearchServiceAreasRequest) Reset() {
	*x = SearchServiceAreasRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_service_area_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchServiceAreasRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchServiceAreasRequest) ProtoMessage() {}

func (x *SearchServiceAreasRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_service_area_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchServiceAreasRequest.ProtoReflect.Descriptor instead.
func (*SearchServiceAreasRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_service_area_service_proto_rawDescGZIP(), []int{0}
}

func (x *SearchServiceAreasRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SearchServiceAreasRequest) GetLocations() []*SearchServiceAreasRequest_SearchLocation {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *SearchServiceAreasRequest) GetCandidateAreaIds() []int64 {
	if x != nil {
		return x.CandidateAreaIds
	}
	return nil
}

// search service_area response
type SearchServiceAreasResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// search results
	Results []*SearchServiceAreasResponse_SearchResult `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *SearchServiceAreasResponse) Reset() {
	*x = SearchServiceAreasResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_service_area_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchServiceAreasResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchServiceAreasResponse) ProtoMessage() {}

func (x *SearchServiceAreasResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_service_area_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchServiceAreasResponse.ProtoReflect.Descriptor instead.
func (*SearchServiceAreasResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_service_area_service_proto_rawDescGZIP(), []int{1}
}

func (x *SearchServiceAreasResponse) GetResults() []*SearchServiceAreasResponse_SearchResult {
	if x != nil {
		return x.Results
	}
	return nil
}

// search location
type SearchServiceAreasRequest_SearchLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// index
	Index int64 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	// latitude
	Lat *float64 `protobuf:"fixed64,2,opt,name=lat,proto3,oneof" json:"lat,omitempty"`
	// longitude
	Lng *float64 `protobuf:"fixed64,3,opt,name=lng,proto3,oneof" json:"lng,omitempty"`
	// zip code
	Zipcode *string `protobuf:"bytes,4,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
}

func (x *SearchServiceAreasRequest_SearchLocation) Reset() {
	*x = SearchServiceAreasRequest_SearchLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_service_area_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchServiceAreasRequest_SearchLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchServiceAreasRequest_SearchLocation) ProtoMessage() {}

func (x *SearchServiceAreasRequest_SearchLocation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_service_area_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchServiceAreasRequest_SearchLocation.ProtoReflect.Descriptor instead.
func (*SearchServiceAreasRequest_SearchLocation) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_service_area_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *SearchServiceAreasRequest_SearchLocation) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *SearchServiceAreasRequest_SearchLocation) GetLat() float64 {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return 0
}

func (x *SearchServiceAreasRequest_SearchLocation) GetLng() float64 {
	if x != nil && x.Lng != nil {
		return *x.Lng
	}
	return 0
}

func (x *SearchServiceAreasRequest_SearchLocation) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

// search result
type SearchServiceAreasResponse_SearchResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// index
	Index int64 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	// search results
	ServiceAreas []*v1.ServiceAreaSearchView `protobuf:"bytes,2,rep,name=service_areas,json=serviceAreas,proto3" json:"service_areas,omitempty"`
}

func (x *SearchServiceAreasResponse_SearchResult) Reset() {
	*x = SearchServiceAreasResponse_SearchResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_service_area_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchServiceAreasResponse_SearchResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchServiceAreasResponse_SearchResult) ProtoMessage() {}

func (x *SearchServiceAreasResponse_SearchResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_service_area_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchServiceAreasResponse_SearchResult.ProtoReflect.Descriptor instead.
func (*SearchServiceAreasResponse_SearchResult) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_service_area_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *SearchServiceAreasResponse_SearchResult) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *SearchServiceAreasResponse_SearchResult) GetServiceAreas() []*v1.ServiceAreaSearchView {
	if x != nil {
		return x.ServiceAreas
	}
	return nil
}

var File_moego_service_organization_v1_service_area_service_proto protoreflect.FileDescriptor

var file_moego_service_organization_v1_service_area_service_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x61, 0x72, 0x65, 0x61, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99, 0x03, 0x0a, 0x19, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x72, 0x65, 0x61,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x78, 0x0a, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x72, 0x65, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x11, 0xfa, 0x42,
	0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3f, 0x0a, 0x12, 0x63, 0x61,
	0x6e, 0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x00,
	0x10, 0xe8, 0x07, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x63, 0x61, 0x6e, 0x64, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x72, 0x65, 0x61, 0x49, 0x64, 0x73, 0x1a, 0x98, 0x01, 0x0a, 0x0e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x15, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x48, 0x00, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03, 0x6c,
	0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x48, 0x01, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x88,
	0x01, 0x01, 0x12, 0x26, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x02, 0x52, 0x07,
	0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6c,
	0x61, 0x74, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6c, 0x6e, 0x67, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x7a,
	0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xfe, 0x01, 0x0a, 0x1a, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x72, 0x65, 0x61, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x60, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x72, 0x65, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x7e, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x58, 0x0a,
	0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x72, 0x65, 0x61, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x41, 0x72, 0x65, 0x61, 0x73, 0x32, 0xa0, 0x01, 0x0a, 0x12, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x72, 0x65, 0x61, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x89,
	0x01, 0x0a, 0x12, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x72, 0x65, 0x61, 0x73, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x72, 0x65, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x72, 0x65,
	0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8f, 0x01, 0x0a, 0x25, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x64, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_organization_v1_service_area_service_proto_rawDescOnce sync.Once
	file_moego_service_organization_v1_service_area_service_proto_rawDescData = file_moego_service_organization_v1_service_area_service_proto_rawDesc
)

func file_moego_service_organization_v1_service_area_service_proto_rawDescGZIP() []byte {
	file_moego_service_organization_v1_service_area_service_proto_rawDescOnce.Do(func() {
		file_moego_service_organization_v1_service_area_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_organization_v1_service_area_service_proto_rawDescData)
	})
	return file_moego_service_organization_v1_service_area_service_proto_rawDescData
}

var file_moego_service_organization_v1_service_area_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_service_organization_v1_service_area_service_proto_goTypes = []interface{}{
	(*SearchServiceAreasRequest)(nil),                // 0: moego.service.organization.v1.SearchServiceAreasRequest
	(*SearchServiceAreasResponse)(nil),               // 1: moego.service.organization.v1.SearchServiceAreasResponse
	(*SearchServiceAreasRequest_SearchLocation)(nil), // 2: moego.service.organization.v1.SearchServiceAreasRequest.SearchLocation
	(*SearchServiceAreasResponse_SearchResult)(nil),  // 3: moego.service.organization.v1.SearchServiceAreasResponse.SearchResult
	(*v1.ServiceAreaSearchView)(nil),                 // 4: moego.models.organization.v1.ServiceAreaSearchView
}
var file_moego_service_organization_v1_service_area_service_proto_depIdxs = []int32{
	2, // 0: moego.service.organization.v1.SearchServiceAreasRequest.locations:type_name -> moego.service.organization.v1.SearchServiceAreasRequest.SearchLocation
	3, // 1: moego.service.organization.v1.SearchServiceAreasResponse.results:type_name -> moego.service.organization.v1.SearchServiceAreasResponse.SearchResult
	4, // 2: moego.service.organization.v1.SearchServiceAreasResponse.SearchResult.service_areas:type_name -> moego.models.organization.v1.ServiceAreaSearchView
	0, // 3: moego.service.organization.v1.ServiceAreaService.SearchServiceAreas:input_type -> moego.service.organization.v1.SearchServiceAreasRequest
	1, // 4: moego.service.organization.v1.ServiceAreaService.SearchServiceAreas:output_type -> moego.service.organization.v1.SearchServiceAreasResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_service_organization_v1_service_area_service_proto_init() }
func file_moego_service_organization_v1_service_area_service_proto_init() {
	if File_moego_service_organization_v1_service_area_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_organization_v1_service_area_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchServiceAreasRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_service_area_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchServiceAreasResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_service_area_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchServiceAreasRequest_SearchLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_service_area_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchServiceAreasResponse_SearchResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_organization_v1_service_area_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_organization_v1_service_area_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_organization_v1_service_area_service_proto_goTypes,
		DependencyIndexes: file_moego_service_organization_v1_service_area_service_proto_depIdxs,
		MessageInfos:      file_moego_service_organization_v1_service_area_service_proto_msgTypes,
	}.Build()
	File_moego_service_organization_v1_service_area_service_proto = out.File
	file_moego_service_organization_v1_service_area_service_proto_rawDesc = nil
	file_moego_service_organization_v1_service_area_service_proto_goTypes = nil
	file_moego_service_organization_v1_service_area_service_proto_depIdxs = nil
}
