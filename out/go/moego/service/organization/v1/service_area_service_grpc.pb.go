// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/organization/v1/service_area_service.proto

package organizationsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ServiceAreaServiceClient is the client API for ServiceAreaService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceAreaServiceClient interface {
	// search service_area, refer to com.moego.server.business.api.IBusinessServiceAreaService.getAreasByLocation
	SearchServiceAreas(ctx context.Context, in *SearchServiceAreasRequest, opts ...grpc.CallOption) (*SearchServiceAreasResponse, error)
}

type serviceAreaServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceAreaServiceClient(cc grpc.ClientConnInterface) ServiceAreaServiceClient {
	return &serviceAreaServiceClient{cc}
}

func (c *serviceAreaServiceClient) SearchServiceAreas(ctx context.Context, in *SearchServiceAreasRequest, opts ...grpc.CallOption) (*SearchServiceAreasResponse, error) {
	out := new(SearchServiceAreasResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.ServiceAreaService/SearchServiceAreas", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceAreaServiceServer is the server API for ServiceAreaService service.
// All implementations must embed UnimplementedServiceAreaServiceServer
// for forward compatibility
type ServiceAreaServiceServer interface {
	// search service_area, refer to com.moego.server.business.api.IBusinessServiceAreaService.getAreasByLocation
	SearchServiceAreas(context.Context, *SearchServiceAreasRequest) (*SearchServiceAreasResponse, error)
	mustEmbedUnimplementedServiceAreaServiceServer()
}

// UnimplementedServiceAreaServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceAreaServiceServer struct {
}

func (UnimplementedServiceAreaServiceServer) SearchServiceAreas(context.Context, *SearchServiceAreasRequest) (*SearchServiceAreasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchServiceAreas not implemented")
}
func (UnimplementedServiceAreaServiceServer) mustEmbedUnimplementedServiceAreaServiceServer() {}

// UnsafeServiceAreaServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceAreaServiceServer will
// result in compilation errors.
type UnsafeServiceAreaServiceServer interface {
	mustEmbedUnimplementedServiceAreaServiceServer()
}

func RegisterServiceAreaServiceServer(s grpc.ServiceRegistrar, srv ServiceAreaServiceServer) {
	s.RegisterService(&ServiceAreaService_ServiceDesc, srv)
}

func _ServiceAreaService_SearchServiceAreas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchServiceAreasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceAreaServiceServer).SearchServiceAreas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.ServiceAreaService/SearchServiceAreas",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceAreaServiceServer).SearchServiceAreas(ctx, req.(*SearchServiceAreasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServiceAreaService_ServiceDesc is the grpc.ServiceDesc for ServiceAreaService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServiceAreaService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.organization.v1.ServiceAreaService",
	HandlerType: (*ServiceAreaServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SearchServiceAreas",
			Handler:    _ServiceAreaService_SearchServiceAreas_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/organization/v1/service_area_service.proto",
}
