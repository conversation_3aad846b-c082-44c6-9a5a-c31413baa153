import { Checkbox, DatePicker, Heading, LegacySelect as Select, Text } from '@moego/ui';
import { useSelector, useStore } from 'amos';
import type { Dayjs } from 'dayjs';
import { uniqBy } from 'lodash';
import { flattenDeep } from 'lodash/fp';
import React, { useEffect, useMemo, type ReactElement, type ReactNode } from 'react';
import { useSetState } from 'react-use';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { type PlaygroupsPrintCard } from '../../store/playgroups/playgroups.actions';
import { type ActivityPrintCardInfo } from '../../store/printCard/activityCard/activityCard.actions';
import { type PrintCardInfo } from '../../store/printCard/appointmentCard/appointmentCard.actions';
import { type ApptListPrintCardInfo } from '../../store/printCard/appointmentList/appointmentList.actions';
import { type StayPrintCardInfo } from '../../store/printCard/stayCard/stayCard.actions';
import { type EnumValues } from '../../store/utils/createEnum';
import { computeUnits } from '../../utils/calculator';
import { CareTypeServicesSelector } from '../CareTypeServicesSelector/CareTypeServicesSelector';
import { Condition } from '../Condition';
import { PrintModal, type PrintModalProps } from '../PrintModal/PrintModal';
import {
  PrintCardType,
  filterApptCardList,
  type MultiTypePrintModalState,
  type Staff,
} from './MultiTypePrintModal.options';
import { MultiTypePrintModalGlobalStyle } from './MultiTypePrintModal.style';
import { ActivityCardContent } from './components/ActivityCard/ActivityCardContent';
import { ApptCardContent } from './components/ApptCard/ApptCardContent';
import { AppointmentListCardContent } from './components/ApptListCard/AppointmentListCardContent';
import { BoardingArrivalCard } from './components/BoardingArrivalCard/BoardingArrivalCard';
import { BoardingDepartureCard } from './components/BoardingDepartureCard/BoardingDepartureCard';
import { PlaygroupCard } from './components/PlaygroupCard/PlaygroupCard';
import { Settings } from './components/Settings';
import { StayCardContent } from './components/StayCard/StayCardContent';
import { type BoardingPrintCardContent } from './components/common/BoardingCard/BoardingCard.types';
import { useDate } from './hooks/useDate';
import { useFilterAppointmentListData } from './hooks/useFilterAppointmentListData';
import { useGetData } from './hooks/useGetData';
import { useStaffList } from './hooks/useStaffList';
import { selectSceneCareTypeAsOptions } from '../../store/careType/careType.selectors';
import { Scene } from '../../store/service/scene.enum';
import { useGetDefaultCareType } from './hooks/useGetDefaultCareType';
import { CollarLabelCardContent } from './components/CollarLabelPrintCard/CollarLabelCardContent';
import { reportData } from '../../utils/tracker';
import { ReportActionName } from '../../utils/reportType';
import { apptListSettingsBox } from '../../store/printCard/appointmentList/appointmentList.boxes';
import { boardingArrivalSettingsBox } from '../../store/printCard/boardingArrival/boardingArrival.boxes';
import { boardingDepartureSettingsBox } from '../../store/printCard/boardingDeparture/boardingDeparture.boxes';
import { selectStayPrintCardSettings } from '../../store/printCard/stayCard/stayCard.selectors';
import { selectActivityPrintCardSettings } from '../../store/printCard/activityCard/activityCard.selectors';
import { selectApptPrintCardSettings } from '../../store/printCard/appointmentCard/appointmentCard.selectors';
import { selectPlaygroupPrintCardSettings } from '../../store/playgroups/playgroups.selectors';
import { selectCollarListSettings } from '../../store/printCard/collarCard/collarCard.selectors';
import { T_SECOND } from 'monofile-utilities/lib/consts';

type BaseProps = {
  from: 'homepage' | 'calendar' | 'apptDrawer' | 'playgroup' | 'tasks';
  cardTypeOptions?: EnumValues<typeof PrintCardType>[];
} & Pick<PrintModalProps, 'isOpen' | 'onClose'>;

export function MultiTypePrintModal(
  props: BaseProps & {
    groomingId?: number;
  },
): ReactElement;

export function MultiTypePrintModal(
  props: BaseProps & {
    date: Dayjs;
    staffList?: Staff[];
    serviceItemTypes?: number[];
    /**
     * 当打开 print modal 时，默认展示的 card 类型
     */
    defaultCardType?: EnumValues<typeof PrintCardType>;
    title?: string;
  },
): ReactElement;

export function MultiTypePrintModal(
  props: BaseProps & {
    groomingId?: number;
    date?: Dayjs;
    staffList?: Staff[];
    serviceItemTypes?: number[];
    defaultCardType?: EnumValues<typeof PrintCardType>;
    title?: string;
  },
) {
  const {
    isOpen,
    onClose,
    cardTypeOptions = PrintCardType.values,
    groomingId,
    date: dateProp,
    staffList: staffListProp,
    serviceItemTypes,
    defaultCardType = cardTypeOptions[0],
    title,
    from,
  } = props;
  const store = useStore();
  const [business, careTypeOptions] = useSelector(
    selectCurrentBusiness(),
    selectSceneCareTypeAsOptions(Scene.PrintStayOrActivityCard),
  );

  const getDefaultCareType = useGetDefaultCareType();

  if (groomingId && (dateProp || staffListProp)) {
    throw new Error('GroomingId and (Date || StaffList) cannot be set at the same time.');
  }

  const [date, setDate] = useDate(dateProp);
  const { selectedStaff, setSelectedStaff, staffList } = useStaffList(staffListProp);
  const [state, setState] = useSetState<MultiTypePrintModalState>({
    selectedCardType: defaultCardType,
    isPetsCheckedInOnly: false,
    careType: getDefaultCareType(serviceItemTypes),
    serviceFilters: [],
  });
  const { selectedCardType, isPetsCheckedInOnly, careType, serviceFilters } = state;

  const {
    loadingContent,
    loadingSettings,
    data: originalData,
    loading,
  } = useGetData({
    isOpen,
    groomingId,
    date,
    modalState: state,
  });
  const filterData = useFilterAppointmentListData();

  const data = useMemo(() => {
    if (selectedCardType === PrintCardType.ApptList) {
      return filterData(originalData as ApptListPrintCardInfo[]);
    }
    return originalData;
  }, [selectedCardType, originalData, filterData]);

  useEffect(() => {
    if (!isOpen) return;
    const time = Date.now();
    reportData(ReportActionName.printCardView, { from, defaultCardType });
    return () => {
      reportData(ReportActionName.printCardLeave, {
        from,
        defaultCardType,
        duration: (Date.now() - time) / T_SECOND,
      });
    };
  }, [isOpen]);

  useEffect(() => {
    if (isOpen) {
      setState({
        selectedCardType: defaultCardType,
        careType: getDefaultCareType(serviceItemTypes),
        serviceFilters: [],
      });
    }
  }, [isOpen]);

  const handleOnClose = () => {
    onClose?.();
    // 重置为外部值。
    if (dateProp) {
      setDate(dateProp);
    }
  };

  const activityData = (data as ActivityPrintCardInfo[])[0] ?? {};
  const showActivityCardNoData =
    selectedCardType === PrintCardType.Activity &&
    data &&
    (activityData?.feedingInstructions ?? []).length === 0 &&
    (activityData?.medicationInstructions ?? []).length === 0 &&
    (activityData?.addOns ?? []).length === 0;

  const showNoData = showActivityCardNoData || data.length === 0;
  const showCheckedInOnly = (
    [
      PrintCardType.BoardingDeparture,
      PrintCardType.BoardingArrival,
      PrintCardType.ApptList,
      PrintCardType.Stay,
      PrintCardType.Activity,
      PrintCardType.Playgroup,
      PrintCardType.CollarLabel,
    ] as number[]
  ).includes(selectedCardType);
  const showTotalPets = ([PrintCardType.Activity, PrintCardType.Stay, PrintCardType.Appt] as number[]).includes(
    selectedCardType,
  );

  const isApptListCardType = selectedCardType === PrintCardType.ApptList;

  const handleAfterPrint = () => {
    function getPrintOptions() {
      switch (selectedCardType) {
        case PrintCardType.ApptList:
          return store.select(apptListSettingsBox);
        case PrintCardType.BoardingDeparture:
          return store.select(boardingDepartureSettingsBox);
        case PrintCardType.BoardingArrival:
          return store.select(boardingArrivalSettingsBox);
        case PrintCardType.Stay:
          return store.select(selectStayPrintCardSettings);
        case PrintCardType.Activity:
          return store.select(selectActivityPrintCardSettings);
        case PrintCardType.Appt:
          return store.select(selectApptPrintCardSettings);
        case PrintCardType.Playgroup:
          return store.select(selectPlaygroupPrintCardSettings);
        case PrintCardType.CollarLabel:
          return store.select(selectCollarListSettings);
        default:
          return {};
      }
    }
    reportData(ReportActionName.printCardPrintClick, {
      cardType: PrintCardType.mapLabels[selectedCardType],
      value: getPrintOptions(),
    });
  };

  const renderSettings = () => {
    return (
      <Settings
        cardTypeOptions={cardTypeOptions}
        isLoading={loadingSettings}
        cardType={selectedCardType}
        onChange={(value) => setState({ selectedCardType: value })}
      />
    );
  };

  const renderContent = () => {
    switch (selectedCardType) {
      case PrintCardType.ApptList:
        return <AppointmentListCardContent data={data as ApptListPrintCardInfo[]} date={business.formatDate(date)} />;
      case PrintCardType.BoardingDeparture:
        return <BoardingDepartureCard data={data as BoardingPrintCardContent[]} date={business.formatDate(date)} />;
      case PrintCardType.BoardingArrival:
        return <BoardingArrivalCard data={data as BoardingPrintCardContent[]} date={business.formatDate(date)} />;
      case PrintCardType.Stay:
        return <StayCardContent data={data as StayPrintCardInfo[]} />;
      case PrintCardType.Activity:
        return <ActivityCardContent data={data as ActivityPrintCardInfo[]} date={business.formatDate(date)} />;
      case PrintCardType.Appt: {
        const filteredApptCardList = filterApptCardList(data as PrintCardInfo[], selectedStaff).sort(
          (a, b) => a.appointment.appointmentStartTime - b.appointment.appointmentStartTime,
        );
        return <ApptCardContent data={filteredApptCardList} />;
      }
      case PrintCardType.Playgroup:
        return <PlaygroupCard data={data as PlaygroupsPrintCard[]} date={business.formatDate(date)} />;
      case PrintCardType.CollarLabel:
        return <CollarLabelCardContent data={data as ApptListPrintCardInfo[]} date={business.formatDate(date)} />;
      default:
        return null;
    }
  };

  const getPetCountString = () => {
    let petCount = 0;
    if (selectedCardType === PrintCardType.Appt) {
      const filterList = filterApptCardList(data as PrintCardInfo[], selectedStaff);
      petCount = uniqBy(filterList, 'petId').length;
    }
    if (selectedCardType === PrintCardType.Stay) {
      petCount = uniqBy(data as StayPrintCardInfo[], 'petId').length;
    }
    if (selectedCardType === PrintCardType.Activity) {
      // 在数组里解构 null 会报错
      const typedData = (data as ActivityPrintCardInfo[]).map((item) => [
        ...(item.feedingInstructions ?? []),
        ...(item.medicationInstructions ?? []),
        ...(item.addOns ?? []),
      ]);
      petCount = uniqBy(flattenDeep(typedData), 'petId').length;
    }
    return computeUnits(petCount, 'pet').join(' ');
  };

  const getDatePickerLabel = () => {
    switch (selectedCardType) {
      case PrintCardType.BoardingDeparture:
        return 'End date:';
      case PrintCardType.BoardingArrival:
        return 'Start date:';
      case PrintCardType.Stay:
        return 'Check in date:';
      default:
        return 'Date:';
    }
  };

  const getNoDataDescription = () => {
    switch (selectedCardType) {
      case PrintCardType.BoardingDeparture:
        return 'No boarding pets scheduled for departure on this date.';
      case PrintCardType.BoardingArrival:
        return 'No boarding pets scheduled for arrival on this date.';
      case PrintCardType.Activity:
        return 'No tasks scheduled for this date';
      case PrintCardType.Playgroup:
        return 'No playgroups available';
      default:
        return 'No appointments scheduled for this date';
    }
  };

  const renderFooter = () => {
    let children: ReactNode = null;
    const isStayAndActivity = ([PrintCardType.Stay, PrintCardType.Activity] as number[]).includes(selectedCardType);
    if (!groomingId) {
      const datePickerLabel = getDatePickerLabel();
      children = (
        <div className="moe-flex moe-items-center">
          <DatePicker
            onChange={(nextDate) => {
              if (!nextDate) return;
              setDate(nextDate);
              reportData(ReportActionName.printCardDateClick, { cardType: selectedCardType });
            }}
            value={date}
            format={business.dateFormat}
            label={datePickerLabel}
            classNames={{
              base: 'moe-flex-row moe-inline-flex',
              label: isStayAndActivity ? 'moe-mr-[8px]' : 'moe-mr-s',
              inputWrapper: isStayAndActivity ? 'moe-w-[148px]' : 'moe-w-[150px]',
            }}
            isDisabled={loading}
          />
          <Condition if={isStayAndActivity}>
            <Select
              options={careTypeOptions}
              value={careType}
              onChange={(value) => setState({ careType: value })}
              label="Care type:"
              classNames={{
                formItemWrapper: 'moe-flex-row moe-inline-flex moe-ml-s',
                formItemLabel: 'moe-mr-[8px]',
                control: 'moe-w-[188px]',
              }}
            />
          </Condition>
          <Condition if={isApptListCardType}>
            <CareTypeServicesSelector
              className="moe-ml-s moe-w-[188px]"
              value={serviceFilters}
              onChange={(filters) => setState({ serviceFilters: filters })}
            />
          </Condition>
          <Condition if={selectedCardType === PrintCardType.Appt}>
            <Select
              value={selectedStaff}
              onChange={(id) => {
                if (id) setSelectedStaff(id);
              }}
              options={staffList.map((staff) => ({ label: staff.name, value: staff.id }))}
              label="Selected staff:"
              classNames={{
                formItemWrapper: 'moe-flex-row moe-inline-flex moe-ml-s',
                formItemLabel: 'moe-mr-s',
                control: 'moe-w-[150px]',
              }}
              isDisabled={loading}
            />
          </Condition>
          <Condition if={showCheckedInOnly}>
            <Checkbox
              isSelected={isPetsCheckedInOnly}
              onChange={(isChecked) => {
                setState({ isPetsCheckedInOnly: isChecked });
                reportData(ReportActionName.printCardCheckInPetsOnlyClick, {
                  cardType: selectedCardType,
                  isChecked,
                });
              }}
              classNames={{ base: 'moe-inline-flex moe-ml-s' }}
            >
              Checked in pets only
            </Checkbox>
          </Condition>
        </div>
      );
    }

    return (
      <div className="moe-flex">
        <Condition if={children}>
          <div className="moe-mr-s">{children}</div>
        </Condition>
        <Condition if={showTotalPets}>
          <div className="moe-flex moe-items-center">
            <Heading size="6">Total:</Heading>
            <Text variant="small" className="moe-ml-xxs">
              {getPetCountString()}
            </Text>
          </div>
        </Condition>
      </div>
    );
  };

  return (
    <>
      <MultiTypePrintModalGlobalStyle />
      <PrintModal
        title={title || 'Print'}
        isOpen={isOpen}
        content={renderContent()}
        isLoadingContent={loadingContent}
        loadingContentClassName="moe-w-[700px]"
        noDataClassName="moe-w-[700px]"
        contentClassName="moe-w-[700px]"
        contentBodyClassName="moe-w-[612px]"
        containerClassName="moe-h-[90vh]"
        sideContent={renderSettings()}
        isDisabled={loading || data.length === 0}
        isNoData={showNoData}
        onClose={handleOnClose}
        footerLeftSide={renderFooter()}
        noDataDescription={getNoDataDescription()}
        printProps={{
          onAfterPrint: handleAfterPrint,
        }}
      />
    </>
  );
}
