import { Condition, Tag, Tooltip, type TooltipProps, Typography } from '@moego/ui';
import { type SideOption } from '@moego/ui/dist/esm/components/utils/constants';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { petCodeCommentMapBox, petCodeMapBox } from '../../store/pet/petCode.boxes';

interface PetCodeGroupTooltipProps {
  petId?: number;
  codesIdList: number[];
  children: TooltipProps['content'];
  side?: SideOption;
  onOpenChange?: (isOpen: boolean) => void;
}

export const PetCodeGroupTooltip = memo((props: PetCodeGroupTooltipProps) => {
  const { codesIdList, children, petId, side = 'right', onOpenChange } = props;
  const [petCodeMap, petCodeCommentMap] = useSelector(petCodeMapBox, petCodeCommentMapBox);
  const data = useMemo(() => {
    return codesIdList.map((codeId) => {
      return {
        ...petCodeMap.getItem(codeId)?.toObject(),
        comment: petId ? petCodeCommentMap.getItem(`${petId}+${codeId}`)?.comment : '',
      };
    });
  }, [codesIdList, petCodeCommentMap, petCodeMap, petId]);
  return (
    <Tooltip
      side={side}
      backgroundTheme="light"
      onOpenChange={onOpenChange}
      classNames={{
        container: 'moe-w-[320px] !moe-max-w-[320px]',
      }}
      content={
        <div style={{ maxHeight: '80vh', overflowY: 'auto' }}>
          <Typography.Heading size="5">Pet code</Typography.Heading>
          <div className="moe-flex moe-flex-wrap moe-max-h-[300px] moe-overflow-y-auto">
            {data.map((item) => {
              return (
                <div key={item.id} className="moe-mt-4 moe-mr-4 moe-inline-block">
                  <Tag fillColor={item.color} borderColor={item.color} color="white" label={item.codeNumber} />
                  <span className="moe-ml-2 moe-text-base moe-align-text-top">{item.description}</span>
                  <Condition if={!!item.comment && item.comment.trim().length > 0}>
                    <div className="moe-mt-2 moe-px-2 moe-py-[6px] moe-rounded-1 moe-bg-neutral-sunken-0">
                      {item.comment}
                    </div>
                  </Condition>
                </div>
              );
            })}
          </div>
        </div>
      }
    >
      {children}
    </Tooltip>
  );
});
