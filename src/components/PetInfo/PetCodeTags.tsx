import { MoreOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import React, { useMemo, memo, forwardRef } from 'react';
import { type PetCodeRecord } from '../../store/pet/petCode.boxes';
import { type PickProps } from '../../store/utils/RecordMap';
import { TagSmall } from '../Tag/TagSmall';
import { PetCodeGroupTooltip } from './PetCodeGroupTooltip';
import { type ID } from '../../types/common';
import { ReportActionName } from '../../utils/reportType';
import { reportData } from '../../utils/tracker';
import { cn } from '@moego/ui';

interface CodeItem extends Readonly<PickProps<PetCodeRecord, 'color' | 'abbreviation' | 'description'>> {
  id: ID;
  codeNumber?: PetCodeRecord['codeNumber'];
}

export interface PetCodeTagsProps {
  petId?: number;
  className?: string;
  codes?: CodeItem[];
  /** 最多展示几个code */
  max?: number;
  /** 是否展示code的description */
  tip?: boolean;
  classNames?: {
    tipContainer?: string;
  };
}

function CodeNumberItem(props: { code: CodeItem }) {
  const { code } = props;
  return <TagSmall variant="filled" color={code.color} label={code.codeNumber || code.abbreviation} />;
}

const PetCodeTagsContent = memo<{
  codeList: CodeItem[];
  remainCodeList: CodeItem[];
  classNameHasSetGap?: boolean;
  classNameHasWarp: boolean;
  className?: string;
}>(
  forwardRef(({ codeList, remainCodeList, classNameHasSetGap, classNameHasWarp, className }, ref) => (
    <div
      className={classNames(
        'moe-flex moe-items-center',
        classNameHasSetGap ? '' : 'moe-gap-[4px]',
        classNameHasWarp ? '' : 'moe-flex-wrap',
        className,
      )}
      aria-label="pet codes"
      ref={ref as React.LegacyRef<HTMLDivElement>}
    >
      {codeList.map((code) => {
        return <CodeNumberItem key={code.codeNumber || code.abbreviation} code={code} />;
      })}
      {remainCodeList.length ? <MoreOutlined className="moe-block moe-cursor-pointer" /> : null}
    </div>
  )),
);

export function PetCodeTags(props: PetCodeTagsProps) {
  const { className, classNames, codes, max = codes?.length ?? 0, tip, petId } = props;
  const { codeList, remainCodeList, codesIdList } = useMemo(() => {
    const list = Array.isArray(codes) ? codes : [];
    const codeList = list.slice(0, max);
    const remainCodeList = list.slice(max);
    return {
      codeList,
      remainCodeList,
      codesIdList: codeList.concat(remainCodeList).map((code) => +code.id),
    };
  }, [codes, max]);

  const classNameHasSetGap = className?.includes('moe-gap-');
  const classNameHasWarp = className?.includes('warp') || max === 1;

  if (codeList.length === 0) {
    return null;
  }

  const result = (
    <PetCodeTagsContent
      codeList={codeList}
      remainCodeList={remainCodeList}
      classNameHasSetGap={classNameHasSetGap}
      classNameHasWarp={classNameHasWarp}
      className={className}
    />
  );

  return tip ? (
    <PetCodeGroupTooltip
      side="bottom"
      codesIdList={codesIdList}
      petId={petId}
      onOpenChange={(isOpen) => {
        if (!isOpen) return;
        reportData(ReportActionName.petInfoComponentCodesPreview);
      }}
    >
      <div className={cn('moe-cursor-pointer', classNames?.tipContainer)}>{result}</div>
    </PetCodeGroupTooltip>
  ) : (
    result
  );
}
