import { useSelector } from 'amos';
import React, { memo } from 'react';
import { useHistory } from 'react-router';
import { PATH_CUSTOMER_PETS } from '../../router/paths';
import { petMapBox } from '../../store/pet/pet.boxes';
import { selectPetIncidentReportDetailList } from '../../store/pet/petIncident.selectors';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { PurePetIncident } from './components/PurePetIncident';
import { ReportActionName } from '../../utils/reportType';
import { reportData } from '../../utils/tracker';

interface PetIncidentProps {
  petId: number;
}

export const PetIncident = memo((props: PetIncidentProps) => {
  const { petId } = props;
  const history = useHistory();
  const [petInfo, petIncidentReportDetailList] = useSelector(
    petMapBox.mustGetItem(petId),
    selectPetIncidentReportDetailList(petId),
  );

  const go2PetIncident = useLatestCallback(async () => {
    reportData(ReportActionName.petInfoComponentIncidentsViewAll);
    return history.push(
      PATH_CUSTOMER_PETS.queried(
        { anchorPoint: 'petIncident' },
        { customerId: petInfo.customerId, petId: petInfo.petId },
      ),
    );
  });

  return (
    <PurePetIncident
      petIncidentReportDetailList={petIncidentReportDetailList}
      go2PetIncident={go2PetIncident}
      onOpenChange={(isOpen) => {
        if (!isOpen) return;
        reportData(ReportActionName.petInfoComponentIncidentPreview);
      }}
    />
  );
});
