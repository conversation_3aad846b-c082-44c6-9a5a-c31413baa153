import { But<PERSON>, <PERSON><PERSON><PERSON> } from '@moego/ui';
import React from 'react';
import { type PetNoteRecord } from '../../store/pet/petNote.boxes';
import { ReportActionName } from '../../utils/reportType';
import { Condition } from '../Condition';
import { IconModernTextNum } from '../Icon/IconModernTextNum';
import { PureNotesContent } from './components/PureNoteContent';
import { useClientPet } from './hooks/useClientPets';
import { reportData } from '../../utils/tracker';

export interface PetNotesProps {
  petId: number;
  notes?: PetNoteRecord[];
  disabled?: boolean;
  go2PetNotes?: (petId: string) => void;
}

export function PetNotes(props: PetNotesProps) {
  const { petId, disabled, notes = [], go2PetNotes } = props;
  const { petHasDeleted } = useClientPet(petId);
  const notesLength = notes.length;

  return notesLength ? (
    <Tooltip
      isDisabled={disabled}
      backgroundTheme="light"
      content={
        <PureNotesContent notes={notes} max={3} type="pet">
          <Condition if={!petHasDeleted && go2PetNotes}>
            <div className="moe-flex moe-items-center moe-gap-x-s moe-justify-start">
              <Condition if={notesLength > 3}>
                <Button
                  variant="tertiary"
                  data-action={ReportActionName.CalendarPetNoteViewAll}
                  onPress={() => {
                    go2PetNotes?.(petId.toString());
                    reportData(ReportActionName.petInfoComponentNotesViewAll);
                  }}
                  size="s"
                  align="start"
                >
                  {`View all (${notes.length})`}
                </Button>
              </Condition>
              <Button
                variant="tertiary"
                data-action={ReportActionName.CalendarPetNoteViewEdit}
                onPress={() => {
                  go2PetNotes?.(petId.toString());
                  reportData(ReportActionName.petInfoComponentNotesViewEdit);
                }}
                size="s"
                align="start"
              >
                Edit
              </Button>
            </div>
          </Condition>
        </PureNotesContent>
      }
    >
      <IconModernTextNum count={notesLength} />
    </Tooltip>
  ) : null;
}
