import { MinorIncidentOutlined } from '@moego/icons-react';
import { But<PERSON>, Heading, Text, Tooltip } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React from 'react';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { type BusinessPetIncidentReportModel } from '@moego/api-web/moego/models/business_customer/v1/business_pet_incident_report_models';

interface PurePetIncidentProps {
  petIncidentReportDetailList: (Pick<
    BusinessPetIncidentReportModel,
    'id' | 'incidentTime' | 'incidentTypeId' | 'description'
  > & {
    incidentTypeName: string;
  })[];
  max?: number;
  go2PetIncident: () => void;
  onOpenChange?: (isOpen: boolean) => void;
}

export const PurePetIncident = (props: PurePetIncidentProps) => {
  const { petIncidentReportDetailList, max = 2, go2PetIncident, onOpenChange } = props;
  const [business] = useSelector(selectCurrentBusiness);

  const latestPetIncidentReport = petIncidentReportDetailList.slice(0, max);

  if (!petIncidentReportDetailList.length) {
    return null;
  }

  return (
    <Tooltip
      onOpenChange={onOpenChange}
      backgroundTheme="light"
      classNames={{
        container: 'moe-w-[320px]',
      }}
      content={
        <div className="moe-flex moe-flex-col moe-items-start moe-gap-y-[10px]">
          <Heading size="5" className="moe-text-primary">
            Incidents
          </Heading>

          {latestPetIncidentReport.map((item, index) => {
            return (
              <div key={index}>
                <Text variant="small" className="moe-text-primary  moe-font-bold">
                  {business.formatDateTime(dayjs(item.incidentTime))}
                </Text>
                <Text variant="small" className="moe-text-primary moe-line-clamp-1 moe-font-bold moe-mb-xxs">
                  {item.incidentTypeName}
                </Text>
                <Text variant="small" className="moe-text-primary moe-line-clamp-3">
                  {item.description}
                </Text>
              </div>
            );
          })}
          <Button
            variant="tertiary-legacy"
            onPress={go2PetIncident}
            size="s"
            align="start"
            className="moe-min-w-[45px]"
          >
            View all
          </Button>
        </div>
      }
    >
      <MinorIncidentOutlined color="#F3413B" className="moe-cursor-pointer" />
    </Tooltip>
  );
};
