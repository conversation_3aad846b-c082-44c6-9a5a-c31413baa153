import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Checkbox, Select, Tabs, Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { ApptTestIds } from '../../../../config/testIds/apptDrawer';
import { serviceMapBox, type MixedService } from '../../../../store/service/service.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { useBool } from '../../../../utils/hooks/useBool';
import { Condition } from '../../../Condition';
import { useApplicablePetServices } from '../../hooks/useApplicablePetServices';
import { type ServicePriceDurationInfo } from '../../hooks/useServiceInfo';
import { type ServiceEntry } from '../../types/serviceEntry';
import { PickServiceItem } from './PickServiceItem';
import { ReportActionName } from '../../../../utils/reportType';
import { reportData } from '../../../../utils/tracker';

interface ServiceOptionItem {
  label: React.ReactNode;
  name: string;
  value: number;
  isDisabled?: boolean;
}

interface PickServiceProps {
  petId: number;
  clientId: number;
  serviceItemType: ServiceItemType;
  serviceType: ServiceType;
  serviceId?: number;
  info?: ServiceEntry;
  serviceIds?: number[];
  tabOptions?: { disabled: boolean; label: string; value: ServiceType }[];
  disabledTabs?: ServiceType[];
  disabledServices?: number[];
  applicable?: boolean;
  label?: string;
  isRequired?: boolean;
  isDisabled?: boolean;
  onChange?: (serviceId: number, extraServiceInf: ServicePriceDurationInfo) => void;
}
export const PickService = memo((props: PickServiceProps) => {
  const {
    label: labelProps,
    petId,
    applicable = true,
    info,
    serviceId,
    serviceIds,
    serviceItemType = ServiceItemType.BOARDING,
    serviceType,
    disabledServices,
    disabledTabs,
    isRequired = true,
    isDisabled,
    onChange,
  } = props;

  const [serviceMap] = useSelector(serviceMapBox);
  const isApplicable = useBool(applicable);
  const [tab, setTab] = useState(serviceType);
  const { applicableServices, applicableAddons, loading, getServiceInfo, lastActiveAddonList, lastActiveServiceList } =
    useApplicablePetServices({
      petIds: [String(petId)],
      staffId: info?.staffId,
      onlyAvailable: isApplicable.value,
      selectedServiceIds: serviceIds?.map((s) => String(s)) || [],
      serviceItemType,
      serviceType: tab,
    });

  const getOptions = useCallback(
    (list: MixedService[]): ServiceOptionItem[] => {
      return list.map((item) => {
        const { name } = serviceMap.mustGetItem(item.serviceId);
        const disabled = disabledServices?.includes(item.serviceId);
        const label = <PickServiceItem info={info} isDisabled={disabled} value={item.serviceId} petId={petId} />;
        return {
          label,
          name,
          textValue: name,
          value: item.serviceId,
          isDisabled: disabled,
        };
      });
    },
    [disabledServices, info, petId, serviceMap],
  );

  const tabOptions = useMemo(
    () => [
      { disabled: disabledTabs?.includes(ServiceType.SERVICE), label: 'Service', value: ServiceType.SERVICE },
      { disabled: disabledTabs?.includes(ServiceType.ADDON), label: 'Add-on', value: ServiceType.ADDON },
    ],
    [disabledTabs],
  );

  const options = useMemo(() => {
    const lastList = tab === ServiceType.SERVICE ? lastActiveServiceList : lastActiveAddonList;
    const otherList = tab === ServiceType.SERVICE ? applicableServices : applicableAddons;
    const last = lastList.length
      ? [
          {
            label: 'LAST APPOINTMENT',
            options: getOptions(lastList),
          },
        ]
      : [];

    const optionsList = last.concat(
      otherList.map((c) => ({
        label: c.categoryName || 'UNCATEGORIZED',
        options: getOptions(c.services),
      })),
    );

    if (!isNormal(serviceId)) return optionsList;

    const isExist = optionsList.some((item) => item.options?.some((sub) => sub.value === serviceId));
    return isExist
      ? optionsList
      : optionsList.concat({
          label: '',
          options: [
            {
              label: info?.serviceName || '',
              name: info?.serviceName || '',
              value: serviceId,
              isDisabled: true,
            },
          ],
        });
  }, [
    tab,
    lastActiveServiceList,
    lastActiveAddonList,
    applicableServices,
    applicableAddons,
    getOptions,
    serviceId,
    info?.serviceName,
  ]);

  const label = useMemo(() => {
    if (labelProps) return labelProps;
    if ([ServiceType.SERVICE, ServiceType.ADDON].every((v) => !disabledTabs?.includes(v))) {
      return 'Service/add-on';
    }
    if (disabledTabs?.includes(ServiceType.ADDON)) {
      return 'Service';
    }
    return 'Add-on';
  }, [disabledTabs, labelProps]);

  useEffect(() => {
    if (disabledTabs?.includes(tab)) {
      const nextTab = tabOptions.find((item) => !disabledTabs?.includes(item.value))?.value;
      nextTab && setTab(nextTab);
    }
  }, [disabledTabs, tabOptions, tab]);

  return (
    <div data-testid={ApptTestIds.ApptEditPetServiceSelectServiceBtn}>
      <Select
        isDisabled={isDisabled}
        isRequired={isRequired}
        label={label}
        value={serviceId}
        items={options}
        isLoading={loading}
        scrollProps={{ viewportProps: { contentStyle: { width: '100%', tableLayout: 'fixed' } } }}
        renderMenu={(menu) => (
          <div className="moe-w-full moe-max-h-[300px] moe-flex moe-flex-col">
            <Condition if={tabOptions.filter((v) => !v.disabled).length > 1}>
              <div className="moe-flex moe-px-s moe-pt-xs moe-w-full">
                <Tabs
                  classNames={{
                    base: 'moe-w-full',
                    panel: 'moe-pt-none',
                  }}
                  selectedKey={String(tab)}
                  onChange={(v) => setTab(Number(v))}
                  disableAnimation
                >
                  {tabOptions.map((item) => (
                    <Tabs.Item key={item.value} label={item.label} isDisabled={item.disabled}></Tabs.Item>
                  ))}
                </Tabs>
              </div>
            </Condition>
            <div className="moe-flex-1 moe-overflow-auto">{menu}</div>
            <div className="moe-p-s moe-border-t moe-border-divider">
              <Checkbox
                isSelected={isApplicable.value}
                onClick={(e) => {
                  e.preventDefault();
                  const checked = !isApplicable.value;
                  isApplicable.as(checked);
                  reportData(ReportActionName.apptDrawerOnlyShowApplicableServicesClick, {
                    serviceType: serviceType,
                    checked,
                  });
                }}
              >
                <Text variant="small">Only show applicable services</Text>
              </Checkbox>
            </div>
          </div>
        )}
        formatOptionLabel={(item) => {
          // 此处 item.props 包含了 ServiceOptionItem 类型，但 typing 没有声明清楚，所以 as 指定下方便阅读
          return (item.props as ServiceOptionItem)?.name || '';
        }}
        onChange={(v) => {
          const serviceId = Number(v);
          const serviceInfo = getServiceInfo(serviceId);
          onChange?.(Number(v), {
            ...serviceInfo,
          });
        }}
      />
    </div>
  );
});

PickService.displayName = 'PickService';
