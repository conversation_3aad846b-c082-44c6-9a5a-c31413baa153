import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector, useStore } from 'amos';
import dayjs from 'dayjs';
import React, { type FC, memo } from 'react';
import { selectApptStartAndEndTime } from '../../container/Appt/store/appt.selectors';
import { petMapBox } from '../../store/pet/pet.boxes';
import { getVaccinationExpiryList } from '../../store/pet/petVaccineBinding.selectors';
import { LegacyBool } from '../../store/utils/createEnum';
import { isNormal } from '../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../utils/DateTimeUtil';
import { VaccineIconComponent } from './component/VaccineIconComponent';
import { ReportActionName } from '../../utils/reportType';
import { reportData } from '../../utils/tracker';

interface VaccineIconProps {
  /**
   * 必传 petId 目前没有多只的场景
   *
   * @type {number}
   * @memberof VaccineIconProps
   */
  petId: number;
  /**
   * 因为疫苗过期信息和时间强相关比如确定过期时间
   * 如果传入 appointmentDate 则优先使用 appointmentDate
   * 如果传入 appointmentId 则去 selector 查找对应预约的开始时间
   * 兜底使用当前时间
   *
   * @type {number}
   * @memberof VaccineIconProps
   */
  appointmentId?: number;
  appointmentDate?: string;
  serviceItemTypes?: ServiceItemType[];
  onTooltipOpenChange?: (isOpen: boolean) => void;
}

export const VaccineIcon: FC<VaccineIconProps> = memo((props) => {
  const { petId, appointmentId, appointmentDate, serviceItemTypes } = props;
  const store = useStore();

  // 开始时间如果不传则去找 appointmentId 对应的开始时间
  const startDate =
    appointmentDate ??
    (isNormal(appointmentId)
      ? (store.select(selectApptStartAndEndTime(String(appointmentId))).startDateTime?.format(DATE_FORMAT_EXCHANGE) ??
        dayjs().format(DATE_FORMAT_EXCHANGE))
      : dayjs().format(DATE_FORMAT_EXCHANGE));

  // 拿到当前 pet 所有的 vaccine 列表
  const [vaccineList, pet] = useSelector(
    getVaccinationExpiryList(petId, startDate, { appointmentId, serviceItemTypes }),
    petMapBox.mustGetItem(petId),
  );

  // 满足下面条件则不显示:
  // 1. 如果 pet detail 中关闭了 vaccine 的提醒则不展示
  // 2. 商家没有 requirement 且 pet 也没有 vaccine 那么就不展示
  if (!LegacyBool.truly(pet.expiryNotification) || vaccineList.length === 0) return null;

  return (
    <VaccineIconComponent
      petId={petId}
      vaccineList={vaccineList}
      onTooltipOpenChange={(isOpen) => {
        if (!isOpen) return;
        reportData(ReportActionName.petInfoComponentVaccinesPreview);
      }}
      onVaccineRecordUpdate={() => {
        reportData(ReportActionName.petInfoComponentVaccinesUpdate);
      }}
    />
  );
});

VaccineIcon.displayName = 'VaccineIcon';
