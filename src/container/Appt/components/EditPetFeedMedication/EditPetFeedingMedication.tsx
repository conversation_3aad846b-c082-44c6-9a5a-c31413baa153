import { Checkbox, cn } from '@moego/ui';
import React, { memo, useEffect } from 'react';
import { Condition } from '../../../../components/Condition';
import { useBool } from '../../../../utils/hooks/useBool';
import { useGetPetFeedingMedication } from '../../hooks/useGetPetFeedingMedication';
import { type ServiceFeedingMedication } from '../../store/appt.types';
import { Feeding } from '../SelectServiceDetail/components/Feeding/Feeding';
import { getDefaultFeedingValue } from '../SelectServiceDetail/components/Feeding/Feeding.util';
import { Medication } from '../SelectServiceDetail/components/Medication/Medication';

export interface EditPetFeedingMedicationProps {
  appointmentId: string;
  petId: string;
  className?: string;
  value?: ServiceFeedingMedication;
  onChange?: (value: ServiceFeedingMedication) => void;
}

export const EditPetFeedingMedication = memo<EditPetFeedingMedicationProps>(function EditPetFeedingMedication(props) {
  const { appointmentId, petId, className, value, onChange } = props;
  const isSelectedNeedMedication = !!value?.medications.length;
  const visible = useBool(isSelectedNeedMedication);

  const { getPetMedication } = useGetPetFeedingMedication();

  useEffect(() => {
    if (isSelectedNeedMedication !== visible.value) {
      visible.as(isSelectedNeedMedication);
    }
  }, [isSelectedNeedMedication, visible.value]);

  if (!value) {
    return null;
  }

  const { feedings, medications } = value || {};

  return (
    <div className={cn('moe-flex moe-flex-col moe-items-stretch', className)}>
      <Feeding
        value={feedings && feedings.length ? feedings : [getDefaultFeedingValue({})]}
        onChange={(v) => {
          onChange?.({ ...value, feedings: v });
        }}
      />

      <Checkbox
        className="moe-mt-m moe-mb-xs"
        isSelected={visible.value}
        onChange={(v) => {
          visible.as(v);
          if (v) {
            onChange?.({ ...value, medications: value.petId ? getPetMedication(Number(value.petId)) : [] });
          } else {
            onChange?.({ ...value, medications: [] });
          }
        }}
      >
        Need medications
      </Checkbox>

      <Condition if={visible.value || medications.length > 0}>
        <Medication
          appointmentId={appointmentId}
          petId={petId}
          value={medications}
          onChange={(v) => {
            onChange?.({ ...value, medications: v });
          }}
        />
      </Condition>
    </div>
  );
});

EditPetFeedingMedication.displayName = 'EditPetFeedingMedication';
