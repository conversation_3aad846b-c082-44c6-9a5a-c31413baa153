/* eslint-disable sonarjs/no-nested-template-literals */
import { BusinessPetMetadataName } from '@moego/api-web/moego/models/business_customer/v1/business_pet_metadata_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Button, Carousel, cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useMemo, useRef } from 'react';
import { useMount, useSetState } from 'react-use';
import { Switch } from '../../../../components/SwitchCase';
import { toastApi } from '../../../../components/Toast/Toast';
import { petMapBox } from '../../../../store/pet/pet.boxes';
import {
  getPetFeedingScheduleList,
  getPetMedicationScheduleList,
} from '../../../../store/pet/petFeedingMedication.actions';
import { getPetMetaDataList } from '../../../../store/pet/petMetaData.action';
import { ID_ANONYMOUS } from '../../../../store/utils/identifier';
import { useAsyncEffect } from '../../../../utils/hooks/useAsyncEffect';
import { useBool } from '../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { saveFeedingAndMedicationLocal, saveFeedingAndMedicationServer } from '../../store/appt.actions';
import { selectApptFeedingAndMedication, selectServiceDetailInAppt } from '../../store/appt.selectors';
import { CreateApptId, type ServiceFeedingMedication } from '../../store/appt.types';
import { checkFeedingMedicationTimeIsInvalid } from '../../utils/feedingMedication';
import { DrawerHead } from '../DrawerHead/DrawerHead';
import { PetAvatar } from '../PetAvatar/PetAvatar';
import { EditPetFeedingMedication } from './EditPetFeedingMedication';
import { reportData } from '../../../../utils/tracker';
import { isEqual, uniq } from 'lodash';
import { ReportActionName } from '../../../../utils/reportType';

interface State {
  activePetId: string;
  petsFeedingAndMedication: ServiceFeedingMedication[];
}

export interface QuickAddFeedMedicationProps {
  className?: string;
  defaultPetId?: string;
  appointmentId: string;
  goBack?: () => void;
}

export const QuickAddFeedMedication = memo<QuickAddFeedMedicationProps>(function QuickAddFeedMedication(props) {
  const { className, appointmentId, defaultPetId } = props;
  const isCreated = appointmentId === CreateApptId;
  const isEdit = !isCreated;
  const dispatch = useDispatch();
  const [petsOrig, initPetsFeedingAndMedication, petMap] = useSelector(
    selectServiceDetailInAppt(appointmentId),
    selectApptFeedingAndMedication(appointmentId),
    petMapBox,
  );
  const isDirty = useBool();

  const pets = useMemo(() => {
    return petsOrig.filter((pet) =>
      // 排除 G，只留下 BDE
      pet.services.some((service) => service.serviceItemType && service.serviceItemType !== ServiceItemType.GROOMING),
    );
  }, [petsOrig]);

  const [state, setState] = useSetState<State>({
    activePetId: defaultPetId || pets?.[0]?.petId,
    petsFeedingAndMedication: initPetsFeedingAndMedication,
  });
  const { activePetId, petsFeedingAndMedication } = state;
  const moreThanOnePet = pets.length > 1;
  const targetPet = pets.find((i) => i.petId === activePetId);
  const mainService = targetPet?.services[0];
  const go2Save = useSerialCallback(async () => {
    const {
      feedingTimeIsInvalid,
      medicationTimeIsInvalid,
      invalidFeedingTimePetIdList,
      invalidMedicationTimePetIdList,
    } = checkFeedingMedicationTimeIsInvalid(petsFeedingAndMedication);
    if (feedingTimeIsInvalid || medicationTimeIsInvalid) {
      // 取第一只就够了，如果有多只，可以让用户一只一只改
      const firstInvalidFeedingPet = petMap.mustGetItem(+(invalidFeedingTimePetIdList[0] || ID_ANONYMOUS)).petName;
      const firstInvalidMedicationPet = petMap.mustGetItem(
        +(invalidMedicationTimePetIdList[0] || ID_ANONYMOUS),
      ).petName;
      const text = feedingTimeIsInvalid
        ? `${firstInvalidFeedingPet ? `${firstInvalidFeedingPet}'s feeding` : 'Feeding'} time is required.`
        : feedingTimeIsInvalid && medicationTimeIsInvalid
          ? 'Feeding and medication times are required.'
          : `${firstInvalidMedicationPet ? `${firstInvalidMedicationPet}'s medication` : 'Medication'} time is required.`;

      toastApi.error(text);
      return;
    }
    if (isEdit) {
      await dispatch(saveFeedingAndMedicationServer(appointmentId, petsFeedingAndMedication));
    } else {
      await dispatch(saveFeedingAndMedicationLocal(petsFeedingAndMedication));
    }
    toastApi.success('Update successfully.');
  });

  const getData = useSerialCallback(async () => {
    await Promise.all([
      dispatch(
        getPetMetaDataList([
          BusinessPetMetadataName.FEEDING_SCHEDULE,
          BusinessPetMetadataName.FEEDING_UNIT,
          BusinessPetMetadataName.FEEDING_TYPE,
          BusinessPetMetadataName.FEEDING_SOURCE,
          BusinessPetMetadataName.FEEDING_INSTRUCTION,
          BusinessPetMetadataName.MEDICATION_SCHEDULE,
          BusinessPetMetadataName.MEDICATION_UNIT,
        ]),
      ),
    ]);
  });
  useMount(getData);

  // 首次选中 activePetId 时，拉一次 feeding & medication 数据
  const fetchPetFeedingMedicationFlagRef = useRef<Record<string, boolean>>({});
  useAsyncEffect(async () => {
    if (fetchPetFeedingMedicationFlagRef.current[activePetId]) return;
    await Promise.all([
      dispatch(getPetMedicationScheduleList(+activePetId)),
      dispatch(getPetFeedingScheduleList(+activePetId)),
    ]);
    fetchPetFeedingMedicationFlagRef.current[activePetId] = true;
  }, [activePetId]);

  const reportFeedMedication = (nextPetsFeedingAndMedication: ServiceFeedingMedication[]) => {
    const isFeedingChanged = !isEqual(
      petsFeedingAndMedication.map((i) => i.feedings),
      nextPetsFeedingAndMedication.map((i) => i.feedings),
    );
    reportData(isFeedingChanged ? ReportActionName.apptDrawerFeeding : ReportActionName.apptDrawerMedication, {
      from: 'quickAddFeedMedication',
      scene: isEdit ? 'edit' : 'create',
      serviceTypes: uniq(pets.flatMap((i) => i.services.map((service) => service.serviceItemType))),
    });
  };

  if (!targetPet || !mainService) {
    return null;
  }
  const value = petsFeedingAndMedication.find((i) => i.petId === activePetId);

  return (
    <div className={cn('moe-flex-1 moe-flex moe-flex-col moe-min-h-0 moe-min-w-0 moe-items-stretch', className)}>
      <DrawerHead title="Feeding & Medication" />
      <div className="moe-p-[24px] moe-flex-1 moe-min-h-0 moe-overflow-auto">
        <Switch>
          <Switch.Case if={moreThanOnePet}>
            <Carousel
              align="end"
              indicatorType="none"
              showArrowMask
              defaultIndex={pets.findIndex((i) => i.petId === activePetId)}
              classNames={{ container: 'moe-gap-s' }}
            >
              {pets.map((item) => {
                return (
                  <PetAvatar
                    isActive={item.petId === activePetId}
                    className="moe-flex-1 moe-max-w-[240px]"
                    key={item.petId}
                    petId={item.petId}
                    onClick={() => {
                      setState({ activePetId: item.petId });
                    }}
                  />
                );
              })}
            </Carousel>
          </Switch.Case>
          <Switch.Case else>
            <PetAvatar
              isActive
              isNoBorder
              className="moe-mb-m moe-p-0 moe-bg-white moe-cursor-default"
              petId={activePetId}
            />
          </Switch.Case>
        </Switch>
        <EditPetFeedingMedication
          appointmentId={appointmentId}
          petId={activePetId}
          value={value}
          onChange={(value) => {
            const nextPetsFeedingAndMedication = petsFeedingAndMedication.map((i) => {
              if (i.petId === value.petId) {
                return value;
              }
              return i;
            });
            isDirty.open();
            reportFeedMedication(nextPetsFeedingAndMedication);
            setState({ petsFeedingAndMedication: nextPetsFeedingAndMedication });
          }}
          className={moreThanOnePet ? 'moe-mt-[24px]' : ''}
        />
      </div>
      <div className="moe-py-[16px] moe-px-[24px]">
        <Button className="moe-w-full" onPress={go2Save} isLoading={go2Save.isBusy()} isDisabled={!isDirty.value}>
          Save
        </Button>
      </div>
    </div>
  );
});
