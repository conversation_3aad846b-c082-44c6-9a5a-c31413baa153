import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector } from 'amos';
import { type ApptInfoPetServiceInfo } from '../../../../store/calendarLatest/calendar.types';
import { serviceMapBox } from '../../../../store/service/service.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../../store/utils/identifier';
import { useCanAutoMerge } from '../../hooks/useCanAutoMerge';
import { matchApptServiceScenes } from '../../store/appt.options';
import { selectMainServiceInPet } from '../../store/appt.selectors';
import { ApptServiceScene, CreateApptId } from '../../store/appt.types';
import { getAssociatedId } from '../../store/appt.utils';
import { type EditServiceItemProps } from './EditServiceItem.type';

export interface GetServiceItemStatusProps extends EditServiceItemProps {}

export const useGetServiceItemStatus = (props: GetServiceItemStatusProps) => {
  const { value } = props;
  const { staffId, isPriceModified, isDurationModified, serviceItemType, serviceType, serviceId } = value || {};
  const [{ requireDedicatedStaff }] = useSelector(serviceMapBox.mustGetItem(serviceId ?? ID_ANONYMOUS));

  const isAddon = serviceType === ServiceType.ADDON;
  const isService = serviceType === ServiceType.SERVICE;

  const [isAssociatedService, showServiceDateTime, isRequireStaff, showLodgingAssignment, showSplitLodgings] =
    matchApptServiceScenes(
      [
        ApptServiceScene.AddAssociatedService,
        ApptServiceScene.ServiceDateTimeWhenEdit,
        ApptServiceScene.ServiceRequireStaff,
        ApptServiceScene.LodgingAssignment,
        ApptServiceScene.ServiceSplitLodgings,
      ],
      { serviceItemType },
    );

  const isShowDuration = isService ? showServiceDateTime : isAddon;

  const isShowOnBoardingTips =
    isNormal(staffId) && serviceType === ServiceType.SERVICE && (isPriceModified || isDurationModified);

  const isShowQuantity = isAddon && !requireDedicatedStaff && isAssociatedService;

  const isShowStaff = (isService && isRequireStaff) || (isAddon && requireDedicatedStaff);

  const isShowLodging = isService && showLodgingAssignment && !showSplitLodgings;

  const isShowSplitLodging = isService && showLodgingAssignment && showSplitLodgings;

  const disabledTabs = isAssociatedService ? (isService ? [ServiceType.ADDON] : [ServiceType.SERVICE]) : [];

  return {
    isShowStaff,
    isShowOnBoardingTips,
    isShowDuration,
    isShowLodging,
    isShowSplitLodging,
    isShowQuantity: props.isShowQuantity ?? isShowQuantity,
    disabledTabs: props.disabledTabs ?? disabledTabs,
  };
};

export interface UpdateDateTypeAndAssociatedServiceParams {
  targetServiceId: number;
  sourceService?: ApptInfoPetServiceInfo;
}

/**
 * update dateType and associatedId when change service
 * change to service: dateType=PET_DETAIL_DATE_DATE_POINT, associatedId=undefined
 * change to addon: associatedId=source associated service
 *  - required staff addon: dateType=PET_DETAIL_DATE_DATE_POINT
 *  - not required staff addon: dateType = PET_DETAIL_DATE_EVERYDAY (Boarding) Or PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY (Others)
 */
export const useUpdateDateTypeAndAssociatedService = (appointmentId: string, petId: string) => {
  const apptId = isNormal(appointmentId) ? appointmentId : CreateApptId;
  const [petMainService, serviceMap] = useSelector(selectMainServiceInPet(petId, apptId), serviceMapBox);
  const canAutoMerge = useCanAutoMerge();

  return ({ targetServiceId, sourceService }: UpdateDateTypeAndAssociatedServiceParams) => {
    const targetService = serviceMap.mustGetItem(targetServiceId);

    let dateType: PetDetailDateType | undefined = undefined;
    let associatedId: string | undefined = undefined;

    // 判断是否是 addon
    const isTargetAddon = targetService?.type === ServiceType.ADDON;
    const isSourceAddon = sourceService?.serviceType === ServiceType.ADDON;
    const isKeepSourceService =
      isNormal(sourceService?.serviceId) && canAutoMerge(sourceService?.serviceId, targetServiceId);

    // change to service，daycare 也有可能有 dateType
    if (!isTargetAddon && isSourceAddon) {
      dateType = PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;
      associatedId = undefined;
    }
    //  keep source service dataType and associatedId when addon "requireDedicatedStaff" did not change
    else if (isKeepSourceService) {
      dateType = sourceService.dateType;
      associatedId = sourceService.associatedId;
    } else {
      // otherwise update dateType and associatedId according to targetService.requireDedicatedStaff
      const isRequiredStaffAddon = targetService?.requireDedicatedStaff;
      associatedId = getAssociatedId(sourceService?.associatedId);
      if (isRequiredStaffAddon) {
        dateType = PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;
      } else {
        dateType =
          petMainService.serviceItemType === ServiceItemType.BOARDING
            ? PetDetailDateType.PET_DETAIL_DATE_EVERYDAY
            : PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY;
      }
    }

    return {
      dateType,
      associatedId,
    };
  };
};
