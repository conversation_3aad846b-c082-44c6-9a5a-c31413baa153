import { Checkbox } from '@moego/ui';
import { FormItemLabel } from '@moego/ui/dist/esm/components/Form/FormItemLabel';
import { useSelector } from 'amos';
import React, { type FC } from 'react';
import { Condition } from '../../../../../components/Condition';
import { getDefaultService } from '../../../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { SplitLodgings } from '../../../../../components/SplitLodgings/SplitLodgings';
import { type SplitLodgingItemValue } from '../../../../../components/SplitLodgings/utils/SplitLodgings.types';
import { isNormal } from '../../../../../store/utils/identifier';
import { selectPetsInAppt } from '../../../store/appt.selectors';
import { CreateApptId } from '../../../store/appt.types';
import { formatSplitLodgingsValueToServerValue } from '../../SelectServiceDetail/components/Service/ServiceBoarding/ServiceLodging.utils';
import { type EditServiceItemProps } from '../EditServiceItem.type';

interface SelectSplitLodgingProps extends EditServiceItemProps {}

export const SelectSplitLodging: FC<SelectSplitLodgingProps> = (props) => {
  const { value, onChange, petId, form, formKey, appointmentId } = props;
  const apptId = isNormal(appointmentId) ? appointmentId : CreateApptId;
  const { serviceId, startDate, startTime, endDate, endTime, isApplySplitLodgingsToAllPets, splitLodgings } =
    value || {};
  const [petsInfo] = useSelector(selectPetsInAppt(String(apptId), false));

  const handleSplitLodgingsChange = (splitLodgings: SplitLodgingItemValue[]) => {
    if (splitLodgings.length === 1) {
      const [item] = splitLodgings;
      onChange?.(
        getDefaultService({
          ...value,
          lodgingId: item.lodgingId,
          lodgingName: item.lodgingName,
          splitLodgings: [],
        }),
      );
    } else {
      onChange?.(
        getDefaultService({
          ...value,
          lodgingId: undefined,
          lodgingName: undefined,
          splitLodgings: formatSplitLodgingsValueToServerValue(splitLodgings),
        }),
      );
    }
  };

  if (!isNormal(serviceId)) {
    return null;
  }

  return (
    <div className="moe-flex-1 moe-h-full">
      <FormItemLabel isRequired>{'Lodging(s)'}</FormItemLabel>
      <SplitLodgings
        form={form}
        formKey={formKey}
        range={{ startDate, startTime, endDate, endTime }}
        petId={petId}
        serviceId={serviceId}
        onChange={handleSplitLodgingsChange}
      />
      <Condition if={splitLodgings && splitLodgings.length > 1 && petsInfo.length > 1}>
        <Checkbox
          className="moe-mt-8px-100"
          isSelected={isApplySplitLodgingsToAllPets}
          onChange={(checked) => onChange?.(getDefaultService({ ...value, isApplySplitLodgingsToAllPets: checked }))}
        >
          Apply to all pets
        </Checkbox>
      </Condition>
    </div>
  );
};

SelectSplitLodging.displayName = 'SelectSplitLodging';
