import { type AppointmentPetFeedingScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_feeding_schedule_defs';
import { MajorPlusOutlined } from '@moego/icons-react';
import { But<PERSON>, Heading } from '@moego/ui';
import React from 'react';
import { Condition } from '../../../../../../components/Condition';
import { memoForwardRef } from '../../../../../../utils/react';
import { type FeedingValue, getDefaultFeedingValue } from './Feeding.util';
import { FeedingItem } from './Item';

export interface FeedingProps {
  value?: FeedingValue[];
  onChange?: (value: FeedingValue[]) => void;
  syncPetFeeding?: () => AppointmentPetFeedingScheduleDef[] | undefined;
}

export const Feeding = memoForwardRef<HTMLDivElement, FeedingProps>((props, ref) => {
  const { value = [], onChange, syncPetFeeding } = props;

  const handleChange = (v: FeedingValue[]) => {
    onChange?.(v);
  };

  const handleRemove = (id?: string) => {
    handleChange(value.filter((item) => item.id !== id));
  };
  const handleAdd = () => {
    let data = value.concat(getDefaultFeedingValue({}));

    // 数据填充
    if (value.length === 0 && syncPetFeeding) {
      const res = syncPetFeeding();
      res && (data = res);
    }
    handleChange(data);
  };

  return (
    <div ref={ref}>
      <div className="moe-mb-xs">
        <Heading size="6">Feeding instructions</Heading>
      </div>
      <div className="moe-flex moe-flex-col moe-rounded-s moe-bg-neutral-sunken-0">
        {value.map((item, index: number) => (
          <div className="moe-border-b-[1px] moe-border-divider moe-border-bottom moe-p-s" key={item.id}>
            <FeedingItem
              value={item}
              onChange={(v) => {
                const newValue = value.slice();
                newValue[index] = v;
                handleChange(newValue);
              }}
            />

            <Condition if={index}>
              <Button
                align="start"
                className="moe-mt-s"
                size="s"
                variant="tertiary-legacy"
                onPress={() => handleRemove(item.id)}
              >
                Delete schedule
              </Button>
            </Condition>
          </div>
        ))}

        <div className="moe-pb-s moe-pt-xs">
          <Button size="s" icon={<MajorPlusOutlined />} variant="tertiary-legacy" onPress={handleAdd}>
            Add feeding schedule
          </Button>
        </div>
      </div>
    </div>
  );
});

Feeding.displayName = 'Feeding';
