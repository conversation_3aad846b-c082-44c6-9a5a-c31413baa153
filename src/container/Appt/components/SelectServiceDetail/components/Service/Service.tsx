import { type AppointmentPetFeedingScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_feeding_schedule_defs';
import { type AppointmentPetMedicationScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_medication_schedule_defs';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { MinorTrashOutlined } from '@moego/icons-react';
import { Checkbox, Form, Heading } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { Switch } from '../../../../../../components/SwitchCase';
import { serviceMapBox } from '../../../../../../store/service/service.boxes';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useIsSingleToMultiDaysApptCallback } from '../../../../hooks/useIsSingleToMultiDaysApptCallback';
import { useSyncPetFeeding, useSyncPetMedication } from '../../../../hooks/useSyncPetDetail';
import { setServiceForPet } from '../../../../store/appt.actions';
import { ApptServiceRecord } from '../../../../store/appt.boxes';
import { selectApptPetService, selectMainServiceInAppt, selectPetsInAppt } from '../../../../store/appt.selectors';
import { CreateApptId } from '../../../../store/appt.types';
import { Feeding } from '../Feeding/Feeding';
import { Medication } from '../Medication/Medication';
import { type ServiceEntryProps } from './Service.type';
import { ServiceBoarding } from './ServiceBoarding/ServiceBoarding';
import { ServiceDaycareMultiDay } from './ServiceDaycare/ServiceDaycareMultiDay';
import { ServiceDaycareNormal } from './ServiceDaycare/ServiceDaycareNormal';
import { ServiceGrooming } from './ServiceGrooming';
import { ReportActionName } from '../../../../../../utils/reportType';
import { reportData } from '../../../../../../utils/tracker';

export const Service = memo((props: ServiceEntryProps) => {
  const {
    item: { id },
    appointmentId = CreateApptId,
    petId,
    isMultiple,
    renderAdditionalService,
    canRemove,
    onRemove,
  } = props;

  const [pets, mainService, { serviceId }] = useSelector(
    selectPetsInAppt(appointmentId),
    selectMainServiceInAppt(appointmentId),
    selectApptPetService(appointmentId, id),
  );
  const [{ serviceItemType, name }] = useSelector(serviceMapBox.mustGetItem(Number(serviceId)));
  const dispatch = useDispatch();
  const visible = useBool(false);
  const isSingleToMultiDaysAppt = useIsSingleToMultiDaysApptCallback();

  const key = ApptServiceRecord.createOwnId(appointmentId, id);

  const isMainService = useMemo(() => {
    const petDetail = pets.find((p) => p.petId === String(petId));
    if (!petDetail) return false;
    const { services } = petDetail;
    const currentPetMainService = services.find((s) => s.serviceItemType === mainService.serviceItemType);
    return currentPetMainService?.serviceId === serviceId;
  }, [mainService.serviceItemType, petId, pets, serviceId]);

  const isSingleToMultiDays = useMemo(
    () => isSingleToMultiDaysAppt(appointmentId, serviceItemType),
    [appointmentId, serviceItemType],
  );

  const needExtraInfo = !isSingleToMultiDays && isMainService;

  const showAdditionalService = useMemo(() => {
    return isMainService && serviceItemType === ServiceItemType.BOARDING;
  }, [isMainService, serviceItemType]);

  const reportFeedMedication = (key: ReportActionName) => {
    reportData(key, {
      from: 'service',
      scene: 'create',
      serviceTypes: [serviceItemType],
    });
  };

  const { syncPetMedication } = useSyncPetMedication({
    petId: Number(petId),
    id,
    disabled: !needExtraInfo || !visible.value,
    flag: visible.value,
    appointmentId,
  });
  const { syncPetFeeding } = useSyncPetFeeding({ petId: Number(petId), id, disabled: !needExtraInfo, appointmentId });

  return (
    <div className="moe-flex moe-flex-col moe-gap-m">
      <div className="moe-flex moe-flex-col moe-gap-s">
        <div className="moe-flex moe-items-center moe-justify-between">
          {!isMultiple && <Heading size={5}>{name}</Heading>}
          {canRemove && !isMainService && (
            <MinorTrashOutlined className="moe-w-[24px] moe-h-[24px] moe-cursor-pointer" onClick={onRemove} />
          )}
        </div>
        <Switch>
          <Switch.Case if={serviceItemType === ServiceItemType.BOARDING}>
            <ServiceBoarding {...props} appointmentId={appointmentId} formKey={key} />
          </Switch.Case>
          <Switch.Case if={serviceItemType === ServiceItemType.DAYCARE}>
            <Switch>
              <Switch.Case if={mainService.serviceItemType === ServiceItemType.BOARDING}>
                {/* boarding 的 daycare 可以选择多天 */}
                <ServiceDaycareMultiDay {...props} formKey={key} />
              </Switch.Case>
              <Switch.Case else>
                <ServiceDaycareNormal {...props} formKey={key} showDatePicker={!isMultiple} />
              </Switch.Case>
            </Switch>
          </Switch.Case>
          <Switch.Case if={serviceItemType === ServiceItemType.GROOMING}>
            <ServiceGrooming {...props} formKey={key} />
          </Switch.Case>
        </Switch>
      </div>

      <Condition if={needExtraInfo}>
        <Form.Item name={`${key}.feedings`}>
          <Feeding
            syncPetFeeding={syncPetFeeding}
            onChange={(v) => {
              dispatch(
                setServiceForPet(appointmentId, id, {
                  feedings: v as AppointmentPetFeedingScheduleDef[],
                }),
              );
              reportFeedMedication(ReportActionName.apptDrawerFeeding);
            }}
          />
        </Form.Item>

        <Checkbox
          defaultSelected={visible.value}
          onChange={(v) => {
            visible.as(v);
            if (v) {
              syncPetMedication();
            } else {
              dispatch(
                setServiceForPet(appointmentId, id, {
                  medications: [],
                }),
              );
            }
          }}
        >
          Need medications
        </Checkbox>

        <Condition if={visible.value}>
          <Form.Item name={`${key}.medications`}>
            <Medication
              appointmentId={appointmentId}
              petId={String(petId)}
              onChange={(v) => {
                dispatch(
                  setServiceForPet(appointmentId, id, {
                    medications: v as AppointmentPetMedicationScheduleDef[],
                  }),
                );
                reportFeedMedication(ReportActionName.apptDrawerMedication);
              }}
            ></Medication>
          </Form.Item>
        </Condition>
      </Condition>
      {showAdditionalService && renderAdditionalService?.(props)}
    </div>
  );
});

Service.displayName = 'Service';
