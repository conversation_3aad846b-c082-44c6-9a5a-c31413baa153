import { Checkbox, Text } from '@moego/ui';
import { FormItemLabel } from '@moego/ui/dist/esm/components/Form/FormItemLabel';
import { useDispatch, useSelector } from 'amos';
import React, { type FC, memo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { SplitLodgings } from '../../../../../../../components/SplitLodgings/SplitLodgings';
import { type SplitLodgingItemValue } from '../../../../../../../components/SplitLodgings/utils/SplitLodgings.types';
import { petMapBox } from '../../../../../../../store/pet/pet.boxes';
import { setServiceForPet } from '../../../../../store/appt.actions';
import { selectMainServiceInAppt } from '../../../../../store/appt.selectors';
import { type ApptServiceInfoRecord } from '../../../../../store/appt.types';
import { type ServiceLodgingProps } from './ServiceBoarding.types';
import { formatSplitLodgingsValueToServerValue } from './ServiceLodging.utils';
import { useGetApptPetServiceSplitLodgings } from './hooks/useGetApptPetServiceSplitLodgings';
import { useSyncFromMainService } from './hooks/useSyncFromMainService';
import { ReportActionName } from '../../../../../../../utils/reportType';
import { reportData } from '../../../../../../../utils/tracker';
import { ServiceItemType } from '@moego/bff-openapi/clients/client.order';

export const ServiceLodging: FC<ServiceLodgingProps> = memo((props) => {
  const { id, petId, serviceId, appointmentId, range, isDisabled, form, formKey } = props;
  const dispatch = useDispatch();
  const [mainService] = useSelector(selectMainServiceInAppt(appointmentId));
  const [{ petName }] = useSelector(petMapBox.mustGetItem(Number(mainService.petId)));
  const getApptPetServiceSplitLodgings = useGetApptPetServiceSplitLodgings();
  const value = getApptPetServiceSplitLodgings({ id, serviceId, appointmentId });
  const { allowShowSyncMainService, syncSplitLodgingsFromMainService, getIsLodgingSameAsMainService } =
    useSyncFromMainService({
      id,
      petId,
      appointmentId,
    });

  const handleChange = (params: Partial<ApptServiceInfoRecord>) => {
    dispatch(setServiceForPet(appointmentId, id, params));
  };

  const handleSplitLodgingsChange = (value: SplitLodgingItemValue[]) => {
    if (value.length === 1) {
      const [item] = value;
      handleChange({ lodgingId: item.lodgingId, lodgingName: item.lodgingName, splitLodgings: [] });
    } else {
      handleChange({
        lodgingId: undefined,
        lodgingName: undefined,
        splitLodgings: formatSplitLodgingsValueToServerValue(value),
      });
    }
  };

  return (
    <div className="moe-flex moe-flex-col moe-gap-xs">
      <FormItemLabel isRequired>{'Lodging(s)'}</FormItemLabel>
      <SplitLodgings
        form={form}
        formKey={formKey}
        range={range}
        petId={petId}
        serviceId={serviceId}
        isDisabled={isDisabled}
        onChange={handleSplitLodgingsChange}
      />
      <Condition if={allowShowSyncMainService}>
        <Checkbox
          defaultSelected={false}
          isSelected={getIsLodgingSameAsMainService(value)}
          onChange={(v) => {
            syncSplitLodgingsFromMainService(v);
            reportData(ReportActionName.apptDrawerSameLodgingAsClick, {
              serviceType: ServiceItemType.BOARDING,
              checked: v,
            });
          }}
        >
          <Text variant="small">Same lodging as {petName}</Text>
        </Checkbox>
      </Condition>
    </div>
  );
});

ServiceLodging.displayName = 'ServiceLodging';
