import { Checkbox, Condition, Form, Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { petMapBox } from '../../../../../../../store/pet/pet.boxes';
import { selectApptPetService } from '../../../../../store/appt.selectors';
import { SelectRoom } from '../../SelectRoom/SelectRoom';
import { useDaycareHook, usePrefillLodging } from './ServiceDaycare.hook';
import { type ServiceLodgingProps } from './ServiceDaycare.type';
import { ReportActionName } from '../../../../../../../utils/reportType';
import { reportData } from '../../../../../../../utils/tracker';
import { ServiceItemType } from '@moego/bff-openapi/clients/client.order';

export const ServiceLodging = memo((props: ServiceLodgingProps) => {
  const { formKey: key, appointmentId, item, isDisabled, range, petId, onChange, form } = props;
  const { id } = item;

  const [{ serviceId }] = useSelector(selectApptPetService(appointmentId, id));
  const {
    mainService,
    currentService: { lodgingId },
    shouldSyncMainService,
  } = useDaycareHook(appointmentId, id);

  const [{ petName }] = useSelector(petMapBox.mustGetItem(Number(mainService.petId)));

  usePrefillLodging({ item, form, formKey: key, appointmentId, range });

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-xs">
      <Form.Item name={`${key}.lodgingId`} label="Lodging allocation">
        <SelectRoom
          petId={petId}
          isRequired={false}
          isClearable
          range={range}
          serviceId={serviceId}
          isDisabled={isDisabled}
          onChange={(v, item) => {
            onChange?.({ lodgingId: v, lodgingName: item?.label });
          }}
          placeholder="Select a room if needed"
        />
      </Form.Item>
      {/* 如果自己就是主 Service 则不需要同步自己 */}
      <Condition if={shouldSyncMainService && mainService.petId !== String(petId) && !!mainService.lodgingId}>
        <Checkbox
          defaultSelected={false}
          isSelected={mainService.lodgingId === lodgingId}
          onChange={(v) => {
            const { lodgingId, lodgingName } = mainService;
            onChange?.(v ? { lodgingId, lodgingName } : { lodgingId: '', lodgingName: '' });
            reportData(ReportActionName.apptDrawerSameLodgingAsClick, {
              serviceType: ServiceItemType.DAYCARE,
              checked: v,
            });
          }}
        >
          <Text variant="small">Same lodging as {petName}</Text>
        </Checkbox>
      </Condition>
    </div>
  );
});
