import { AlertDialog } from '@moego/ui';
import { useStore } from 'amos';
import dayjs from 'dayjs';
import React from 'react';
import { AppointmentCheckerClient } from '../../../middleware/clients';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { calendarQuickAddApptFields } from '../../../store/calendarLatest/calendar.boxes';
import { isNormal } from '../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { stringToDateMessage } from '../../../utils/utils';
import { ConflictCheckAlertDialogContent } from '../components/ConflictCheckAlertDialogContent';
import { apptInfoMapBox } from '../store/appt.boxes';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';

interface ConflictCheckParams {
  appointmentId: string;
  startDateStr?: string;
  endDateStr?: string;
  petIds?: string[];
  lodgingUnitIds?: string[];
  confirmText?: string;
  cancelText?: string;
  showCancel?: boolean;
  clientId?: string;
}

export const useCheckConflictAlert = () => {
  const store = useStore();

  return async (params: ConflictCheckParams) => {
    const {
      appointmentId,
      startDateStr,
      endDateStr,
      petIds = [],
      lodgingUnitIds = [],
      confirmText = 'Continue to schedule',
      cancelText = 'Back to edit',
      showCancel = true,
      clientId,
    } = params;
    const business = store.select(selectCurrentBusiness);
    const isCreateApptId = !isNormal(appointmentId);

    const startDate = stringToDateMessage(startDateStr || dayjs().format(DATE_FORMAT_EXCHANGE));
    const endDate = stringToDateMessage(endDateStr || startDateStr || dayjs().format(DATE_FORMAT_EXCHANGE));

    const finalClientId =
      clientId ||
      (isCreateApptId
        ? String(store.select(calendarQuickAddApptFields).clientId)
        : String(store.select(apptInfoMapBox.mustGetItem(appointmentId)).customerId));

    const checkResult = await AppointmentCheckerClient.checkSaveAppointment({
      petIds,
      businessId: String(business.id),
      lodgingUnitIds: [...new Set(lodgingUnitIds.filter(isNormal))],
      startDate,
      endDate,
      customerId: finalClientId,
      appointmentId: isCreateApptId ? undefined : appointmentId,
    });

    const isConflictAppt = checkResult.appointmentDateConflictCheckResult.conflictAppointments.length > 0;
    const isConflictBusinessClosed = checkResult.businessClosedDateCheckResult.closedDate.length > 0;
    const isConflictLodgingOverCapacity = checkResult.lodgingOverCapacityCheckResult.lodgingUnits.length > 0;
    const hasConflicts = isConflictAppt || isConflictBusinessClosed || isConflictLodgingOverCapacity;

    if (hasConflicts) {
      reportData(ReportActionName.apptCheckConflictAlert, {
        isConflictAppt,
        isConflictBusinessClosed,
        isConflictLodgingOverCapacity,
      });
      await new Promise((resolve, reject) =>
        AlertDialog.open({
          content: <ConflictCheckAlertDialogContent {...checkResult} business={business} />,
          title: 'Conflict alert',
          confirmText,
          cancelText,
          showCancelButton: showCancel,
          onConfirm: () => {
            resolve(undefined);
          },
          onCancel: reject,
          onClose: reject,
        }),
      );
    }
  };
};

export const getLodgingIds = (lodgingId?: string, splitLodgings?: { lodgingId: string }[]) => {
  const lodgingUnitIds: string[] = [];
  if (lodgingId) {
    lodgingUnitIds.push(lodgingId);
  } else if (splitLodgings) {
    lodgingUnitIds.push(...splitLodgings.map(({ lodgingId }) => lodgingId));
  }
  return lodgingUnitIds;
};
