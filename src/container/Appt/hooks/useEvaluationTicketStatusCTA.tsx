import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useSelector } from 'amos';
import { ApptTestIds } from '../../../config/testIds/apptDrawer';
import { selectCurrentPermissions } from '../../../store/business/role.selectors';
import { ReadyForPickupConfirmModalMode, calendarLoadingEventsBox } from '../../../store/calendarLatest/calendar.boxes';
import { type TicketDetailInfo } from '../../../store/calendarLatest/calendar.types';
import { bookingTableLoadingEventsBox } from '../../../store/grooming/grooming.boxes';
import { overviewLoadingEventsBox } from '../../../store/overview/overview.boxes';
import { useBusinessIsWorkingLocation } from '../../../utils/BusinessUtil';
import { useChangeTicketStatus } from '../../Calendar/Grooming/GroomingTicketModal/hooks/useChangeTicketStatus';
import { AppointmentStatus } from '../../TicketDetail/AppointmentStatus';
import { type ActionItem } from '../components/types';
import { NextActionLabel } from '../modules/ApptDetailDrawer/hooks/useTicketStatusCTA';
import { reportCheckInScene } from '../utils/apptReport';
import { type ApptCTAScene } from '../utils/types';
import { useEvaluationConfirmCheckOut } from './useEvaluationConfirmCheckOut';
import { useEvaluationTicketActions } from './useEvaluationTicketActions';
import { useWrapAsyncActionItem } from './useWrapAsyncActionItem';

/**
 * @deprecated use useApptEditStatusCTA instead after invoice reinvent fully launched
 */
export const useEvaluationTicketStatusCTA = (ticket: // 方便复用至overview, 显示传参
{
  id: TicketDetailInfo['id'];
  appointmentStatus: TicketDetailInfo['appointmentStatus'];
  apptCTAScene: ApptCTAScene;
}): ActionItem | null => {
  const dispatch = useDispatch();
  const { id: ticketId, appointmentStatus, apptCTAScene } = ticket;
  const { refreshTicket, setTakePaymentModal, setReadyForPickupModal } = useEvaluationTicketActions(ticketId);
  const handleChangeTicketStatus = useChangeTicketStatus(() => refreshTicket());
  const evaluationConfirmCheckOut = useEvaluationConfirmCheckOut({ appointmentId: ticketId });
  const isWorkingLocation = useBusinessIsWorkingLocation();
  const [permissions] = useSelector(selectCurrentPermissions);
  const canProcessPayment = permissions.has('canProcessPayment');
  const isActionDisabled = !isWorkingLocation;
  const { warpAsyncActionItem } = useWrapAsyncActionItem();

  const actionMap: { [key in AppointmentStatus]?: ActionItem } = {
    [AppointmentStatus.UNCONFIRMED]: {
      label: NextActionLabel.Unconfirmed,
      onClick: async () => {
        const finishReportCheckIn = reportCheckInScene({
          apptCTAScene,
          mainServiceItemType: ServiceItemType.EVALUATION,
        });
        const result = await handleChangeTicketStatus(ticketId, AppointmentStatus.CHECKED_IN);
        finishReportCheckIn?.();
        return result;
      },
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptCheckInBtn,
    },
    [AppointmentStatus.CONFIRMED]: {
      label: NextActionLabel.Confirmed,
      onClick: async () => {
        const finishReportCheckIn = reportCheckInScene({
          apptCTAScene,
          mainServiceItemType: ServiceItemType.EVALUATION,
        });
        const result = await handleChangeTicketStatus(ticketId, AppointmentStatus.CHECKED_IN);
        finishReportCheckIn?.();
        return result;
      },
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptCheckInBtn,
    },
    [AppointmentStatus.CHECKED_IN]: {
      label: NextActionLabel.CheckedIn,
      onClick: async () => {
        return setReadyForPickupModal(true, ReadyForPickupConfirmModalMode.ManualSendMessage);
      },
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptMarkAsReadyBtn,
    },
    [AppointmentStatus.READY]: {
      label: NextActionLabel.Ready,
      onClick: async () => {
        dispatch([
          calendarLoadingEventsBox.setState(true),
          overviewLoadingEventsBox.setState(true),
          bookingTableLoadingEventsBox.setState(true),
        ]);

        try {
          const res = await evaluationConfirmCheckOut();

          if (res.next) {
            await handleChangeTicketStatus(ticketId, AppointmentStatus.FINISHED);
            if (canProcessPayment) {
              setTakePaymentModal(true, { isCheckout: true });
            }
          }
        } finally {
          dispatch([
            calendarLoadingEventsBox.setState(false),
            overviewLoadingEventsBox.setState(false),
            bookingTableLoadingEventsBox.setState(false),
          ]);
        }
      },
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptCheckOutBtn,
    },
  };

  if (ticket.appointmentStatus) {
    const item = actionMap[appointmentStatus as AppointmentStatus];

    if (item) {
      return warpAsyncActionItem(item);
    }
  }

  return null;
};
