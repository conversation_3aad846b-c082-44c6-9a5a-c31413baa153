import { type Dispatchable, useDispatch, useSelector, useStore } from 'amos';
import ReactDOM from 'react-dom';
import { getMainCareType } from '../../../../components/PetAndServicePicker/utils/getMainCareType';
import { switchBusinessWithCheckWorkingLocation } from '../../../../store/business/business.actions';
import { currentBusinessIdBox } from '../../../../store/business/business.boxes';
import { apptDetailDrawerBox } from '../../../../store/calendarLatest/calendar.boxes';
import { useBusinessApplicableEvaluation } from '../../../../store/evaluation/evaluation.hooks';
import { getLodgingUnitList } from '../../../../store/lodging/actions/public/lodgingUnit.actions';
import { getAllBusinessBasicServiceInfoList } from '../../../../store/service/actions/public/service.actions';
import { selectAllServiceList } from '../../../../store/service/service.selectors';
import { getStaffList } from '../../../../store/staff/staff.actions';
import { selectBusinessStaffs } from '../../../../store/staff/staff.selectors';
import { isNormal } from '../../../../store/utils/identifier';
import { truly } from '../../../../store/utils/utils';
import { useCancelableCallback } from '../../../../utils/hooks/useCancelableCallback';
import { useSignNowMessageListener } from '../../../Agreement/hooks/useSignNowMessageListener';
import { useRepeatSeriesInit } from '../../components/RepeatSeries/hooks/useRepeatSeriesInit';
import { matchApptFlowScene } from '../../store/appt.options';
import { selectApptInfo } from '../../store/appt.selectors';
import { ApptFlowScene } from '../../store/appt.types';
import { isEvaluationAppt } from '../../store/appt.utils';
import { useApptDetailRouter } from './ApptDetailDrawer.router';
import { useTicketActions } from './hooks/useTicketActions';
import { useCheckTimeslotAvailability } from '../../../../query/calendar/timeslot';

export const useApptLifeCycles = (ticketId: string) => {
  const dispatch = useDispatch();
  const store = useStore();
  const drawerRouter = useApptDetailRouter();
  const [{ appointment, serviceItemTypes }, { loading }] = useSelector(selectApptInfo(ticketId), apptDetailDrawerBox);
  const { refreshTicket, prefetchTicket } = useTicketActions(Number(ticketId));

  const { fetchEvaluationList, currentEvaluation } = useBusinessApplicableEvaluation();

  const repeatId = isNormal(appointment.repeatId) ? Number(appointment.repeatId) : undefined;
  useRepeatSeriesInit(true, repeatId);

  const initData = useCancelableCallback(async (signal, appointmentId: string) => {
    const { onReady, serviceItemTypes } = store.select(apptDetailDrawerBox);
    const res = await prefetchTicket({ appointmentId, serviceItemTypes: serviceItemTypes || undefined }, signal);

    if (store.select(currentBusinessIdBox) !== Number(res.appointment.businessId)) {
      await dispatch(switchBusinessWithCheckWorkingLocation(res.appointment.businessId));
    }

    if (isEvaluationAppt(res.serviceItemTypes) && !currentEvaluation?.id) {
      fetchEvaluationList();
    }

    ReactDOM.unstable_batchedUpdates(() => {
      const isNeedLodging = matchApptFlowScene(ApptFlowScene.ApptLodging, getMainCareType(res.serviceItemTypes));

      const staffList = store.select(selectBusinessStaffs());
      const serviceList = store.select(selectAllServiceList());

      const actions: Array<Dispatchable> = [
        isNeedLodging && getLodgingUnitList({ businessId: res.appointment.businessId }),
        !serviceList.size && getAllBusinessBasicServiceInfoList(),
        !staffList.size && getStaffList(),
      ].filter(truly);

      dispatch(actions);

      setTimeout(() => {
        onReady?.({ router: drawerRouter });
      });
    });
  });

  const refreshData = useCancelableCallback(async (signal) => {
    await refreshTicket(signal);
  });

  // 监听 signNow 消息，刷新 drawer 数据
  useSignNowMessageListener(async () => {
    refreshData();
  });

  useCheckTimeslotAvailability();

  return {
    initData,
    refreshData,
    isLoading: initData.isBusy() || refreshData.isBusy() || loading /** refreshTicket */,
    isEvaluationAppt: isEvaluationAppt(serviceItemTypes),
    isApptDataPending: !serviceItemTypes.length, // 没有查到 appt 的数据，说明接口还没请求过，先阻止部分内容的渲染，因为还不知道是什么类型的 appt
    currentApptBusinessId: Number(appointment.businessId),
  };
};
