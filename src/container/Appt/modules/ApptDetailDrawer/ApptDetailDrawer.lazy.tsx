import { datadogRum } from '@datadog/browser-rum';
import { Spin } from '@moego/ui';
import React, { lazy, memo, Suspense, useEffect, useLayoutEffect, useMemo } from 'react';
import { PATH_GROOMING_CALENDAR, PATH_MAP_VIEW } from '../../../../router/paths';
import { globalEvent } from '../../../../utils/events/events';
import { useUnsavedConfirmGlobalV2 } from '../../../../utils/hooks/useUnsavedConfirmGlobalV2';
import { DataDogActionName } from '../../../../utils/logger';
import { CalendarDrawer } from '../../../Calendar/latest/ApptCalendar/components/CalendarDrawer';
import { useDraftConfirmConfig } from '../QuickAddAppt/hooks/useQuickAddLeaveConfirm';
import { type ApptDetailDrawerProps } from './ApptDetailDrawer';
import { ApptDetailRouteName, useApptDetailRouter } from './ApptDetailDrawer.router';
import { ReportActionName } from '../../../../utils/reportType';
import { reportData } from '../../../../utils/tracker';

const LazyComponent = lazy(() =>
  import('./ApptDetailDrawer').then(({ ApptDetailDrawer }) => ({
    default: ApptDetailDrawer,
  })),
);

export interface DrawerWrapperProps extends Pick<ApptDetailDrawerProps, 'onClose'> {}

export const DrawerLazyWrapper = memo((props: DrawerWrapperProps) => {
  const { onClose } = props;

  const drawerRouter = useApptDetailRouter();

  const { getDoubleConfirmConfig, asyncDoubleConfirm } = useDraftConfirmConfig();
  const { confirmConfig, isNeedDoubleConfirm, doubleConfirmTips } = useMemo(
    () => ({
      confirmConfig: getDoubleConfirmConfig(),
      isNeedDoubleConfirm: [ApptDetailRouteName.EditSchedule, ApptDetailRouteName.EvaluationReschedule].includes(
        drawerRouter.current.name,
      ),
      doubleConfirmTips: 'The appointment that you’re currently editing has unsaved changes.',
    }),
    [getDoubleConfirmConfig, drawerRouter],
  );

  const handleClose = () => {
    if (isNeedDoubleConfirm) {
      asyncDoubleConfirm({
        closable: true,
        onOk: () => Promise.resolve(),
        content: doubleConfirmTips,
        onCancel: (closeFn) => {
          if (closeFn?.triggerCancel) {
            return;
          }
          onClose?.();
        },
      });
      return;
    }
    onClose?.();
  };

  useUnsavedConfirmGlobalV2({
    noNeedConfirmPath: [PATH_GROOMING_CALENDAR.path, PATH_MAP_VIEW.path],
    alwaysRun: true,
    showConfirm: isNeedDoubleConfirm,
    modalProps: {
      ...confirmConfig,
      content: doubleConfirmTips,
      onConfirm: () => {
        return Promise.reject();
      },
      onCancel: () => {
        onClose?.();
        return Promise.resolve();
      },
    },
  });

  useLayoutEffect(() => {
    const params = { context: { type: 'appt' } };
    datadogRum.stopDurationVital(DataDogActionName.OPEN_APPT_DRAWER, params);

    const remove = globalEvent.closeApptDetail.on(() => {
      onClose?.();
    });

    return () => {
      datadogRum.stopDurationVital(DataDogActionName.CLOSE_APPT_DRAWER, params);

      remove();
    };
  }, []);

  useEffect(() => {
    reportData(ReportActionName.apptDrawerView);
    return () => {
      reportData(ReportActionName.apptDrawerLeave);
    };
  }, []);

  return (
    <CalendarDrawer size="l" isOpen onClose={handleClose}>
      <Suspense
        fallback={
          <div className="moe-w-full moe-h-full moe-flex moe-justify-center moe-items-center">
            <Spin isLoading />
          </div>
        }
      >
        <LazyComponent onClose={handleClose} />
      </Suspense>
    </CalendarDrawer>
  );
});
