import { useSelector } from 'amos';
import React, { useEffect, useMemo } from 'react';
import { BDOnlyTabs } from '../../../../store/calendarLatest/calendar.utils';
import { selectBDFeatureEnable } from '../../../../store/company/company.selectors';
import { ApptNavigationTabsV3 } from '../../../Calendar/latest/AwesomeCalendar.utils';
import { DrawerLeftNav } from '../../components/DrawerLeftNav/DrawerLeftNav';
import { matchApptFlowScene } from '../../store/appt.options';
import { selectMainServiceInAppt } from '../../store/appt.selectors';
import { ApptFlowScene } from '../../store/appt.types';
import { ApptDetailRouteName, useApptDetailRouter } from './ApptDetailDrawer.router';
import { reportData } from '../../../../utils/tracker';
import { ReportActionName } from '../../../../utils/reportType';

export interface ApptDetailNavProps {
  ticketId: string;
  className?: string;
}

export function ApptDetailNav(props: ApptDetailNavProps) {
  const { ticketId, className } = props;
  const [enableBD, { serviceItemType }] = useSelector(selectBDFeatureEnable, selectMainServiceInAppt(ticketId));
  const drawerRouter = useApptDetailRouter();

  const currentActiveNavTab = useMemo(() => {
    switch (drawerRouter.current.name) {
      case ApptDetailRouteName.FeedingMedication:
        return ApptNavigationTabsV3.FEEDING_AND_MEDICATION;
      case ApptDetailRouteName.PetBelongings:
        return ApptNavigationTabsV3.PET_BELONGINGS;
      case ApptDetailRouteName.Comment:
        return ApptNavigationTabsV3.TICKET_COMMENT;
      case ApptDetailRouteName.History:
        return ApptNavigationTabsV3.ACTIVITY_LOG;
      default:
        return ApptNavigationTabsV3.INFO;
    }
  }, [drawerRouter]);

  useEffect(() => {
    reportData(ReportActionName.apptDrawerNavTabView, {
      name: ApptNavigationTabsV3.mapLabels[currentActiveNavTab].label,
    });
  }, [currentActiveNavTab]);

  const handleChangeNavTab = (v: number) => {
    switch (v) {
      case ApptNavigationTabsV3.INFO:
        drawerRouter.go(ApptDetailRouteName.Home);
        break;
      case ApptNavigationTabsV3.TICKET_COMMENT:
        drawerRouter.go(ApptDetailRouteName.Comment);
        break;
      case ApptNavigationTabsV3.FEEDING_AND_MEDICATION:
        drawerRouter.go(ApptDetailRouteName.FeedingMedication);
        break;
      case ApptNavigationTabsV3.PET_BELONGINGS:
        drawerRouter.go(ApptDetailRouteName.PetBelongings);
        break;
      case ApptNavigationTabsV3.ACTIVITY_LOG:
        drawerRouter.go(ApptDetailRouteName.History);
        break;
    }
  };

  const options = useMemo(() => {
    return ApptNavigationTabsV3.values
      .filter((v) => {
        if (BDOnlyTabs.includes(v)) {
          return enableBD && matchApptFlowScene(ApptFlowScene.FeedingMedication, serviceItemType);
        }
        return true;
      })
      .map((tab) => {
        const isActive = tab === currentActiveNavTab;
        return {
          ...ApptNavigationTabsV3.mapLabels[tab],
          value: tab,
          isActive,
        };
      });
  }, [currentActiveNavTab, enableBD, serviceItemType]);

  return (
    <DrawerLeftNav className={className} options={options} value={currentActiveNavTab} onChange={handleChangeNavTab} />
  );
}
