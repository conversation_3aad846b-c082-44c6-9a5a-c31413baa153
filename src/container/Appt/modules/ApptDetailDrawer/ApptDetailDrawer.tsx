import { Spin } from '@moego/ui';
import { useSelector, useStore } from 'amos';
import React, { useEffect, useMemo } from 'react';
import { apptDetailDrawerBox } from '../../../../store/calendarLatest/calendar.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { BusinessIdProvider } from '../../../../utils/BusinessUtil';
import { ErrorToolTipGlobalStyle } from '../../../CreateTicket/components/CreateTicketV2.style';
import { QuickAddFeedMedication } from '../../components/EditPetFeedMedication/QuickAddFeedMedication';
import { ApptDrawerV3Style } from '../../style/apptDrawerStyle';
import { EvaluationApptInfoPanel } from '../EvaluationApptDetailDrawer/EvaluationApptInfoPanel';
import { EvaluationApptDetailAddNewPet } from '../EvaluationApptDetailDrawer/components/EvaluationApptDetailAddNewPet';
import { EvaluationApptEditPet } from '../EvaluationApptDetailDrawer/components/EvaluationApptEditPet';
import { EvaluationApptReschedule } from '../EvaluationApptDetailDrawer/components/EvaluationApptReschedule';
import { ApptActivityLog } from './ApptActivityLog/ApptActivityLog';
import { useApptLifeCycles } from './ApptDetailDrawer.hook';
import { ApptDetailNav } from './ApptDetailDrawer.nav';
import { ApptDetailRouteName, useApptDetailRouter } from './ApptDetailDrawer.router';
import { ApptDetailEvaluationInfoPanel } from './ApptDetailEvaluationInfoPanel';
import { ApptEditPet } from './ApptEditPet';
import { ApptEditPetHybrid } from './ApptEditPetHybrid';
import { ApptInfoPanel } from './ApptInfoPanel/ApptInfoPanel';
import { ApptCancelConfirmModal } from './ApptInfoPanel/ApptModals/ApptCancelConfirmModal';
import { ApptPrintCardModal } from './ApptInfoPanel/ApptModals/ApptPrintCardModal';
import { ApptReadyForPickupConfirmModal } from './ApptInfoPanel/ApptModals/ApptReadyForPickupConfirmModal';
import { ApptTakePaymentModal } from './ApptInfoPanel/ApptModals/ApptTakePaymentModal';
import { ApptToWaitingListModal } from './ApptInfoPanel/ApptModals/ApptToWaitingListModal';
import { SelectPetAndService } from './ApptInfoPanel/SelectPetAndService/SelectPetAndService';
import { ApptPetBelongings } from './ApptPetBelongings/ApptPetBelongings';
import { ApptReschedule } from './ApptReschedule/ApptReschedule';
import { ApptSelectServiceDetail } from './ApptSelectServiceDetail';
import { ApptTicketCommentPanel } from './ApptTicketCommentPanel/ApptTicketCommentPanel';
import { ApptToWaitList } from './ApptToWaitList/ApptToWaitList';
import { PreviewEditAppts } from './PreviewEditAppts';
import { ReportActionName } from '../../../../utils/reportType';
import { reportData } from '../../../../utils/tracker';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import { selectApptInfo } from '../../store/appt.selectors';

export interface ApptDetailDrawerProps {
  onClose?: () => void;
}

export function ApptDetailDrawer(props: ApptDetailDrawerProps) {
  const { onClose } = props;
  const store = useStore();
  const [{ ticketId }] = useSelector(apptDetailDrawerBox);

  const drawerRouter = useApptDetailRouter();

  const subPage = useMemo(() => {
    if (drawerRouter.is(ApptDetailRouteName.EditSchedule)) {
      // 编辑 ticket 的时间
      return <ApptReschedule />;
    }
    if (drawerRouter.is(ApptDetailRouteName.EditPetService)) {
      // 编辑 grooming only pet & service
      return <ApptEditPet />;
    }
    if (drawerRouter.is(ApptDetailRouteName.EditPetHybridService)) {
      // 编辑非 grooming only 的 pet & service
      return <ApptEditPetHybrid />;
    }
    if (drawerRouter.is(ApptDetailRouteName.SelectServiceDetail)) {
      // 非 grooming only 选择 pet service 之后，会进入这里
      return <ApptSelectServiceDetail />;
    }
    if (drawerRouter.is(ApptDetailRouteName.SelectPetService)) {
      // 新增 pet & service
      return <SelectPetAndService />;
    }
    if (drawerRouter.is(ApptDetailRouteName.EditPetEvaluation)) {
      // 编辑evaluation
      return <ApptDetailEvaluationInfoPanel />;
    }
    if (drawerRouter.is(ApptDetailRouteName.EvaluationEditPet)) {
      return <EvaluationApptEditPet />;
    }
    if (drawerRouter.is(ApptDetailRouteName.EvaluationReschedule)) {
      return <EvaluationApptReschedule />;
    }
    if (drawerRouter.is(ApptDetailRouteName.EvaluationSelectPetService)) {
      return <EvaluationApptDetailAddNewPet />;
    }
    return null;
  }, [drawerRouter]);

  const tabPage = useMemo(() => {
    if (drawerRouter.is(ApptDetailRouteName.Comment)) {
      return <ApptTicketCommentPanel ticketId={ticketId} />;
    }
    if (drawerRouter.is(ApptDetailRouteName.History)) {
      return <ApptActivityLog ticketId={ticketId} />;
    }
    if (drawerRouter.is(ApptDetailRouteName.PetBelongings)) {
      return <ApptPetBelongings />;
    }
    if (drawerRouter.is(ApptDetailRouteName.FeedingMedication)) {
      return <QuickAddFeedMedication appointmentId={String(ticketId)} goBack={drawerRouter.back} />;
    }
    return null;
  }, [drawerRouter, ticketId]);

  const { isLoading, initData, isApptDataPending, isEvaluationAppt, currentApptBusinessId, refreshData } =
    useApptLifeCycles(String(ticketId));

  useEffect(() => {
    isNormal(ticketId) && initData(String(ticketId));
  }, [ticketId]);

  useEffect(() => {
    if (isApptDataPending) return;
    const time = Date.now();
    const serviceItemTypes = store.select(selectApptInfo(String(ticketId))).serviceItemTypes;
    reportData(ReportActionName.apptDrawerView, { serviceItemTypes });
    return () => {
      reportData(ReportActionName.apptDrawerLeave, { duration: (Date.now() - time) / T_SECOND, serviceItemTypes });
    };
  }, [isApptDataPending]);

  return (
    // TODO(vision,p3) BusinessIdProvider 这个也是不好的设计来的，需要重构，这个可以在后续的pr中改
    <BusinessIdProvider value={currentApptBusinessId}>
      <Spin
        isLoading={isLoading}
        classNames={{
          base: 'moe-w-full moe-h-full',
          container: 'moe-relative moe-w-full moe-h-full moe-overflow-hidden',
        }}
      >
        {subPage && (
          <div className="moe-absolute moe-top-0 moe-left-0 moe-right-0 moe-bottom-0 moe-z-10 moe-bg-white moe-flex">
            {subPage}
          </div>
        )}

        <div className="moe-h-full moe-flex moe-flex-row moe-flex-1 moe-items-stretch moe-min-w-0">
          <ApptDetailNav ticketId={String(ticketId)} />

          <div className="moe-relative moe-flex-1 moe-h-full moe-flex moe-overflow-hidden">
            {tabPage && (
              <div className="moe-absolute moe-top-0 moe-left-0 moe-right-0 moe-bottom-0 moe-z-10 moe-bg-white moe-flex">
                {tabPage}
              </div>
            )}
            {/* 保护 home 页面在不断切换是不被重复创建销毁。一次 drawer 打开，Home 的渲染应该只有一次，减少例如 client 接口的重复请求 */}
            {isApptDataPending ? null : isEvaluationAppt ? (
              <EvaluationApptInfoPanel ticketId={ticketId} />
            ) : (
              <ApptInfoPanel ticketId={ticketId} />
            )}
          </div>
        </div>
      </Spin>

      <ErrorToolTipGlobalStyle />
      <ApptDrawerV3Style />

      <ApptCancelConfirmModal ticketId={ticketId} />
      <ApptPrintCardModal ticketId={ticketId} />
      <ApptToWaitingListModal ticketId={ticketId} />
      <ApptTakePaymentModal ticketId={ticketId} />
      <ApptReadyForPickupConfirmModal ticketId={ticketId} />
      {/* 当 bd appt 编辑时，新增 grooming 的 service，出现卡片预览和支持拖拽 */}
      <PreviewEditAppts appointmentId={ticketId} />
      <ApptToWaitList
        ticketId={ticketId}
        onFinish={(formState) => {
          if (formState?.deleteOriginalAppointment) {
            // add to waitlist 删除原有的 appointment 后，需要关闭当前的 drawer
            onClose?.();
          }
          refreshData?.();
        }}
      />
    </BusinessIdProvider>
  );
}
