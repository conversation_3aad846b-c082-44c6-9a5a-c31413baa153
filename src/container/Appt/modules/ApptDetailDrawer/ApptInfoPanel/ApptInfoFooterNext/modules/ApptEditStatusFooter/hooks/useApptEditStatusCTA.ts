import { AppointmentStatus } from '@moego/api-web/moego/models/appointment/v1/appointment_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type OrderModelAppointmentView } from '@moego/api-web/moego/models/order/v1/order_models';
import { useDispatch, useSelector, useStore } from 'amos';
import { getMainCareType } from '../../../../../../../../../components/PetAndServicePicker/utils/getMainCareType';
import { ApptTestIds } from '../../../../../../../../../config/testIds/apptDrawer';
import { selectCurrentPermissions } from '../../../../../../../../../store/business/role.selectors';
import {
  ReadyForPickupConfirmModalMode,
  calendarLoadingEventsBox,
} from '../../../../../../../../../store/calendarLatest/calendar.boxes';
import { bookingTableLoadingEventsBox } from '../../../../../../../../../store/grooming/grooming.boxes';
import { overviewLoadingEventsBox } from '../../../../../../../../../store/overview/overview.boxes';
import { isNormal } from '../../../../../../../../../store/utils/identifier';
import { useBusinessIsWorkingLocation } from '../../../../../../../../../utils/BusinessUtil';
import { PaymentActionName } from '../../../../../../../../../utils/reportData/payment';
import { ReportActionName } from '../../../../../../../../../utils/reportType';
import { reportData } from '../../../../../../../../../utils/tracker';
import { useChangeTicketStatus } from '../../../../../../../../Calendar/Grooming/GroomingTicketModal/hooks/useChangeTicketStatus';
import { useInvoiceReinvent } from '../../../../../../../../PaymentFlow/hooks/useInvoiceReinvent';
import { useInvoiceReinventReport } from '../../../../../../../../PaymentFlow/hooks/useInvoiceReinvent.report';
import { useTakePayment } from '../../../../../../../../PaymentFlow/TakePaymentDrawer/useTakePayment';
import { useViewInvoiceDrawer } from '../../../../../../../../PaymentFlow/ViewInvoiceDrawer/useViewInvoiceDrawer';
import { useBeforeCheckoutActionsForInvoiceV4 } from '../../../../../../../components/ApptCheckInOutAlert/hooks/useBeforeCheckoutActionsForInvoiceV4';
import { useConfirmCheckOut } from '../../../../../../../components/ApptCheckInOutAlert/hooks/useConfirmCheckOut';
import { type ActionItem } from '../../../../../../../components/types';
import { useEvaluationConfirmCheckOut } from '../../../../../../../hooks/useEvaluationConfirmCheckOut';
import { useEvaluationTicketActions } from '../../../../../../../hooks/useEvaluationTicketActions';
import { type useApptDaycareExceedDurationList } from '../../../../../../../hooks/useGetDaycareExceedDurationList';
import { useTakeApptCheckout } from '../../../../../../../hooks/useTakeApptCheckout';
import { useWrapAsyncActionItem } from '../../../../../../../hooks/useWrapAsyncActionItem';
import { getAppointment } from '../../../../../../../store/appt.api';
import { type ApptInfo } from '../../../../../../../store/appt.boxes';
import { selectApptInfo, selectMainServiceInAppt } from '../../../../../../../store/appt.selectors';
import { reportCheckInScene } from '../../../../../../../utils/apptReport';
import { type ApptCTAScene } from '../../../../../../../utils/types';
import { useBookAgainFromTicket } from '../../../../../hooks/useBookAgainFromTicket';
import { useFutureApptCheckoutAlert } from '../../../../../hooks/useFutureApptCheckoutAlert';
import { useRenderCheckoutExceedDuration } from '../../../../../hooks/useRenderCheckoutExceedDuration';
import { useTicketActions } from '../../../../../hooks/useTicketActions';
import { getUnsettledOrders, hasOriginalOrder } from '../../../../../utils';

export interface ApptEditStatusCTAParams {
  appointmentId: string;
  appointmentStatus: AppointmentStatus;
  apptCustomerInfoIsDeleted: ApptInfo['customer']['customerProfile']['deleted'];
  petExceedDurationList: ReturnType<typeof useApptDaycareExceedDurationList>;
  serviceItemTypes?: ServiceItemType[];
  orderId?: number | string;
  orders?: OrderModelAppointmentView[];
  apptCTAScene: ApptCTAScene;
  isOverpaid?: boolean;
}

export function getNextActionNameInNewInvoice(isNewOrderV4Flow: boolean) {
  return {
    CHECK_IN: 'Check in',
    MARK_AS_READY: 'Mark as ready',
    CHECK_OUT: isNewOrderV4Flow ? 'Start check out' : 'Check out',
    BOOK_AGAIN: 'Book again',
    CHARGE_NOW: 'Charge now',
    REFUND_OVERPAYMENT: 'Refund overpayment',
  };
}

export const useApptEditStatusCTA = (params: ApptEditStatusCTAParams) => {
  const {
    appointmentId,
    appointmentStatus,
    apptCustomerInfoIsDeleted: groomingCustomerInfoIsDeleted,
    petExceedDurationList,
    serviceItemTypes,
    orderId,
    orders,
    apptCTAScene,
    isOverpaid,
  } = params;
  const dispatch = useDispatch();
  const reportPaymentData = useInvoiceReinventReport();
  const { isNewOrderV4Flow } = useInvoiceReinvent();

  const unsettledOrder = getUnsettledOrders(orders ?? [])[0];
  const showChargeNowWithCancelled =
    isNewOrderV4Flow && appointmentStatus === AppointmentStatus.CANCELED && !!unsettledOrder;
  const showChargeNowWithFinished =
    isNewOrderV4Flow &&
    appointmentStatus === AppointmentStatus.FINISHED &&
    (!hasOriginalOrder(orders ?? []) || !!unsettledOrder);

  const openViewInvoiceDrawer = useViewInvoiceDrawer();

  /**
   * is day care or boarding appointment
   * serviceItemTypes 包含 DAYCARE 或 BOARDING 或主 service 是 DAYCARE 或 BOARDING
   */
  const [mainService] = useSelector(selectMainServiceInAppt(appointmentId));
  const isBDAppt =
    serviceItemTypes?.includes(ServiceItemType.BOARDING) ||
    serviceItemTypes?.includes(ServiceItemType.DAYCARE) ||
    [ServiceItemType.BOARDING, ServiceItemType.DAYCARE].includes(mainService.serviceItemType);

  const isEvaluationAppt =
    !!serviceItemTypes?.length && serviceItemTypes?.every((item) => item === ServiceItemType.EVALUATION);
  const evaluationConfirmCheckOut = useEvaluationConfirmCheckOut({
    appointmentId,
  });

  // permissions
  const [permissions] = useSelector(selectCurrentPermissions);
  const canProcessPayment = permissions.has('canProcessPayment');

  // isActionDisabled
  const isWorkingLocation = useBusinessIsWorkingLocation();
  const isActionDisabled = !isWorkingLocation;

  // appointment actions
  const {
    refreshTicket: refreshNormalTicket,
    setTakePaymentModal: setNormalTakePaymentModal,
    setReadyForPickupModal: setNormalReadyForPickupModal,
  } = useTicketActions(Number(appointmentId));
  const {
    refreshTicket: refreshEvaluationTicket,
    setTakePaymentModal: setEvaluationTakePaymentModal,
    setReadyForPickupModal: setEvaluationReadyForPickupModal,
  } = useEvaluationTicketActions(Number(appointmentId));
  const bookAgain = useBookAgainFromTicket();
  const takeApptCheckout = useTakeApptCheckout();
  const NextActionName = getNextActionNameInNewInvoice(isNewOrderV4Flow);

  const refreshTicket = isEvaluationAppt ? refreshEvaluationTicket : refreshNormalTicket;
  const setTakePaymentModal = isEvaluationAppt ? setEvaluationTakePaymentModal : setNormalTakePaymentModal;
  const setReadyForPickupModal = isEvaluationAppt ? setEvaluationReadyForPickupModal : setNormalReadyForPickupModal;
  const store = useStore();

  const handleChangeTicketStatus = useChangeTicketStatus(() => refreshTicket());
  const futureApptAlert = useFutureApptCheckoutAlert(appointmentId);
  const exceedDuration = useRenderCheckoutExceedDuration(petExceedDurationList);
  const { runRequiredActions } = useBeforeCheckoutActionsForInvoiceV4();
  const handleConfirmCheckout = useConfirmCheckOut({
    appointmentId,
    // TODO: 考虑非当天报错的情况
    renderAlertBefore: futureApptAlert ? () => futureApptAlert : undefined,
    renderAlertAfter: petExceedDurationList?.length ? exceedDuration : undefined,
  });
  const { requestTakePayment } = useTakePayment();
  const { warpAsyncActionItem } = useWrapAsyncActionItem();

  const handleMarkAsReady = async () => {
    reportData(ReportActionName.markAsReadyClick);
    return setReadyForPickupModal(true, ReadyForPickupConfirmModalMode.ManualSendMessage);
  };

  const handleCheckoutPayment = async () => {
    const handlePayment = () => {
      if (canProcessPayment) {
        setTakePaymentModal(true, { isCheckout: true });
      }
    };

    if (isNewOrderV4Flow) {
      if (isEvaluationAppt) {
        // 这里是个 double confirm，并不是真正的 checkout
        const { next } = await evaluationConfirmCheckOut();
        if (!next) return;
      }

      // 这里是个 double confirm，并不是真正的 checkout
      const { next, extra } = await handleConfirmCheckout();
      if (!next) return;
      let appt = store.select(selectApptInfo(appointmentId)).appointment;
      // NOTE: 隐式依赖了 appt 了，但是代码流程里面能够保障这个值存在.
      // 理论上不会进入，只是保险起见避免极端情况防御获取
      if (!isNormal(appt.id) && isNormal(appointmentId)) {
        await dispatch(getAppointment({ appointmentId }));
        appt = store.select(selectApptInfo(appointmentId)).appointment;
      }

      await runRequiredActions({
        appointmentId,
        orderId: orders?.[0]?.id ?? '',
        isOrderVersionV4: appt.isNewOrder,
        checkoutDate: extra?.checkoutDate,
      });

      await takeApptCheckout({ apptId: appointmentId, orders: orders ?? [] });
      await refreshTicket();
      return;
    }

    if (isEvaluationAppt) {
      const { next } = await evaluationConfirmCheckOut();
      if (next) {
        await handleChangeTicketStatus(+appointmentId, AppointmentStatus.FINISHED);
        handlePayment();
      }
      return;
    }

    const { next, extra } = await handleConfirmCheckout();
    if (next) {
      await handleChangeTicketStatus(+appointmentId, AppointmentStatus.FINISHED, extra);
      handlePayment();
    }
  };

  const handleCheckout = async () => {
    dispatch([
      calendarLoadingEventsBox.setState(true),
      overviewLoadingEventsBox.setState(true),
      bookingTableLoadingEventsBox.setState(true),
    ]);

    try {
      await handleCheckoutPayment();
    } finally {
      dispatch([
        calendarLoadingEventsBox.setState(false),
        overviewLoadingEventsBox.setState(false),
        bookingTableLoadingEventsBox.setState(false),
      ]);

      reportPaymentData(PaymentActionName.CheckOut, {
        orderId,
      });
    }
  };

  const handleReportCheckIn = () => {
    const finishReportCheckIn = reportCheckInScene({
      apptCTAScene,
      mainServiceItemType: serviceItemTypes?.length ? getMainCareType(serviceItemTypes) : mainService.serviceItemType,
    });
    return finishReportCheckIn;
  };

  const showChargeNowWithFinishedWrapper = (config: ActionItem): ActionItem => {
    if (!showChargeNowWithFinished) return config;

    return {
      label: NextActionName.CHARGE_NOW,
      onClick: async () => {
        await refreshTicket();
        if (unsettledOrder) {
          await requestTakePayment({
            invoiceId: +unsettledOrder.id,
            module: 'grooming',
          });
        } else {
          await requestTakePayment({
            appointmentId: +appointmentId,
            module: 'grooming',
            invoiceId: 0,
          });
        }
        await refreshTicket();
      },
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptChargeNowBtn,
    };
  };

  const showChargeNowWithCancelledWrapper = (config: ActionItem): ActionItem => {
    if (!showChargeNowWithCancelled) return config;

    return {
      label: NextActionName.CHARGE_NOW,
      onClick: async () => {
        await refreshTicket();
        await requestTakePayment({
          invoiceId: +unsettledOrder.id,
          module: 'grooming',
        });
        await refreshTicket();
      },
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptChargeNowBtn,
    };
  };

  const nextActionMap = {
    [AppointmentStatus.UNCONFIRMED]: {
      label: NextActionName.CHECK_IN,
      onClick: async (params?) => {
        const finishReportCheckIn = handleReportCheckIn();
        const { extra, needRefreshTicket } = params?.checkInParams ?? {};
        await handleChangeTicketStatus(+appointmentId, AppointmentStatus.CHECKED_IN, extra, needRefreshTicket);
        finishReportCheckIn?.();
      },
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptCheckInBtn,
    },
    [AppointmentStatus.CONFIRMED]: {
      label: NextActionName.CHECK_IN,
      onClick: async (params?) => {
        const finishReportCheckIn = handleReportCheckIn();
        const { extra, needRefreshTicket } = params?.checkInParams ?? {};
        await handleChangeTicketStatus(+appointmentId, AppointmentStatus.CHECKED_IN, extra, needRefreshTicket);
        finishReportCheckIn?.();
      },
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptCheckInBtn,
    },
    [AppointmentStatus.CHECKED_IN]: {
      label: isBDAppt ? NextActionName.CHECK_OUT : NextActionName.MARK_AS_READY,
      onClick: async () => {
        if (isBDAppt) {
          await handleCheckout();
          return;
        }
        await handleMarkAsReady();
      },
      disabled: isActionDisabled,
      testId: isBDAppt ? 'appt-check-out-btn' : 'appt-mark-as-ready-btn',
    },
    [AppointmentStatus.READY]: {
      label: NextActionName.CHECK_OUT,
      onClick: async () => {
        await handleCheckout();
      },
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptCheckOutBtn,
    },
    [AppointmentStatus.FINISHED]: showChargeNowWithFinishedWrapper({
      label: NextActionName.BOOK_AGAIN,
      onClick: async () => {
        await bookAgain();
      },
      disabled: !!groomingCustomerInfoIsDeleted,
      testId: ApptTestIds.ApptBookAgainBtn,
    }),
    [AppointmentStatus.CANCELED]: showChargeNowWithCancelledWrapper({
      label: NextActionName.BOOK_AGAIN,
      onClick: async () => {
        await bookAgain();
      },
      disabled: !!groomingCustomerInfoIsDeleted,
      testId: ApptTestIds.ApptBookAgainBtn,
    }),
  } satisfies {
    [key in AppointmentStatus]?: ActionItem;
  };

  if (isOverpaid && orders?.[0]?.id) {
    return warpAsyncActionItem({
      label: NextActionName.REFUND_OVERPAYMENT,
      onClick: async () => {
        openViewInvoiceDrawer({ invoiceId: orders[0].id });
      },
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptRefundOverpaymentBtn,
    });
  }

  // order v4 之前，Evaluation 完成，只有 Take payment 操作，按钮 CTA 不出现
  // order v4 之后，Evaluation 完成，如果存在未结算订单，则展示 Charge now 操作，否则按钮 CTA 不出现
  const isEvaluationFinishedOrCancelled =
    isEvaluationAppt && [AppointmentStatus.FINISHED, AppointmentStatus.CANCELED].includes(appointmentStatus);

  if (
    appointmentStatus &&
    !!nextActionMap[appointmentStatus] &&
    (!isEvaluationFinishedOrCancelled || showChargeNowWithCancelled || showChargeNowWithFinished)
  ) {
    const nextAction = nextActionMap[appointmentStatus];

    return nextAction ? warpAsyncActionItem(nextAction) : null;
  }

  // 其余情况不显示 CTA
  return null;
};
