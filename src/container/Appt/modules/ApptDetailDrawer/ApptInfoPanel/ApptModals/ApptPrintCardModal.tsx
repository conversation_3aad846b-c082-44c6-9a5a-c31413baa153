import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { useRouteMatch } from 'react-router';
import { ClientInfoRender } from '../../../../../../components/ClientPicker/ClientInfoRender';
import { ReportModule } from '../../../../../../components/ErrorBoundary/types';
import { withErrorBoundary } from '../../../../../../components/ErrorBoundary/withErrorBoundary';
import { MultiTypePrintModal } from '../../../../../../components/MultiTypePrintModal/MultiTypePrintModal';
import { PrintCardType } from '../../../../../../components/MultiTypePrintModal/MultiTypePrintModal.options';
import { PATH_HOME_OVERVIEW, PATH_LODGING_CALENDAR } from '../../../../../../router/paths';
import { apptDetailModalStatusBox } from '../../../../../../store/calendarLatest/calendar.boxes';
import { matchApptFlowScene } from '../../../../store/appt.options';
import { selectApptInfo, selectMainServiceInAppt } from '../../../../store/appt.selectors';
import { ApptFlowScene } from '../../../../store/appt.types';
import { useTicketActions } from '../../hooks/useTicketActions';

export interface ApptPrintCardModalProps {
  ticketId: number;
}

const ApptPrintCardModalComponent = memo(({ ticketId }: ApptPrintCardModalProps) => {
  const [ticket, { printCardModalVisible }, { serviceItemType }] = useSelector(
    selectApptInfo(String(ticketId)),
    apptDetailModalStatusBox,
    selectMainServiceInAppt(String(ticketId)),
  );
  const { setPrintCardModalVisible } = useTicketActions();
  const isOverview = useRouteMatch(PATH_HOME_OVERVIEW.path);
  const inLodgingOrOverviewPage = useRouteMatch(PATH_LODGING_CALENDAR.path) || isOverview;

  const cardTypeOptions = useMemo(() => {
    const BDCardTypeOptions = inLodgingOrOverviewPage
      ? [PrintCardType.Stay, PrintCardType.Appt]
      : [PrintCardType.Appt, PrintCardType.Stay];

    return BDCardTypeOptions.filter((v) =>
      v === PrintCardType.Stay ? matchApptFlowScene(ApptFlowScene.StayCard, serviceItemType) : true,
    );
  }, [serviceItemType, inLodgingOrOverviewPage]);

  return (
    <ClientInfoRender clientId={Number(ticket.appointment.customerId)} preventAutoReload>
      {() => {
        return (
          <MultiTypePrintModal
            from="apptDrawer"
            cardTypeOptions={cardTypeOptions}
            isOpen={printCardModalVisible}
            groomingId={ticketId}
            onClose={() => setPrintCardModalVisible(false)}
          />
        );
      }}
    </ClientInfoRender>
  );
});

export const ApptPrintCardModal = withErrorBoundary(ApptPrintCardModalComponent, {
  reportModule: ReportModule.PrintCard,
});
