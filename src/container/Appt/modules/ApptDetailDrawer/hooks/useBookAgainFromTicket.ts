import { AppointmentStatus } from '@moego/api-web/moego/models/appointment/v1/appointment_enums';
import { AppointmentNoteType } from '@moego/api-web/moego/models/appointment/v1/appointment_note_enums';
import { useDispatch, useStore } from 'amos';
import dayjs from 'dayjs';
import { pick } from 'lodash';
import { useHistory, useRouteMatch } from 'react-router';
import { toastApi } from '../../../../../components/Toast/Toast';
import { PATH_GROOMING_CALENDAR, PATH_HOME_OVERVIEW, PATH_LODGING_CALENDAR } from '../../../../../router/paths';
import { switchBusiness } from '../../../../../store/business/business.actions';
import { currentBusinessIdBox } from '../../../../../store/business/business.boxes';
import { selectCurrentPermissions } from '../../../../../store/business/role.selectors';
import { apptDetailDrawerBox } from '../../../../../store/calendarLatest/calendar.boxes';
import { QuickAddSource, ViewType } from '../../../../../store/calendarLatest/calendar.types';
import { getSavedPriceList } from '../../../../../store/pet/petSavedPrice.actions';
import { getAllBusinessBasicServiceInfoList } from '../../../../../store/service/actions/public/service.actions';
import { selectAllActiveServiceIdList } from '../../../../../store/service/service.selectors';
import { isNormal } from '../../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { permissionAlertAsync } from '../../../../../utils/message';
import { useAutoFillAddOn, useAutoFillService } from '../../../hooks/useAutoFill';
import { useCalcGroomingOnlySchedule } from '../../../hooks/useCalcGroomingOnlySchedule';
import { getLodgingIds, useCheckConflictAlert } from '../../../hooks/useCheckConflictAlert';
import { useCreateApptDrawer } from '../../../hooks/useCreateApptDrawer';
import { transformToApptServices, useLastApptPetAndService } from '../../../hooks/useLastApptPetAndService';
import { useResolveStaff } from '../../../hooks/useResolveStaff';
import { getAppointment } from '../../../store/appt.api';
import { type ApptInfo, apptPetMapBox } from '../../../store/appt.boxes';
import { matchApptFlowScene } from '../../../store/appt.options';
import { selectApptInfo, selectMainServiceInAppt } from '../../../store/appt.selectors';
import { ApptFlowScene, CreateApptId } from '../../../store/appt.types';
import { reportData } from '../../../../../utils/tracker';
import { ReportActionName } from '../../../../../utils/reportType';

export function useBookAgainFromTicket() {
  const store = useStore();
  const dispatch = useDispatch();
  const inCalendarRoute = useRouteMatch(PATH_GROOMING_CALENDAR.path);
  const inLodgingRoute = useRouteMatch(PATH_LODGING_CALENDAR.path);
  const inOverviewRoute = useRouteMatch(PATH_HOME_OVERVIEW.path);
  const checkConflictAlert = useCheckConflictAlert();
  const history = useHistory();

  const autoFillAddOn = useAutoFillAddOn();
  const autoFillService = useAutoFillService();
  const calcGroomingOnlySchedule = useCalcGroomingOnlySchedule();
  const getFilterServices = useLastApptPetAndService();
  const { getAvailableStaff } = useResolveStaff();
  const openCreateDrawer = useCreateApptDrawer();

  const checkConflict = async (ticket: ApptInfo, filterServices: ReturnType<typeof getFilterServices>) => {
    const pets = ticket.serviceDetail.map((s) => s.pet.id);
    const {
      appointment: { status },
      customerId,
    } = ticket;
    const lodgingUnitIds = filterServices.reduce((prev, curr) => {
      return prev.concat(curr.services.map((s) => getLodgingIds(s.serviceDetail.lodgingId, s.splitLodgings)).flat());
    }, [] as string[]);
    const { startDate, endDate } = filterServices
      .reduce(
        (prev, curr) => {
          const { services, addOns, evaluations } = curr;
          return prev.concat(
            services
              .map(({ serviceDetail }) => pick(serviceDetail, ['startDate', 'endDate']))
              .concat(addOns.map(({ serviceDetail }) => pick(serviceDetail, ['startDate', 'endDate'])))
              .concat(evaluations.map((e) => pick(e, ['startDate', 'endDate']))),
          );
        },
        [] as { startDate: string; endDate: string }[],
      )
      .reduce(
        (prev, curr) => {
          const { startDate, endDate } = curr;
          return {
            startDate: dayjs(startDate).isBefore(prev.startDate, 'day') ? startDate : prev.startDate,
            endDate: dayjs(endDate).isAfter(prev.endDate, 'day') ? endDate : prev.endDate,
          };
        },
        {
          startDate: dayjs().format(DATE_FORMAT_EXCHANGE),
          endDate: dayjs().format(DATE_FORMAT_EXCHANGE),
        },
      );
    if (status !== AppointmentStatus.FINISHED) {
      await checkConflictAlert({
        appointmentId: CreateApptId,
        petIds: pets,
        lodgingUnitIds,
        startDateStr: startDate,
        endDateStr: endDate,
        showCancel: false,
        confirmText: 'Got it',
        clientId: String(customerId),
      });
    }
  };

  return useSerialCallback(async (outTicketId?: number) => {
    reportData(ReportActionName.apptBookAgainClick);
    const permissions = store.select(selectCurrentPermissions());
    await permissionAlertAsync(permissions, 'createAppointment');

    const finalTicketId = isNormal(outTicketId) ? outTicketId : store.select(apptDetailDrawerBox).ticketId;
    const activeIdList = store.select(selectAllActiveServiceIdList());
    const [availableStaffs] = await Promise.all([
      getAvailableStaff(),
      dispatch(getAppointment({ appointmentId: String(finalTicketId) })),
      !activeIdList.size && dispatch(getAllBusinessBasicServiceInfoList()),
    ]);
    const ticket = store.select(selectApptInfo(String(finalTicketId)));
    const {
      notes,
      appointment: { colorCode, startAtSameTime },
      customerId,
    } = ticket;

    const filterServices = getFilterServices({
      appointment: ticket.appointment,
      serviceDetail: ticket.serviceDetail,
      serviceItemTypes: ticket.serviceItemTypes,
      availableStaffs,
    });
    const filterPets = await transformToApptServices(filterServices);

    if (!filterPets.length) {
      return toastApi.error('Book again failed, pets or services in this appointment are no longer available.');
    }

    await checkConflict(ticket, filterServices);

    await dispatch(switchBusiness(store.select(currentBusinessIdBox)));

    const originApptId = ticket.appointment.id;
    const newApptId = CreateApptId;
    const pets = ticket.serviceDetail.map((s) => s.pet.id);

    await Promise.all(pets.map((id) => dispatch(getSavedPriceList(Number(id)))));

    // 在开启drawer 之前，先准备好数据，这里面隐式依赖了当前 appt 的数据
    // 如果 drawer 关闭的话，我们会清空当前 appt 的数据的，这里的清理只要是为了方便，避免有残留数据
    const actions = filterServices
      .map(({ pet, services, addOns }) => {
        const params = { petId: pet.id, originApptId, newApptId, availableStaffs };
        return [
          ...services.map((s) => {
            return autoFillService({ ...params, originService: s });
          }),
          ...addOns.map((s) => {
            return autoFillAddOn({ ...params, originService: s });
          }),
        ];
      })
      .flat();

    const alertNotes = notes.find((note) => note.type === AppointmentNoteType.ALERT_NOTES)?.note || '';
    openCreateDrawer({
      params: {
        source: QuickAddSource.BookAgain,
        clientId: +customerId,
        disableViewClientLastAppt: true,
        allPetsStartAtSameTime: startAtSameTime,
        alertNotes,
        colorCode,
      },
      async onReady() {
        // 打开drawer 后，把数据 merge，记得先 merge service 再 merge pet，pet 是父，service 是子. 先子后父
        await Promise.all(dispatch(actions));
        await dispatch(apptPetMapBox.mergeItem(newApptId, { appointmentId: newApptId, pets: filterPets }));

        const { serviceItemType } = store.select(selectMainServiceInAppt(newApptId));
        if (matchApptFlowScene(ApptFlowScene.AutoCalc, serviceItemType)) {
          await calcGroomingOnlySchedule({
            allPetsStartAtSameTime: startAtSameTime,
            customerId: customerId,
            appointmentId: newApptId,
          });
        }
        if (!inCalendarRoute && !inOverviewRoute && !inLodgingRoute) {
          // 这个逻辑我认为不对，book again 应该根据appt 的类型来跳不同的路由，而不是都到 staff calendar
          history.push(PATH_GROOMING_CALENDAR.stated({ backCalendarView: ViewType.DAY }));
        }
      },
    });
  });
}
