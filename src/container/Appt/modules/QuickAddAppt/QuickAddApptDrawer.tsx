import React, { useEffect, useMemo } from 'react';
import { <PERSON><PERSON>erProvider } from '../../../../layout/components/ScrollerProvider';
import { useInitBusinessApplicableEvaluation } from '../../../../store/evaluation/evaluation.hooks';
import { QuickAddFeedMedication } from '../../components/EditPetFeedMedication/QuickAddFeedMedication';
import { useRepeatSeriesInit } from '../../components/RepeatSeries/hooks/useRepeatSeriesInit';
import { CreateApptId } from '../../store/appt.types';
import { ApptTimeReschedule } from './ApptTimeReSchedule/ApptTimeReSchedule';
import { CommentsCreate } from './Comments/CommentsCreate';
import { EditEvaluationInfoPanelCreate } from './EditEvaluationInfoPanelCreate';
import { EditPetServiceHybrid } from './EditPetAndService/EditPetServiceHybrid';
import { EditPetServiceNormal } from './EditPetAndService/EditPetServiceNormal';
import { useQuickAddConfig } from './hooks/useQuickAddConfig';
import { useQuickAddFetchData } from './hooks/useQuickAddFetchData';
import { type SubmitQuickAddParams } from './hooks/useSubmitQuickAdd';
import { PreviewQuickAddAppts } from './PreviewQuickAddAppts';
import {
  CreateApptRouteName,
  createApptRouter,
  DrawerRouterProvider,
  useCreateApptRouter,
  type DrawerRouterType,
} from './QuickAddApptDrawer.router';
import { QuickAddFooter, type QuickAddFooterProps } from './QuickAddFooter';
import { QuickAddLeftNav } from './QuickAddLeftNav';
import { QuickAddPetBelongings } from './QuickAddPetBelongings';
import { SelectServiceDetailCreate } from './SelectServiceDetailCreate';
import { StepHome } from './StepHome';
import { StepSelectMultiplePets } from './StepSelectMultiplePets/StepSelectMultiplePets';
import { reportData } from '../../../../utils/tracker';
import { ReportActionName } from '../../../../utils/reportType';

export interface QuickAddApptDrawerProps
  extends Pick<SubmitQuickAddParams, 'onCreated'>,
    Pick<QuickAddFooterProps, 'onClose'> {
  onReady?: (v: { router: DrawerRouterType | null; close: () => void }) => void;
}

export function QuickAddApptDrawer(props: QuickAddApptDrawerProps) {
  const { onCreated, onClose, onReady } = props;
  const [{ clientId }] = useQuickAddConfig();

  useQuickAddFetchData();
  useRepeatSeriesInit(true);
  useInitBusinessApplicableEvaluation(true);

  const drawerRouter = useCreateApptRouter();
  createApptRouter.set(drawerRouter); // 供外部无上下文时访问

  useEffect(() => {
    onReady?.({
      get router() {
        return createApptRouter.current;
      },
      close() {
        onClose?.();
      },
    });
    return () => {
      createApptRouter.set(null);
    };
  }, []);

  const subPage = useMemo(() => {
    if (drawerRouter.is(CreateApptRouteName.EditSchedule)) {
      return <ApptTimeReschedule />;
    }
    if (drawerRouter.is(CreateApptRouteName.SelectPetService)) {
      return <StepSelectMultiplePets />;
    }
    if (drawerRouter.is(CreateApptRouteName.EditPetService)) {
      return <EditPetServiceNormal />;
    }
    if (drawerRouter.is(CreateApptRouteName.EditPetHybridService)) {
      return <EditPetServiceHybrid />;
    }
    if (drawerRouter.is(CreateApptRouteName.SelectServiceDetail)) {
      return <SelectServiceDetailCreate />;
    }
    if (drawerRouter.is(CreateApptRouteName.EditPetEvaluation)) {
      return <EditEvaluationInfoPanelCreate />;
    }
    return null;
  }, [drawerRouter]);

  useEffect(() => {
    reportData(ReportActionName.quickAddApptFlow, {
      step: drawerRouter.current.name,
    });
  }, [drawerRouter?.current?.name]);

  const tabPage = useMemo(() => {
    if (drawerRouter.is(CreateApptRouteName.Comment)) {
      return <CommentsCreate clientId={clientId} />;
    }
    if (drawerRouter.is(CreateApptRouteName.PetBelongings)) {
      return <QuickAddPetBelongings />;
    }
    if (drawerRouter.is(CreateApptRouteName.FeedingMedication)) {
      return <QuickAddFeedMedication appointmentId={CreateApptId} goBack={drawerRouter.back} />;
    }
    return null;
  }, [drawerRouter, clientId]);

  return (
    <DrawerRouterProvider value={drawerRouter}>
      <div className="moe-relative moe-w-full moe-h-full moe-overflow-hidden">
        {subPage && (
          <div className="moe-absolute moe-top-0 moe-left-0 moe-right-0 moe-bottom-0 moe-z-10 moe-bg-white moe-flex">
            {subPage}
          </div>
        )}

        <div className="moe-h-full moe-flex moe-flex-row moe-flex-1 moe-items-stretch moe-min-w-0">
          <QuickAddLeftNav />

          <div className="moe-relative moe-flex-1 moe-h-full moe-flex moe-overflow-hidden">
            {tabPage && (
              <div className="moe-absolute moe-top-0 moe-left-0 moe-right-0 moe-bottom-0 moe-z-10 moe-bg-white moe-flex">
                {tabPage}
              </div>
            )}
            {/* 保护 home 页面在不断切换是不被重复创建销毁。一次 drawer 打开，Home 的渲染应该只有一次，减少例如 client 接口的重复请求 */}
            <ScrollerProvider className="moe-flex-1 moe-h-full moe-min-w-0 moe-flex moe-flex-col">
              <StepHome>
                {/*
                  为什么这个特殊判断显隐
                  是footer有计算金额的逻辑，当用户在其他页面改变 pet service 时，不想让它重新计算
                  当然也可以在对应的 hook 里面去判断
                */}
                {drawerRouter.is(CreateApptRouteName.Home) && (
                  <QuickAddFooter onCreated={onCreated} onClose={onClose} />
                )}
              </StepHome>
            </ScrollerProvider>
          </div>
        </div>
      </div>
      <PreviewQuickAddAppts />
    </DrawerRouterProvider>
  );
}
