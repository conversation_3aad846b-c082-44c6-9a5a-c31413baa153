import { type Action, useDispatch, useSelector, useStore } from 'amos';
import { useEffect } from 'react';
import { currentBusinessIdBox } from '../../../../../store/business/business.boxes';
import { getLodgingUnitList } from '../../../../../store/lodging/actions/public/lodgingUnit.actions';
import { selectLodgingUnitIdListWithoutCategory } from '../../../../../store/lodging/lodgingUnit.selectors';
import { getPetCodeList } from '../../../../../store/pet/petCode.actions';
import { getAllBusinessBasicServiceInfoList } from '../../../../../store/service/actions/public/service.actions';
import { selectAllServiceList } from '../../../../../store/service/service.selectors';
import { getBusinessServiceArea } from '../../../../../store/serviceArea/serviceArea.actions';
import { selectBusinessServiceAreaIdList } from '../../../../../store/serviceArea/serviceArea.selectors';
import { getStaffList } from '../../../../../store/staff/staff.actions';
import { selectBusinessStaffs } from '../../../../../store/staff/staff.selectors';
import { isNormal } from '../../../../../store/utils/identifier';
import { truly } from '../../../../../store/utils/utils';
import { useCheckTimeslotAvailability } from '../../../../../query/calendar/timeslot';

/** 获取抽屉打开后 需要的基本数据，比如staff数据，service数据等 */
export function useQuickAddFetchData() {
  const dispatch = useDispatch();
  const store = useStore();
  const [businessId] = useSelector(currentBusinessIdBox);

  useEffect(() => {
    if (isNormal(businessId)) {
      const staffList = store.select(selectBusinessStaffs());
      const serviceList = store.select(selectAllServiceList());
      const lodgingUnitList = store.select(selectLodgingUnitIdListWithoutCategory(String(businessId)));
      const serviceAreaList = store.select(selectBusinessServiceAreaIdList(businessId));

      const actions: Action<Promise<unknown>, any>[] = [
        !serviceList.size && getAllBusinessBasicServiceInfoList(),
        !staffList.size && getStaffList(),
        !lodgingUnitList.size && getLodgingUnitList({ businessId: String(businessId) }),
        !serviceAreaList.size && getBusinessServiceArea(businessId),
        getPetCodeList(),
      ].filter(truly);

      dispatch(actions);
    }
  }, [businessId]);

  useCheckTimeslotAvailability();
}
