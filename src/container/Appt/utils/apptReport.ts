import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { AllCareTypes } from '../../../store/careType/careType.boxes';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';
import { ApptCTAScene } from './types';
import { T_SECOND } from 'monofile-utilities/lib/consts';

interface ReportCheckInPayload {
  actionName?: ReportActionName;
  mainServiceItemType: ServiceItemType;
  apptCTAScene?: ApptCTAScene;
  isFinish?: boolean;
  duration?: number;
}

function getCheckInActionName(apptCTAScene?: ApptCTAScene, isFinish?: boolean) {
  return apptCTAScene === ApptCTAScene.HomePage
    ? isFinish
      ? ReportActionName.apptCheckInHomePageFinish
      : ReportActionName.apptCheckInHomePage
    : ReportActionName.apptCheckInDrawerCTA;
}

/**
 * 上报 check-in 发生的场景
 */
export const reportCheckInScene = (payload: ReportCheckInPayload) => {
  try {
    const time = Date.now();
    const { mainServiceItemType, actionName, apptCTAScene, isFinish, duration } = payload;
    const reportActionName =
      // actionName 的优先级最高，不过不传的话，会根据 apptCTAScene 来判断
      actionName || getCheckInActionName(apptCTAScene, isFinish);

    const serviceName = AllCareTypes.mapLabels[mainServiceItemType];
    reportData(reportActionName, {
      mainServiceItemType: serviceName,
      duration,
    });
    return () => {
      reportCheckInScene({
        ...payload,
        isFinish: true,
        duration: (Date.now() - time) / T_SECOND,
      });
    };
  } catch (error) {
    console.error('report check-in scene error', error);
  }
};
