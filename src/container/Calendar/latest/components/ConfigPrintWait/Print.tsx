import { Dropdown, Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useMemo } from 'react';
import IconPrint from '../../../../../assets/svg/icon-print.svg';
import { MultiTypePrintModal } from '../../../../../components/MultiTypePrintModal/MultiTypePrintModal';
import { PrintCardType } from '../../../../../components/MultiTypePrintModal/MultiTypePrintModal.options';
import { CommonTestIds } from '../../../../../config/testIds/common';
import { calendarSelectedDate, staffsForCalendarBox } from '../../../../../store/calendarLatest/calendar.boxes';
import { selectBusinessCalendarConfig } from '../../../../../store/calendarLatest/calendar.selectors';
import { useBool } from '../../../../../utils/hooks/useBool';
import { SummaryPrintModal } from '../../../Grooming/GroomingCalendar/components/SummaryPrintModal';
import { useGetStaffList } from '../../ApptCalendar/hooks/useGetStaffList';
import { IconText } from './IconText';
import { printReporter } from '../../../../../utils/reportData/reporter/printReporter';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';

export interface PrintProps {
  className?: string;
}

export function Print(props: PrintProps) {
  const { className } = props;
  const [selectedDate, { selectedStaffs }, { magicWaitListMode }] = useSelector(
    calendarSelectedDate,
    staffsForCalendarBox,
    selectBusinessCalendarConfig,
  );
  const selectedStaffList = useGetStaffList(selectedStaffs);
  const printSummaryModalVisible = useBool(false);
  const printCardModalVisible = useBool(false);

  const staffList = useMemo(() => {
    return selectedStaffList.map((staff) => {
      return {
        ...staff.toJSON(),
        staffId: staff.id,
        checked: true,
      };
    });
  }, [selectedStaffList]);

  const handlePrintSummary = useLatestCallback(() => {
    printReporter.setPrintType('summary');
    printSummaryModalVisible.open();
  });

  const handlePrintCard = useLatestCallback(() => {
    printReporter.setPrintType('appointment');
    printCardModalVisible.open();
  });

  return (
    <>
      <Dropdown isDisabled={Boolean(magicWaitListMode)}>
        <Dropdown.Trigger>
          <IconText className={className} icon={IconPrint} text="Print" data-testid={CommonTestIds.CalendarMiniPrint} />
        </Dropdown.Trigger>
        <Dropdown.Menu>
          <Dropdown.MenuItem onAction={handlePrintCard}>
            <Text variant="regular">Print daily appointment cards</Text>
          </Dropdown.MenuItem>
          <Dropdown.MenuItem onAction={handlePrintSummary}>
            <Text variant="regular">Print daily summary</Text>
          </Dropdown.MenuItem>
        </Dropdown.Menu>
      </Dropdown>
      <SummaryPrintModal
        visible={printSummaryModalVisible.value}
        onClose={printSummaryModalVisible.close}
        currentDate={selectedDate}
        staffsList={staffList}
      />
      {/* <PrintCardModal visible={printCardModalVisible.value} onClose={printCardModalVisible.close} /> */}
      <MultiTypePrintModal
        from="calendar"
        cardTypeOptions={[PrintCardType.Appt]}
        isOpen={printCardModalVisible.value}
        onClose={printCardModalVisible.close}
        date={selectedDate}
        staffList={staffList.map((staff) => ({
          name: staff.firstName,
          id: `${staff.staffId}`,
        }))}
      />
    </>
  );
}
