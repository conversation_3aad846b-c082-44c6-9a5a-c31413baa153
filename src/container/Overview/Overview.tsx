import { datadogRum } from '@datadog/browser-rum';
import { AppointmentStatus } from '@moego/api-web/moego/models/appointment/v1/appointment_enums';
import type { AppointmentOverview } from '@moego/api-web/moego/models/appointment/v1/appointment_models';
import { OverviewReportStatus, OverviewStatus } from '@moego/api-web/moego/models/appointment/v1/overview_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { MajorIntakeFormOutlined, MajorPrintOutlined, MajorReportOutlined } from '@moego/icons-react';
import { Button, cn, Empty, IconButton, Input, type SortingState, Tabs, Typography } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import RefResizeObserver from 'rc-resize-observer';
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { useHistory } from 'react-router';
import { useDebounce, useUnmount } from 'react-use';
import { SwitchBusinessDropdown } from '../../components/Business/SwitchBusinessDropdown';
import { CardVisibleProvider } from '../../components/CardVisible';
import { CareTypeMultiSelector } from '../../components/CareTypeSelector/CareTypeSelector';
import { Condition } from '../../components/Condition';
import { DayDimensionDatePicker } from '../../components/DatePicker/DayDimensionDatePicker';
import { WithPermission } from '../../components/GuardRoute/WithPermission';
import { MultiTypePrintModal } from '../../components/MultiTypePrintModal/MultiTypePrintModal';
import { PrintCardType } from '../../components/MultiTypePrintModal/MultiTypePrintModal.options';
import { OverviewTestIds } from '../../config/testIds/overview';
import { LayoutContainer } from '../../layout/LayoutContainer';
import { PATH_HOME_TASK } from '../../router/paths';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { selectSceneCareType } from '../../store/careType/careType.selectors';
import { getAllSubscriptionPlans } from '../../store/company/subscription.actions';
import { selectSubscriptionPlans } from '../../store/company/subscription.selectors';
import { setOverviewReportStatus } from '../../store/overview/actions/private/overview.actions';
import {
  clearOverviewList,
  clearOverviewSilentRefresh,
  setOverviewSilentRefresh,
  setOverviewState,
} from '../../store/overview/actions/public/overview.actions';
import {
  DefaultPagination,
  OverviewCountRecordBox,
  type OverviewPageState,
  OverviewPageStateBox,
  OverviewSilentRefreshBox,
  ReportStatus,
} from '../../store/overview/overview.boxes';
import {
  getOverviewParams,
  selectIsOverviewSilentRefresh,
  selectOverviewPageList,
} from '../../store/overview/overview.selectors';
import { getDateStatus } from '../../store/overview/overview.util';
import { getPetCodeList } from '../../store/pet/petCode.actions';
import { staffMapBox } from '../../store/staff/staff.boxes';
import { selectBusinessStaffs } from '../../store/staff/staff.selectors';
import { DATE_FORMAT_EXCHANGE } from '../../utils/DateTimeUtil';
import { globalEvent } from '../../utils/events/events';
import { useBool } from '../../utils/hooks/useBool';
import { useOpenApptDetailDrawer } from '../../utils/hooks/useOpenApptDetailDrawer';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { DataDogActionName } from '../../utils/logger';
import { ReportActionName } from '../../utils/reportType';
import { reportData } from '../../utils/tracker';
import { useGetReviewBoosterPreference } from '../Appt/modules/ApptDetailDrawer/hooks/useReviewBoosterPreference';
import { OverviewModal } from './Overview.modal';
import { QuickCheckIn } from './components/QuickCheckIn/QuickCheckIn';
import { ReportCardEntry } from './components/ReportCard/ReportCardEntry';
import { ServiceSummaryModal } from './components/ServiceSummaryModal';
import { WaitlistEntry } from './components/WaitlistEntry';
import { useGetOverviewColumns } from './hooks/useGetOverviewColumns';
import { useGetOverviewFetchData } from './hooks/useGetOverviewFetchData';
import { IdleRenderProvider, useIdleRender } from './idle';
import { OverviewTable } from './overview.style';
import { FILTER_DEFAULT_HEIGHT, initialState } from './overviewOptions';
import { META_DATA_KEY_LIST } from '../../store/metadata/metadata.config';
import { useEnableFeature } from '../../store/metadata/featureEnable.hooks';
import dayjs from 'dayjs';
import { useUpdateEffect } from 'ahooks';
import { T_SECOND } from 'monofile-utilities/lib/consts';

const DEFAULT_EMPTY_DESCRIPTION = 'No appointment under this status yet.';
const EMPTY_DESCRIPTION_PAGE = 'No appointment under this page yet.';
const PAGE_SIZE_OPTIONS = [15, 25, 50, 100];

function getRowId<
  T extends {
    ownKey: string;
  },
>(row: T) {
  return row.ownKey;
}
const OverviewTableClassNames = {
  bodyMoreRow: 'empty:moe-hidden',
};
const OverviewSpinClassNames = {
  iconContainer: 'moe-top-[100px]',
};

export const OverviewPage = memo(() => {
  const [keyword, setKeyword] = useState('');
  const scrollRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();

  // 提前预请求数据
  useGetReviewBoosterPreference();
  const { setIsReadyRendering } = useIdleRender();
  const [business, overviewCounts, staffList, staffMap, state, allCareTypes] = useSelector(
    selectCurrentBusiness,
    OverviewCountRecordBox,
    selectBusinessStaffs(),
    staffMapBox,
    OverviewPageStateBox,
    selectSceneCareType,
  );
  const [isOverviewSilentRefresh] = useSelector(selectIsOverviewSilentRefresh);
  const [emptyDescription, setEmptyDescription] = useState(DEFAULT_EMPTY_DESCRIPTION);
  const store = useStore();
  const [selectedKey, setSelectedKey] = useState(state.status);

  const {
    transformOverviewData,
    refreshOverviewData: refreshData,
    isLoading,
    pagination,
    refreshPageData,
  } = useGetOverviewFetchData();

  const showSummaryModal = useBool();
  const history = useHistory();

  const sorting = useMemo(() => {
    return state.orderBys.map((item) => ({
      id: item.fieldName,
      desc: !item.asc,
    }));
  }, [state.orderBys]);

  const showPrintCardModal = useBool();
  const isFilterWrap = useBool();

  const initAllSubscriptionPlans = useSerialCallback(async () => {
    const planList = store.select(selectSubscriptionPlans);
    if (planList.size === 0) {
      await dispatch(getAllSubscriptionPlans());
    }
  });
  const loading = (isLoading || initAllSubscriptionPlans.isBusy()) && !isOverviewSilentRefresh;

  const handleSortChange = useSerialCallback((sorting: SortingState) => {
    setOverviewStateInner({
      orderBys: sorting.map((item) => ({
        fieldName: item.id,
        asc: !item.desc,
      })),
    });
    reportData(ReportActionName.homepageTableHeaderSortClick, {
      name: sorting[0].id,
    });
  });

  const handleActionChange = useCallback(
    async (appointment?: AppointmentOverview) => {
      if (pagination.pageNum > 1 && transformOverviewData.length - 1 === 0) {
        // 大于 1 页, 且操作最后一个数据，则变更空列表描述
        setEmptyDescription(EMPTY_DESCRIPTION_PAGE);
        await refreshData({ pageNum: DefaultPagination.pageNum });
        setEmptyDescription(DEFAULT_EMPTY_DESCRIPTION);
      } else {
        await refreshData({ pageNum: pagination.pageNum });
      }
      const clickedTicketId = appointment?.id;
      if (clickedTicketId) {
        const overviewSilentRefreshBox = store.select(OverviewSilentRefreshBox);
        const stringTicketId = clickedTicketId.toString();

        overviewSilentRefreshBox[stringTicketId] && dispatch(setOverviewSilentRefresh({ [stringTicketId]: false }));
      } else {
        dispatch(clearOverviewSilentRefresh());
      }
    },
    [pagination.pageNum, transformOverviewData.length],
  );
  useEffect(() => {
    if (loading) {
      setIsReadyRendering(false);
    } else {
      // 先延后一帧，确保浏览器的 paint 执行完毕、用户看到了界面更新，然后再设置 ready 状态以渲染 action 列。
      // 如果不延后，浏览器会执行 pre-paint，然后立即开始渲染 action 列；用户感觉到的界面更新会比较晚。
      // 预计能减少 100ms+ 的用户等待时间，但总计算量不变。
      // 这里 batchedUpdates 不管用，依旧会出现交替 render-commit 的情况。
      setTimeout(() => {
        setIsReadyRendering(true);
      });
    }
  }, [setIsReadyRendering, loading]);

  const setOverviewStateInner = (params: Partial<OverviewPageState>) => {
    if (params.status) {
      setSelectedKey(params.status);
      const pageList = store.select(selectOverviewPageList());
      return refreshData(getOverviewParams({ ...state, ...params }, pageList.pageNum, pageList.pageSize)).finally(
        () => {
          dispatch(setOverviewState(params));
        },
      );
    } else {
      dispatch(setOverviewState(params));
      return refreshData({});
    }
  };
  const columns = useGetOverviewColumns(state.status, handleActionChange, handleSortChange, setOverviewStateInner);

  const { openApptDetailDrawer } = useOpenApptDetailDrawer();

  useEffect(() => {
    dispatch(getPetCodeList());

    // 依赖权限数据,预请求
    const planList = store.select(selectSubscriptionPlans);
    if (planList.size === 0) {
      initAllSubscriptionPlans();
    }

    const dispose = globalEvent.refresh.on(() => {
      refreshData({});
    });

    return () => {
      setOverviewStateInner({ ...initialState });
      dispatch(setOverviewReportStatus(ReportStatus.all));
      dispose();
    };
  }, []);

  useEffect(() => {
    refreshData({});
  }, [business.id]);

  useEffect(() => {
    setOverviewStateInner({ serviceItemType: allCareTypes });
  }, [allCareTypes.length]);

  useEffect(() => {
    const time = Date.now();
    reportData(ReportActionName.homepageView);
    return () => {
      reportData(ReportActionName.homepageLeave, { duration: (Date.now() - time) / T_SECOND });
    };
  }, []);

  const { isToday, isPastDay, isFutureDay } = useMemo(() => getDateStatus(state.date), [state.date]);

  const tabsList = useMemo(() => {
    const tabs = [];

    if (isToday || isFutureDay) {
      tabs.push(
        {
          key: OverviewStatus.EXPECTED,
          label: `Expected (${overviewCounts[OverviewStatus.EXPECTED]})`,
          reportLabel: 'Expected',
        },
        {
          key: OverviewStatus.IN_STORE,
          label: `In-house (${overviewCounts[OverviewStatus.IN_STORE]})`,
          reportLabel: 'In-house',
        },
      );
    }

    if (isToday) {
      tabs.push({
        key: OverviewStatus.READY_TO_GO,
        label: `Going home today (${overviewCounts[OverviewStatus.READY_TO_GO]})`,
        reportLabel: 'Going home today',
      });
    }

    if (isToday || isPastDay || isFutureDay) {
      tabs.push({
        key: OverviewStatus.APPOINTMENT_FINISHED,
        label: `${isToday ? 'Finished' : 'Departure'} (${overviewCounts[OverviewStatus.APPOINTMENT_FINISHED]})`,
        reportLabel: `${isToday ? 'Finished' : 'Departure'}`,
      });
    }
    return tabs;
  }, [state.date, isToday, overviewCounts]);

  const { enable: accessTabBand } = useEnableFeature(META_DATA_KEY_LIST.AccessTabBand);

  const cardTypeOptions = useMemo(
    () =>
      PrintCardType.values.filter((cardType) => {
        if (cardType === PrintCardType.BoardingDeparture) {
          return allCareTypes.includes(ServiceItemType.BOARDING);
        }
        if (cardType === PrintCardType.CollarLabel) {
          return accessTabBand;
        }
        return cardType !== PrintCardType.Playgroup;
      }),
    [allCareTypes, accessTabBand],
  );

  const staffRenderList = useMemo(() => {
    return staffList
      .map((id) => {
        const staff = staffMap.mustGetItem(id);
        return {
          id: `${staff.id}`,
          name: staff.fullName(),
        };
      })
      .toJSON();
  }, [staffList, staffMap]);

  // 接口防抖
  useDebounce(
    () => {
      if (keyword !== state.keyword) {
        setOverviewStateInner({ keyword });
      }
    },
    300,
    [keyword],
  );

  const hasSearch = keyword !== state.keyword;
  useEffect(() => {
    if (hasSearch) {
      reportData(ReportActionName.homepageHeaderSearch);
    }
  }, [hasSearch]);

  const handleBusinessChange = () => {
    setOverviewStateInner({ ...initialState, serviceItemType: allCareTypes });
    reportData(ReportActionName.homepageLocationSwitchClick);
  };

  useEffect(() => {
    const activeStatus = tabsList.map((tab) => tab.key);
    setOverviewStateInner({ activeStatus, status: activeStatus[0] });
  }, [tabsList.map((tab) => tab.key).join(',')]);

  const memoPagination = useMemo(
    () => ({
      pageIndex: pagination.pageNum,
      pageSize: pagination.pageSize,
      totalSize: pagination.total,
      pageSizeOptions: PAGE_SIZE_OPTIONS,
      renderTotal: () => {
        return (
          <Typography.Text variant="small">
            <span className="moe-text-tertiary">Total: </span>
            <span className="moe-text-primary">{overviewCounts[state.status]}</span>
          </Typography.Text>
        );
      },
    }),
    [pagination, overviewCounts, state.status],
  );
  useUpdateEffect(() => {
    reportData(ReportActionName.homepageTablePaginationClick, {
      pageSize: pagination.pageSize,
    });
  }, [pagination.pageSize]);
  const handlePageChange = useCallback(
    (pagination) => {
      refreshPageData({
        pageNum: pagination.pageIndex,
        pageSize: pagination.pageSize,
      });
    },
    [refreshPageData],
  );
  const emptyPlaceholder = useMemo(() => {
    return (
      <Empty
        classNames={{
          title: 'moe-hidden',
        }}
        className="moe-my-[96px]"
        description={emptyDescription}
      />
    );
  }, [emptyDescription]);

  const handleRowClick = useCallback(
    (row) => {
      openApptDetailDrawer({
        ticketId: Number(row.original.appointment.id),
        serviceItemTypes: row.original.serviceItemTypes,
      });
      reportData(ReportActionName.homepageItemApptClick);
    },
    [openApptDetailDrawer],
  );

  useUnmount(() => {
    ReactDOM.unstable_batchedUpdates(() => {
      setIsReadyRendering(false);
      dispatch(clearOverviewList());
    });
  });

  return (
    <LayoutContainer className="moe-bg-white moe-px-0">
      <div className="moe-h-full moe-overflow-auto moe-px-l" ref={scrollRef}>
        <div className="moe-flex moe-items-start moe-justify-between">
          <div className="moe-flex moe-items-center moe-flex-wrap moe-justify-start">
            <DayDimensionDatePicker
              className="moe-h-[40px]"
              value={state.date}
              onChange={(date) => {
                setOverviewStateInner({ date });
                reportData(ReportActionName.homepageDateClick, {
                  dateType: dayjs().isSame(date, 'day') ? 'today' : dayjs().isBefore(date, 'day') ? 'future' : 'past',
                });
              }}
            />

            <SwitchBusinessDropdown
              scene="working"
              onChange={handleBusinessChange}
              className="moe-h-[40px]"
            ></SwitchBusinessDropdown>
          </div>
          <div className="moe-flex moe-justify-end moe-gap-[15px] moe-flex-wrap">
            <ReportCardEntry date={state.date} />
            <IconButton
              tooltip="Print"
              className="moe-shrink-0"
              icon={<MajorPrintOutlined />}
              variant="secondary"
              size="l"
              onPress={() => {
                showPrintCardModal.open();
                reportData(ReportActionName.homepageHeaderIconPrintCardClick);
              }}
              data-testid={OverviewTestIds.OverviewPrintBtn}
            />
            <IconButton
              className="moe-shrink-0"
              icon={<MajorReportOutlined />}
              variant="secondary"
              size="l"
              onPress={() => {
                showSummaryModal.open();
                reportData(ReportActionName.homepageHeaderIconSummaryClick);
              }}
              tooltip="View service summary"
            />
            <WaitlistEntry />
            <WithPermission permissions={'accessTasks'}>
              <Button
                className="moe-shrink-0"
                variant="secondary"
                onPress={(e) => {
                  reportData(ReportActionName.homepageHeaderIconTasksClick);
                  if (e.ctrlKey || e.metaKey) {
                    window.open(
                      PATH_HOME_TASK.build({
                        date: state.date.format(DATE_FORMAT_EXCHANGE),
                      }),
                      '_blank',
                      'noopener',
                    );
                  } else {
                    history.push(
                      PATH_HOME_TASK.build({
                        date: state.date.format(DATE_FORMAT_EXCHANGE),
                      }),
                    );
                  }
                }}
                icon={<MajorIntakeFormOutlined />}
              >
                Tasks
              </Button>
            </WithPermission>

            <Condition if={isToday && allCareTypes.includes(ServiceItemType.DAYCARE)}>
              <QuickCheckIn />
            </Condition>
          </div>
        </div>
        <RefResizeObserver
          onResize={({ height }, element) => {
            const isWrap = height > FILTER_DEFAULT_HEIGHT || element.clientWidth < element.scrollWidth;
            isFilterWrap.as(isWrap);
          }}
        >
          <div
            className={cn(
              'moe-flex moe-gap-s moe-items-center moe-justify-start moe-p-spacing-s moe-rounded-m moe-mt-l moe-bg-neutral-sunken-0',
              {
                'moe-flex-wrap': isFilterWrap.value,
              },
            )}
          >
            <Input.Search
              className={cn('moe-min-w-[460px] !moe-max-w-[686px] moe-flex-1 rr-ignore rr-block')}
              placeholder="Search by pet name, customer name, or phone number"
              value={keyword}
              onChange={(keyword) => {
                setKeyword(keyword);
              }}
            />

            <CareTypeMultiSelector
              displayLabel={false}
              classNames={{
                control: 'moe-min-w-[260px] moe-shrink-0',
                formItemWrapper: 'moe-ml-0',
              }}
              careTypes={state.serviceItemType}
              onChange={(values) => {
                setOverviewStateInner({ serviceItemType: values });
                reportData(ReportActionName.homepageHeaderFilterClick);
              }}
            />
            <Condition if={!!state.keyword || state.serviceItemType?.length < allCareTypes.length}>
              <Button
                className="moe-shrink-0"
                align="start"
                variant="tertiary-legacy"
                onPress={() => {
                  setOverviewStateInner({
                    ...initialState,
                    serviceItemType: allCareTypes,
                  });
                  setKeyword('');
                }}
              >
                Reset
              </Button>
            </Condition>
          </div>
        </RefResizeObserver>
        <div className="moe-mt-m">
          <Tabs
            selectedKey={selectedKey.toString()}
            onChange={async (key) => {
              if (state.status !== Number(key)) {
                ReactDOM.unstable_batchedUpdates(() => {
                  const task = datadogRum.startDurationVital(DataDogActionName.CHANGE_TAB);
                  setIsReadyRendering(false);
                  setOverviewStateInner({
                    status: Number(key) as OverviewStatus,
                    reportStatus: OverviewReportStatus.UNSPECIFIED,
                    appointmentStatus: AppointmentStatus.UNSPECIFIED,
                  }).finally(() => {
                    task &&
                      datadogRum.stopDurationVital(DataDogActionName.CHANGE_TAB, {
                        context: {
                          overviewStatus: key,
                        },
                      });
                  });
                  reportData(ReportActionName.homepageTabClick, {
                    status: tabsList.find((tab) => tab.key === Number(key))?.reportLabel,
                  });
                });
              }
            }}
            classNames={{
              panel: 'moe-pt-[8px]',
            }}
          >
            {tabsList.map((tab) => {
              return <Tabs.Item key={tab.key} label={tab.label} />;
            })}
          </Tabs>
          <OverviewTable
            columns={columns}
            data={transformOverviewData}
            getRowId={getRowId}
            sorting={sorting}
            onSortingChange={handleSortChange}
            stickyContainer={scrollRef.current ?? undefined}
            onRowClick={handleRowClick}
            manualSorting
            classNames={OverviewTableClassNames}
            emptyPlaceholder={emptyPlaceholder}
            isLoading={loading}
            spinClassNames={OverviewSpinClassNames}
            pagination={memoPagination}
            onPaginationChange={handlePageChange}
          />
        </div>
        <MultiTypePrintModal
          from="homepage"
          isOpen={showPrintCardModal.value}
          onClose={showPrintCardModal.close}
          cardTypeOptions={cardTypeOptions}
          date={state.date}
          staffList={staffRenderList}
          serviceItemTypes={state.serviceItemType}
        />
        <ServiceSummaryModal
          isOpen={showSummaryModal.value}
          onClose={showSummaryModal.close}
          initDate={state.date.format(DATE_FORMAT_EXCHANGE)}
        />
        <OverviewModal />
      </div>
    </LayoutContainer>
  );
});

export const Overview = () => {
  return (
    <IdleRenderProvider>
      <CardVisibleProvider>
        <OverviewPage />
      </CardVisibleProvider>
    </IdleRenderProvider>
  );
};
