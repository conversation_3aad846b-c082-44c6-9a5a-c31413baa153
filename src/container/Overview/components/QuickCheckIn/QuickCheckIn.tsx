import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Button, Modal, useForm, useWatch } from '@moego/ui';
import { useDispatch, useStore } from 'amos';
import React, { memo, useEffect, useRef, useState } from 'react';
import { useAsync } from 'react-use';
import { toastApi } from '../../../../components/Toast/Toast';
import { currentBusinessIdBox } from '../../../../store/business/business.boxes';
import { getOverviewList } from '../../../../store/overview/actions/public/overview.actions';
import { selectOverviewParams } from '../../../../store/overview/overview.selectors';
import { getDaycareServiceList } from '../../../../store/service/actions/public/service.actions';
import { withPl } from '../../../../utils/calculator';
import { useBool } from '../../../../utils/hooks/useBool';
import { ReportActionName } from '../../../../utils/reportType';
import { reportCheckInScene } from '../../../Appt/utils/apptReport';
import { QuickCheckInForm, type QuickCheckInFormValues, type QuickCheckInRef } from './components/QuickCheckInForm';
import { ApptCheckInOutAlert } from '../../../Appt/components/ApptCheckInOutAlert/ApptCheckInOutAlert';
import { ID_ANONYMOUS } from '../../../../store/utils/identifier';
import { type AlertDetail } from '@moego/api-web/moego/models/appointment/v1/check_in_out_alert_defs';
import { getPetDetail } from '../../../../store/pet/pet.actions';
import { listToMap, useSerialCallback } from '@moego/tools';
import { batchGetAlertsForCheckIn } from '../../../../store/checkInOutAlert/checkInOutAlert.actions';
import { selectCheckInOutSettings } from '../../../../store/checkInOutAlert/checkInOutAlert.selectors';
import { selectCompanyCareTypeNameMap } from '../../../../store/careType/careType.selectors';
import { reportData } from '../../../../utils/tracker';

export const QuickCheckIn = memo(() => {
  const dispatch = useDispatch();
  const store = useStore();
  const overviewParams = store.select(selectOverviewParams());
  const businessId = String(store.select(currentBusinessIdBox));
  const alertSettings = store.select(selectCheckInOutSettings);
  const companyCareTypeNameMap = store.select(selectCompanyCareTypeNameMap);

  const visible = useBool();
  const ref = useRef<QuickCheckInRef>(null);
  const title = `${companyCareTypeNameMap.Daycare} quick check in`;

  useEffect(() => {
    if (visible.value) {
      reportData(ReportActionName.apptCheckInQuickCheckInModalView);
    }
  }, [visible.value]);

  const { loading: isButtonLoading, value: total } = useAsync(async () => {
    const { pagination } = await dispatch(
      getDaycareServiceList({
        businessIds: [businessId],
        pagination: {
          pageNum: 1,
          pageSize: 10,
        },
      }),
    );
    return pagination.total;
  }, []);

  const form = useForm<QuickCheckInFormValues>();
  const petIds = useWatch({
    control: form.control,
    name: 'petIds',
  });

  const [alertDetails, setAlertDetails] = useState<AlertDetail[]>([]);

  const queryAlertDetails = useSerialCallback(async () => {
    if (!petIds?.length) {
      return [];
    }

    const data = await dispatch(getPetDetail(petIds.map((petId) => Number(petId))));
    const petMap = listToMap(
      data,
      (row) => String(row.petDetail.petId),
      (row) => row.petDetail,
      true,
    );

    const map = new Map<number, Set<string>>();
    petIds.map((petId) => {
      const pet = petMap.get(petId);
      const customerId = pet?.customerId;

      if (customerId && customerId !== ID_ANONYMOUS) {
        map.set(customerId, (map.get(customerId) ?? new Set()).add(petId));
      }
    });

    if (!map.size) {
      return [];
    }

    const clientPets = [...map.entries()].map(([customerId, petIds]) => ({
      customerId: String(customerId),
      petIds: [...petIds],
    }));
    const alertDetails = await dispatch(batchGetAlertsForCheckIn({ businessId, clientPets }));

    reportData(ReportActionName.apptCheckInAlertsView, {
      value: alertDetails.map((alertDetail) => alertDetail.clientAlert),
    });

    return alertDetails;
  });

  useEffect(() => {
    if (alertSettings.enabledForQuickCheckIn) {
      queryAlertDetails().then((details) => {
        setAlertDetails(details ?? []);
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [petIds?.sort().join()]);

  return (
    <>
      <Button
        onPress={() => {
          if (!total) {
            return toastApi.error('Quick check in is only for daycare, please set up your daycare service first.');
          }
          visible.open();
        }}
        isLoading={isButtonLoading}
      >
        {title}
      </Button>
      <Modal
        isOpen={visible.value}
        title={title}
        onClose={() => {
          visible.close();
          setAlertDetails([]);
        }}
        autoCloseOnConfirm={false}
        confirmText="Proceed with check in"
        confirmButtonProps={
          queryAlertDetails.isBusy()
            ? {
                isLoading: true,
              }
            : undefined
        }
        onConfirm={async () => {
          const res = await ref.current?.submit();
          if (res) {
            reportCheckInScene({
              actionName: ReportActionName.apptCheckInQuickCheckIn,
              mainServiceItemType: ServiceItemType.DAYCARE,
            });
            const { createdAppointmentIds, updatedAppointmentIds } = res;
            const length = createdAppointmentIds.length + updatedAppointmentIds.length;
            if (length) {
              toastApi.success(`${withPl(length, 'appointment')} successfully checked in.`);
              dispatch(getOverviewList(overviewParams));
            } else {
              toastApi.error('Pet(s) already in-store.');
            }
          }
          visible.close();
        }}
      >
        <QuickCheckInForm form={form} ref={ref} />
        {alertDetails.length > 0 ? (
          <div className="moe-flex moe-flex-col moe-gap-y-m moe-mt-[24px]">
            {alertDetails.map((alertDetail, index) => (
              <ApptCheckInOutAlert key={index} {...alertDetail} />
            ))}
          </div>
        ) : null}
      </Modal>
    </>
  );
});
