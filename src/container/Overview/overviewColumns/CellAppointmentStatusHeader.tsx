import { OverviewStatus } from '@moego/api-web/moego/models/appointment/v1/overview_enums';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { type OverviewPageState, OverviewPageStateBox } from '../../../store/overview/overview.boxes';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { AppointmentStatus } from '../../TicketDetail/AppointmentStatus';
import { ApptTicketStatus } from '../../TicketDetail/interfaces.latest';
import { DropdownFilter } from '../components/DropdownFilter';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';

interface CellAppointmentStatusHeaderProps {
  showApptStatus?: boolean;
  setOverviewStateInner: (state: Partial<OverviewPageState>) => void;
}
const baseOption = {
  label: 'All',
  value: String(AppointmentStatus.UNSPECIFIED),
};

export const CellAppointmentStatusHeader = memo(function CellAppointmentStatusHeader({
  showApptStatus,
  setOverviewStateInner,
}: CellAppointmentStatusHeaderProps) {
  const [{ status, appointmentStatus }] = useSelector(OverviewPageStateBox);

  const handleAppointmentStatusChange = useLatestCallback((value: string) => {
    setOverviewStateInner({
      appointmentStatus: Number(value) as AppointmentStatus,
    });
    reportData(ReportActionName.homepageTableHeaderApptStatusFilterClick, {
      name: ApptTicketStatus.mapLabels[Number(value) as AppointmentStatus].label,
    });
  });

  const apptStatusOptions = useMemo(() => {
    return showApptStatus
      ? ([
          baseOption,
          ...ApptTicketStatus.values
            .filter((status) => status !== AppointmentStatus.CANCELED)
            .map((status) => {
              return {
                label: ApptTicketStatus.mapLabels[status].label,
                value: String(status),
              };
            }),
        ] as {
          label: string;
          value: string;
        }[])
      : [];
  }, [showApptStatus]);

  const dropDownOptions = useMemo(() => {
    const result = [];
    switch (status) {
      case OverviewStatus.EXPECTED: {
        result.push(
          baseOption,
          {
            label: 'Confirmed',
            value: AppointmentStatus.CONFIRMED,
          },
          {
            label: 'Unconfirmed',
            value: AppointmentStatus.UNCONFIRMED,
          },
        );
        break;
      }
      case OverviewStatus.IN_STORE:
      case OverviewStatus.READY_TO_GO: {
        result.push(
          baseOption,
          {
            label: 'Checked in',
            value: AppointmentStatus.CHECKED_IN,
          },
          {
            label: 'Ready',
            value: AppointmentStatus.READY,
          },
        );
        break;
      }
    }
    return result.map(({ label, value }) => ({
      label,
      value: String(value),
    }));
  }, [status]);

  return (
    <div className="moe-flex moe-items-center moe-gap-[8px]">
      <span className="moe-w-[50px]">Status</span>
      <DropdownFilter
        value={String(appointmentStatus)}
        onChange={handleAppointmentStatusChange}
        label="Appointment status"
        options={showApptStatus ? apptStatusOptions : dropDownOptions}
        isActive={appointmentStatus !== AppointmentStatus.UNSPECIFIED}
      />
    </div>
  );
});
