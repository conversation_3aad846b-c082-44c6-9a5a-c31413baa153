import { AppointmentNoteType } from '@moego/api-web/moego/models/appointment/v1/appointment_note_enums';
import { <PERSON><PERSON><PERSON> } from '@moego/finance-utils';
import { MajorDollarOutlined, MinorMessageOutlined } from '@moego/icons-react';
import { IconButton, Text, Tooltip } from '@moego/ui';
import { type CellContext } from '@moego/ui/dist/esm/components/Table/Table.types';
import { useSelector, useStore } from 'amos';
import classNames from 'classnames';
import React, { memo } from 'react';
import SvgIconAgreementNosignedSvg from '../../../assets/svg/icon-agreement-nosigned.svg';
import SvgMessageErrorSvg from '../../../assets/svg/message-error.svg';
import { CustomerPackagesWithTooltip } from '../../../components/CustomerPackages/CustomerPackagesWithTooltip';
import { SvgIcon } from '../../../components/Icon/Icon';
import { MembershipIdentify } from '../../../components/MembershipIdentify/MembershipIdentify';
import { TagSmall } from '../../../components/Tag/TagSmall';
import { PATH_CUSTOMER_DETAIL } from '../../../router/paths';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../store/business/role.selectors';
import { printFullName } from '../../../store/customer/customer.boxes';
import { type OverviewRecordModel } from '../../../store/overview/overview.boxes';
import { useRedirectMessage } from '../../../utils/BusinessUtil';
import { notificationsDict } from '../../Calendar/Grooming/interfaces';
import { CofStatus } from '../../Payment/components/CofStatus';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';

const onStopPropagation: React.MouseEventHandler<HTMLSpanElement> = (e) => {
  e.stopPropagation();
};

export const CellClient: React.FC<CellContext<OverviewRecordModel, unknown>> = memo(({ row }) => {
  const { customerProfile, isNewCustomer, requiredSign, customerPackages, unpaidAmount, cofStatus } =
    row.original.customer;

  const [business] = useSelector(selectCurrentBusiness);
  const { notes } = row.original;
  const alertNote = notes.find((note) => note.type === AppointmentNoteType.ALERT_NOTES)?.note || '';
  const isDeleted = customerProfile.deleted;
  const isUnpaid = new MoeMoney(unpaidAmount).isZero() === false;

  const handleToMessage = useRedirectMessage();
  const store = useStore();

  const go2ClientDetail = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (isDeleted || !store.select(selectCurrentPermissions()).has('viewIndividualClientProfile')) {
      return;
    }
    reportData(ReportActionName.homepageItemClientNameClick);
    // open in new tab
    window.open(
      PATH_CUSTOMER_DETAIL.build({ customerId: Number(customerProfile.id) }),
      '_blank',
      'noopener noreferrer',
    );
  };

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-1">
      <div className="moe-gap-xs moe-flex moe-items-center moe-flex-wrap">
        <span
          className={classNames('moe-max-w-[400px] moe-truncate moe-text-regular-short moe-text-primary', {
            ['moe-text-[#ccc] moe-cursor-default']: isDeleted,
            ['hover:moe-text-brand moe-cursor-pointer']: !isDeleted,
          })}
          onClick={go2ClientDetail}
        >
          {printFullName(customerProfile.firstName, customerProfile.lastName)}
        </span>
        <MembershipIdentify className="!moe-ml-0" customerId={customerProfile.id} />
        <CofStatus
          customerId={Number(customerProfile.id)}
          cofStatus={cofStatus}
          iconClassName="!moe-ml-0 !moe-mr-0"
          source="homepage"
          showAction={true}
          onTooltipOpenChange={(isOpen) => {
            isOpen && reportData(ReportActionName.homepageItemIconCardOnFilePreview);
          }}
        />
        <CustomerPackagesWithTooltip
          packages={customerPackages}
          onTooltipOpenChange={(isOpen) => {
            isOpen && reportData(ReportActionName.homepageItemIconPackagePreview);
          }}
        />
        {isNewCustomer ? <TagSmall label="New" color="success" isBordered={false} /> : null}
        {alertNote ? (
          <Tooltip
            content={alertNote}
            side="top"
            onOpenChange={(isOpen) => {
              isOpen && reportData(ReportActionName.homepageItemIconAlertNotePreview);
            }}
          >
            <div className="moe-cursor-pointer moe-flex moe-items-center" onClick={onStopPropagation}>
              <SvgIcon src={SvgMessageErrorSvg} size={20} color={'#F3413B'} />
            </div>
          </Tooltip>
        ) : null}

        {isUnpaid ? (
          <Tooltip
            content={`${business.formatMoney(unpaidAmount)} unpaid`}
            side="top"
            onOpenChange={(isOpen) => {
              isOpen && reportData(ReportActionName.homepageItemIconUnpaidPreview);
            }}
          >
            <div className="moe-cursor-pointer moe-flex moe-items-center" onClick={onStopPropagation}>
              <MajorDollarOutlined className="moe-text-danger moe-w-[20px] moe-h-[20px] !moe-text-[20px]" />
            </div>
          </Tooltip>
        ) : null}

        {requiredSign ? (
          <Tooltip
            content={notificationsDict.requiredSign}
            side="top"
            onOpenChange={(isOpen) => {
              isOpen && reportData(ReportActionName.homepageItemIconAgreementPreview);
            }}
          >
            <div className="moe-cursor-pointer moe-flex moe-items-center" onClick={onStopPropagation}>
              <SvgIcon src={SvgIconAgreementNosignedSvg} size={20} color={'#F3413B'} />
            </div>
          </Tooltip>
        ) : null}
      </div>
      <div className="moe-flex moe-gap-xs moe-items-center">
        <Text variant="small" className="rr-mask moe-text-tertiary">
          {customerProfile.phoneNumber}
        </Text>
        <IconButton
          variant="primary"
          color="transparent"
          icon={<MinorMessageOutlined />}
          className="!moe-w-[20px] !moe-h-[20px] !moe-text-[#333]"
          onPress={() => customerProfile.id && handleToMessage(Number(customerProfile.id), {}, true)}
        />
      </div>
    </div>
  );
});

CellClient.displayName = 'CellClient';
