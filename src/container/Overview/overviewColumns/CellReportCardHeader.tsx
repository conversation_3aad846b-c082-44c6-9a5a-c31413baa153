import { type OverviewReportStatus } from '@moego/api-web/moego/models/appointment/v1/overview_enums';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { type OverviewPageState, OverviewPageStateBox, ReportStatus } from '../../../store/overview/overview.boxes';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { DropdownFilter } from '../components/DropdownFilter';
import { reportData } from '../../../utils/tracker';
import { ReportActionName } from '../../../utils/reportType';

export const CellReportCardHeader = memo(function CellReportCardHeader({
  setOverviewStateInner,
}: {
  setOverviewStateInner: (params: Partial<OverviewPageState>) => void;
}) {
  const [{ reportStatus }] = useSelector(OverviewPageStateBox);
  const handleReportStatusChange = useLatestCallback((value: string) => {
    setOverviewStateInner({
      reportStatus: Number(value) as OverviewReportStatus,
    });
    reportData(ReportActionName.homepageTableHeaderReportCardFilterClick, {
      name: ReportStatus.mapLabels[Number(value) as OverviewReportStatus],
    });
  });
  const reportOptions = useMemo(() => {
    return [ReportStatus.all, ReportStatus.unsent, ReportStatus.draft, ReportStatus.sent].map((status) => {
      const label = ReportStatus.mapLabels[status];
      return {
        value: String(status),
        label,
      };
    });
  }, []);

  return (
    <div className="moe-flex moe-items-center moe-gap-[8px]">
      <span className="moe-w-[80px]">Report card</span>
      <DropdownFilter
        value={String(reportStatus)}
        onChange={handleReportStatusChange}
        label="Report card status"
        options={reportOptions}
        isActive={reportStatus !== ReportStatus.all}
      />
    </div>
  );
});
