import type {
  AddOnCompositeOverview,
  EvaluationServiceOverview,
} from '@moego/api-web/moego/api/appointment/v1/overview_api';
import { Heading, Tag, Text, Tooltip, cn } from '@moego/ui';
import { type CellContext } from '@moego/ui/dist/esm/components/Table/Table.types';
import React, { memo, useMemo } from 'react';
import { Condition } from '../../../components/Condition';
import { OverviewPageStateBox, type OverviewRecordModel } from '../../../store/overview/overview.boxes';
import { withPl } from '../../../utils/calculator';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { getMainCareType } from '../../../components/PetAndServicePicker/utils/getMainCareType';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';

const COMMON_TEXT_CLASS = 'tablet-l:moe-max-w-[368px] moe-max-w-[238px] moe-whitespace-pre-wrap';
const COMMON_CONTAINER_CLASS = 'moe-mt-xs first:moe-mt-0 moe-max-w-[270px] tablet-l:moe-max-w-[400px]';

const isDateMatchCondition = ({
  dateType,
  overviewDate,
  addOnStartDate,
  specificDates,
  mainServiceStartDate,
  mainServiceEndDate,
  mainServiceItemType,
}: {
  dateType: PetDetailDateType;
  overviewDate: dayjs.Dayjs;
  addOnStartDate: string;
  specificDates: string[];
  // 有可能就是纯 addon，就没有 mainService
  mainServiceStartDate?: string;
  mainServiceEndDate?: string;
  mainServiceItemType?: ServiceItemType;
}) => {
  const unit = 'day';
  switch (dateType) {
    case PetDetailDateType.PET_DETAIL_DATE_EVERYDAY:
      if (mainServiceItemType === ServiceItemType.DAYCARE) {
        // 如果是 daycare 的话，quick add 的时候 addon 有可能传 1
        // 因此不能把 daycare 的 end date 作为判断条件，否则会把 daycare 当天的 addon 也过滤掉
        // TODO(winches) 后续在 quick add 场景的时候，应该要把这个 dateType 传对
        return overviewDate.isSame(dayjs(mainServiceStartDate), unit);
      }
      return (
        overviewDate.isSameOrAfter(dayjs(mainServiceStartDate), unit) &&
        overviewDate.isBefore(dayjs(mainServiceEndDate), unit)
      );

    case PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE:
      return specificDates?.some((date) => dayjs(date).isSame(overviewDate, unit)) || false;

    case PetDetailDateType.PET_DETAIL_DATE_DATE_POINT:
      return dayjs(addOnStartDate).isSame(overviewDate, unit);

    case PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY:
      return (
        overviewDate.isSameOrAfter(dayjs(mainServiceStartDate), unit) &&
        overviewDate.isSameOrBefore(dayjs(mainServiceEndDate), unit)
      );

    case PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY:
      return (
        overviewDate.isAfter(dayjs(mainServiceStartDate), unit) &&
        overviewDate.isSameOrBefore(dayjs(mainServiceEndDate), unit)
      );

    case PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY:
      return overviewDate.isSame(dayjs(mainServiceStartDate), unit);

    case PetDetailDateType.PET_DETAIL_DATE_LAST_DAY:
      return overviewDate.isSame(dayjs(mainServiceEndDate), unit);

    default:
      return false;
  }
};

const AddOnsTag = memo(({ addOns }: { addOns: AddOnCompositeOverview[] }) => {
  return (
    <>
      {addOns?.length > 0 && (
        <Tooltip
          side="top"
          onOpenChange={(isOpen) => {
            isOpen && reportData(ReportActionName.homepageItemAddOnPreview);
          }}
          content={addOns.map((row) => (
            <div key={row.id}>
              {row.serviceName} {row.staffName ? ` (${row.staffName})` : ''}
            </div>
          ))}
        >
          <Tag
            label={`+${withPl(addOns.length, 'add-on')}`}
            variant="filled"
            color="neutral"
            size="s"
            className="moe-mt-xxs"
          ></Tag>
        </Tooltip>
      )}
    </>
  );
});

const EvaluationsTag = memo(({ evaluations }: { evaluations: EvaluationServiceOverview[] }) => {
  const [business] = useSelector(selectCurrentBusiness);
  return (
    <>
      {evaluations?.length > 0 && (
        <Tooltip
          side="top"
          content={evaluations.map((row) => (
            <div key={row.id}>
              {row.serviceName}
              <br />
              {business.formatDate(dayjs(row.startDate))}{' '}
              {business.formatTime(dayjs(row.startDate).add(row.startTime || 0, 'minute'))}{' '}
              {row.staffName ? ` (${row.staffName})` : ''}
            </div>
          ))}
        >
          <Tag
            label={`+${withPl(evaluations.length, 'evaluation')}`}
            variant="filled"
            color="neutral"
            size="s"
            className="moe-mt-xxs"
          ></Tag>
        </Tooltip>
      )}
    </>
  );
});

/**
 * 展示策略
 * Add-on 展示规则：
 * 如果一条 appt 记录只有 add-on, 则展示 add-on name
 * 其他情况只展示 service name
 * Room 展示规则：跟随在 Boarding service name 下一行
 * Multi pet 展示规则：
 * Group by pet
 * Single pet展示规则:
 * 从上到下平铺
 */
export const CellService: React.FC<CellContext<OverviewRecordModel, unknown>> = ({ row }) => {
  const { serviceDetail } = row.original;
  const isMultiPets = serviceDetail.length > 1;

  const [state] = useSelector(OverviewPageStateBox);

  // 先处理数据，后编写 UI，做到逻辑与展示分离，同时也可以用到 useMemo，避免不必要的 rerender。
  // 例如内部的 addOns 如果放到 render 逻辑中，每次 filter 都会返回一个新的引用。
  const serviceDetailVM = useMemo(() => {
    return serviceDetail.map((detail) => {
      const { pet, services, evaluations } = detail;
      const isEmptyServices = services.length === 0;
      const mainServiceItemType = getMainCareType(services.map((s) => s.serviceItemType));
      // 可能 services 为空数组，此时 mainService 就是 undefined
      const mainService = services.find((s) => s.serviceItemType === mainServiceItemType);

      const addOns = detail.addOns.filter(({ dateType, specificDates, startDate }) =>
        isDateMatchCondition({
          dateType,
          specificDates,
          addOnStartDate: startDate,
          overviewDate: state.date,
          mainServiceStartDate: mainService?.startDate,
          mainServiceEndDate: mainService?.endDate,
          mainServiceItemType: mainService?.serviceItemType,
        }),
      );
      const isOnlyEvaluation = !!evaluations?.length && !services?.length && !addOns?.length;
      let showServices = [];
      if (services?.length !== 0) {
        showServices = services;
      } else if (addOns?.length !== 0) {
        showServices = addOns;
      } else {
        showServices = evaluations;
      }

      return {
        pet,
        showServices,
        isEmptyServices,
        isOnlyEvaluation,
        addOns,
        evaluations,
      };
    });
  }, [serviceDetail, state.date]);

  return (
    <>
      {serviceDetailVM.map((detail) => {
        const { pet, showServices, isEmptyServices, isOnlyEvaluation, addOns, evaluations } = detail;

        if (!isMultiPets) {
          return (
            <div key={pet.id} className={COMMON_CONTAINER_CLASS}>
              {showServices.map((row) => {
                const staffName = row.staffName;
                return (
                  <div className={COMMON_CONTAINER_CLASS} key={row.id}>
                    <Text variant="regular-short" className={cn(COMMON_TEXT_CLASS, 'moe-text-primary')}>
                      {row.serviceName}
                    </Text>
                    <Text variant="small" className={cn(COMMON_TEXT_CLASS, 'moe-text-tertiary moe-mt-xxs')}>
                      {staffName}
                    </Text>
                  </div>
                );
              })}
              {!isEmptyServices && <AddOnsTag addOns={addOns} />}
              {!isOnlyEvaluation && <EvaluationsTag evaluations={evaluations} />}
            </div>
          );
        }
        return (
          <React.Fragment key={pet.id}>
            <div className={cn(COMMON_CONTAINER_CLASS, 'moe-flex moe-flex-wrap moe-items-center')}>
              <Heading size="5" className="moe-inline-block">
                {pet.petName}:&nbsp;
              </Heading>
              {showServices?.map((row, index) => {
                const staffName = row.staffName;
                return (
                  <div key={row.id}>
                    <Text variant="regular-short" className={cn(COMMON_TEXT_CLASS, 'moe-text-primary')}>
                      {row.serviceName}
                      <Condition if={staffName}>
                        <Text variant="small" as="span" className="moe-text-tertiary">
                          &nbsp;-&nbsp;{staffName}
                        </Text>
                      </Condition>
                      <Condition if={index !== showServices.length - 1}>
                        <span className="moe-text-tertiary">,&nbsp;</span>
                      </Condition>
                    </Text>
                  </div>
                );
              })}
            </div>
            {!isEmptyServices && <AddOnsTag addOns={addOns} />}
            {!isOnlyEvaluation && <EvaluationsTag evaluations={evaluations} />}
          </React.Fragment>
        );
      })}
    </>
  );
};
