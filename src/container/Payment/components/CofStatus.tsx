import { Tooltip, type TooltipProps, cn } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import React, { useMemo, useCallback, useEffect, useState } from 'react';
import { CustomerCompositeOverviewCOFStatus } from '@moego/api-web/moego/api/appointment/v1/overview_api';
import { isUndefined } from 'lodash';
import { toastApi } from '../../../components/Toast/Toast';
import { openGlobalModal } from '../../../components/globals/GlobalModals.store';
import { ApptTestIds } from '../../../config/testIds/apptDrawer';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../store/business/role.selectors';
import { checkCustomerMessagePermission } from '../../../store/message/message.actions';
import { getPaymentSettingInfo } from '../../../store/payment/actions/public/payment.actions';
import { CardAuthStatus } from '../../../store/payment/payment.boxes';
import { selectPaymentSettingInfo } from '../../../store/payment/payment.selectors';
import {
  MSG_NO_PERMISSION_TO_SEND_MESSAGE_TO_CUSTOMER,
  useBusinessIsWorkingLocation,
} from '../../../utils/BusinessUtil';
import { stripeAvailableCountryOption } from '../../CardProcessing/stripe/StripeSetup';
import {
  MajorCofDanger,
  MajorCofFailed,
  MajorCofSuccess,
  MajorCofWarning,
  MajorConfigOutlined,
  MinorCofDanger,
  MinorCofFailed,
  MinorCofOutlined,
  MinorCofSuccess,
  MinorCofWarning,
} from '@moego/icons-react';
import { useRecurringPaymentMethodList } from '../../../query/payment/useRecurringPaymentMethodList';

const CofStatusToolTip = ({
  content,
  visibleExplicitly,
  ...rest
}: Omit<TooltipProps, 'visible'> & { visibleExplicitly?: boolean }) => {
  const props: TooltipProps = {
    ...rest,
    content: typeof content === 'string' ? <span className="!moe-text-[#333]">{content}</span> : content,
    backgroundTheme: 'light',
  };
  if (typeof visibleExplicitly === 'boolean') {
    props.isOpen = visibleExplicitly;
  }
  return <Tooltip {...props} />;
};

const CofStatusToolTipOverlay = ({
  title,
  message,
  action,
  onClick,
  cofRequestSource,
}: {
  title: string;
  message: string;
  action: string;
  onClick: (e: React.MouseEvent) => void;
  cofRequestSource?: string;
}) => {
  const isWorkingLocation = useBusinessIsWorkingLocation();
  const disabled = !isWorkingLocation;
  return (
    <div className="!moe-flex !moe-flex-col">
      <div className="!moe-text-sm !moe-font-bold !moe-text-[#333333]">{title}</div>
      {action ? (
        <>
          <div className="!moe-mt-[4px] !moe-text-sm !moe-font-medium !moe-text-[#666666]">{message}</div>
          <div
            className={cn(
              '!moe-mt-[16px] !moe-self-end !moe-px-[12px] !moe-py-[6px] !moe-text-xs !moe-font-bold !moe-text-[#fff] !moe-bg-brand-bold !moe-rounded-[56px] !moe-cursor-pointer',
              disabled && '!moe-opacity-50',
            )}
            onClick={!disabled ? onClick : undefined}
            data-action={cofRequestSource ? 'cof-request' : undefined}
            data-source={cofRequestSource}
            data-testid={ApptTestIds.ApptCardIconReqCardBtn}
          >
            {action}
          </div>
        </>
      ) : null}
    </div>
  );
};

interface CofStatusIconProps {
  source: string;
  authEnabled: boolean;
  showAction?: boolean;
  iconClassName?: string;
  isMajorIcon?: boolean;
  tooltipsProps?: Omit<TooltipProps, 'children'>;
  cofStatus: CustomerCompositeOverviewCOFStatus;
  onTooltipOpenChange?: (isOpen: boolean) => void;
  onRequestCof?: () => void;
  onRequestAuthentication?: () => void;
}

export const CofStatusIcon = ({
  cofStatus,
  source,
  showAction,
  authEnabled,
  iconClassName,
  isMajorIcon,
  tooltipsProps,
  onRequestCof: onRequestCofOriginal,
  onRequestAuthentication: onRequestAuthenticationOriginal,
  onTooltipOpenChange,
}: CofStatusIconProps) => {
  // By default, the tooltip doesn't go until the mouse is moved out of the tooltip content. To force-dismiss the
  // tooltip when the action is clicked and card auth modal is open, we set the tooltip into controlled mode temporarily
  // with the following "toolTipVisible" state, and then set back to uncontrolled mode intermediately.
  const [toolTipVisible, setToolTipVisible] = useState<boolean>(false);
  const iconClass = cn(
    'moe-shrink-0 [&:not(:first-child)]:moe-ml-[12px] [&:last-of-type:not(:last-child)]:moe-mr-[12px]',
    iconClassName,
  );

  const getAction = useCallback(
    (action: string) => {
      return showAction ? action : '';
    },
    [showAction],
  );

  const onRequestCof = () => {
    setToolTipVisible(false);
    onRequestCofOriginal?.();
  };
  const onRequestAuthentication = () => {
    setToolTipVisible(false);
    onRequestAuthenticationOriginal?.();
  };
  const handleTooltipOpenChange = (isOpen: boolean) => {
    setToolTipVisible(isOpen);
    onTooltipOpenChange?.(isOpen);
  };

  if (cofStatus === CustomerCompositeOverviewCOFStatus.NO_CARD_ON_FILE) {
    const Icon = isMajorIcon ? MajorCofFailed : MinorCofFailed;
    return (
      <CofStatusToolTip
        {...tooltipsProps}
        visibleExplicitly={toolTipVisible}
        onOpenChange={handleTooltipOpenChange}
        content={
          <CofStatusToolTipOverlay
            title="No card on file"
            message="You can request card info."
            action={getAction('Request card info')}
            onClick={(e) => {
              e.stopPropagation();
              onRequestCof();
            }}
            cofRequestSource={source}
          />
        }
      >
        <Icon className={iconClass} data-testid={ApptTestIds.ApptCardIconBtn} />
      </CofStatusToolTip>
    );
  }
  const renderStatus = () => {
    const Icon = isMajorIcon ? MajorConfigOutlined : MinorCofOutlined;
    return <Icon className={iconClass} data-testid={ApptTestIds.ApptCofStatusYesAuthOffIcon} />;
  };
  const renderStatusWithAuth = () => {
    switch (cofStatus) {
      case CustomerCompositeOverviewCOFStatus.AUTHORIZED: {
        const Icon = isMajorIcon ? MajorCofSuccess : MinorCofSuccess;
        return (
          <CofStatusToolTip
            content="Authenticated"
            visibleExplicitly={toolTipVisible}
            onOpenChange={handleTooltipOpenChange}
          >
            <Icon className={iconClass} data-testid={ApptTestIds.ApptCofStatusSuccessIcon} />
          </CofStatusToolTip>
        );
      }
      case CustomerCompositeOverviewCOFStatus.PENDING: {
        const Icon = isMajorIcon ? MajorCofWarning : MinorCofWarning;
        return (
          <CofStatusToolTip
            visibleExplicitly={toolTipVisible}
            onOpenChange={handleTooltipOpenChange}
            content={
              <CofStatusToolTipOverlay
                title="Unauthenticated"
                message="You can request card authentication."
                action={getAction('Request authentication')}
                onClick={onRequestAuthentication}
              />
            }
          >
            <Icon className={iconClass} data-testid={ApptTestIds.ApptCofStatusUnknownIcon} />
          </CofStatusToolTip>
        );
      }
      case CustomerCompositeOverviewCOFStatus.FAILED: {
        const Icon = isMajorIcon ? MajorCofDanger : MinorCofDanger;
        return (
          <CofStatusToolTip
            visibleExplicitly={toolTipVisible}
            onOpenChange={handleTooltipOpenChange}
            content={
              <CofStatusToolTipOverlay
                title="Authentication failed"
                message="You can request card info."
                action={getAction('Request card info')}
                onClick={onRequestCof}
              />
            }
          >
            <Icon className={iconClass} data-testid={ApptTestIds.ApptCofStatusFailIcon} />
          </CofStatusToolTip>
        );
      }
      default:
        // Should not reach
        return null;
    }
  };
  return authEnabled ? renderStatusWithAuth() : renderStatus();
};

interface Props {
  source: string;
  customerId: number;
  showAction?: boolean;
  iconClassName?: string;
  tooltipsProps?: Omit<TooltipProps, 'children'>;
  isMajorIcon?: boolean;
  cofStatus?: CustomerCompositeOverviewCOFStatus;
  onTooltipOpenChange?: (isOpen: boolean) => void;
}

// Sort order: Succeeded - Unknown - Failed
const cardAuthStatusOrderMap: Record<string, number> = {
  [CardAuthStatus.Succeeded]: 0,
  [CardAuthStatus.Unknown]: 1,
  [CardAuthStatus.Failed]: 2,
};

/**
 * Icon of customer's Card on File status. Note that the ancestor component must dispatch the action
 * "getStripeCustomerPaymentMethodList(customerId)".
 * @constructor
 */
export const CofStatus = React.memo(
  ({
    source,
    cofStatus: cofStatusProp,
    customerId,
    showAction = true,
    iconClassName,
    tooltipsProps,
    isMajorIcon,
    onTooltipOpenChange,
  }: Props) => {
    const dispatch = useDispatch();
    const store = useStore();
    const permissions = store.select(selectCurrentPermissions());
    const [business, paymentSetting] = useSelector(selectCurrentBusiness(), selectPaymentSettingInfo());
    const { data: recurringPaymentMethodList } = useRecurringPaymentMethodList({ customerId: customerId.toString() });

    const method = useMemo(() => {
      if (paymentSetting.cardAuthEnable === 1) {
        return recurringPaymentMethodList?.sort((a, b) => {
          const va = cardAuthStatusOrderMap[a.metadata?.authStatus || CardAuthStatus.Unknown];
          const vb = cardAuthStatusOrderMap[b.metadata?.authStatus || CardAuthStatus.Unknown];
          return va - vb;
        })[0];
      }
      return recurringPaymentMethodList?.[0];
    }, [paymentSetting.cardAuthEnable, recurringPaymentMethodList]);

    useEffect(() => {
      // Fetch payment setting if not fetched yet
      if (!paymentSetting.businessId) {
        dispatch(getPaymentSettingInfo());
      }
    }, [paymentSetting.businessId]);

    const cofStatus = useMemo(() => {
      if (!isUndefined(cofStatusProp)) {
        return cofStatusProp;
      }

      if (!method) {
        return CustomerCompositeOverviewCOFStatus.NO_CARD_ON_FILE;
      }

      switch (method.authStatus) {
        case CardAuthStatus.Succeeded:
          return CustomerCompositeOverviewCOFStatus.AUTHORIZED;
        case CardAuthStatus.Unknown:
          return CustomerCompositeOverviewCOFStatus.PENDING;
        case CardAuthStatus.Failed:
          return CustomerCompositeOverviewCOFStatus.FAILED;
        default:
          return CustomerCompositeOverviewCOFStatus.NO_CARD_ON_FILE;
      }
    }, [method, cofStatusProp]);

    const country = stripeAvailableCountryOption(business.country);
    if (!country) {
      return null;
    }
    if (business.preferSquare()) {
      return null;
    }
    const handleRequestCof = async () => {
      if (!permissions.has('viewFullMessage') || !permissions.has('viewMessageCenter')) {
        const data = await dispatch(checkCustomerMessagePermission(customerId));
        if (!data.result) {
          toastApi.error(MSG_NO_PERMISSION_TO_SEND_MESSAGE_TO_CUSTOMER);
          return;
        }
      }
      dispatch(openGlobalModal({ requestCof: { customerId } }));
    };
    const handleRequestAuthentication = async () => {
      dispatch(openGlobalModal({ requestCardAuth: { customerId } }));
    };

    return (
      <CofStatusIcon
        authEnabled={paymentSetting.cardAuthEnable === 1}
        cofStatus={cofStatus}
        source={source}
        showAction={showAction}
        tooltipsProps={tooltipsProps}
        iconClassName={iconClassName}
        isMajorIcon={isMajorIcon}
        onRequestCof={handleRequestCof}
        onRequestAuthentication={handleRequestAuthentication}
        onTooltipOpenChange={onTooltipOpenChange}
      />
    );
  },
);
