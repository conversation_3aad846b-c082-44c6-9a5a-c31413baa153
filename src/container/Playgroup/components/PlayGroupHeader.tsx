import { <PERSON><PERSON><PERSON><PERSON><PERSON>, MajorPrintOutlined, SegmentControl } from '@moego/ui';
import React from 'react';
import { SwitchBusinessDropdown } from '../../../components/Business/SwitchBusinessDropdown';
import { DayDimensionDatePicker } from '../../../components/DatePicker/DayDimensionDatePicker';
import { WeeklyDatePicker } from '../../../components/DatePicker/WeeklyDatePicker';
import { MultiTypePrintModal } from '../../../components/MultiTypePrintModal/MultiTypePrintModal';
import { PrintCardType } from '../../../components/MultiTypePrintModal/MultiTypePrintModal.options';
import { Switch } from '../../../components/SwitchCase';
import { PlaygroupTestIds } from '../../../config/testIds/playgroup';
import { useBool } from '../../../utils/hooks/useBool';
import { ViewType, usePlaygroupContext } from '../Playgroup.context';

export const PlayGroupHeader = () => {
  const { selectedDate, setSelectedDate, viewType, setViewType } = usePlaygroupContext();
  const showPrintCardModal = useBool();

  return (
    <div className="moe-flex moe-justify-between">
      <div className="moe-flex moe-items-center moe-pb-spacing-m">
        <SegmentControl
          className="moe-mr-8px-200"
          value={viewType + ''}
          onChange={(key) => {
            setViewType(+key);
          }}
        >
          {ViewType.values.map((viewTypeValue) => {
            const viewTypeLabel = ViewType.mapLabels[viewTypeValue];
            return (
              <SegmentControl.Item
                classNames={{
                  content: 'moe-w-[58px] moe-justify-center',
                }}
                value={viewTypeValue + ''}
                key={viewTypeValue}
                label={viewTypeLabel}
              />
            );
          })}
        </SegmentControl>
        <Switch>
          <Switch.Case if={viewType === ViewType.Day}>
            <DayDimensionDatePicker
              className="moe-h-[40px] moe-ml-8px-200"
              value={selectedDate}
              onChange={(date) => {
                setSelectedDate(date);
              }}
            />
          </Switch.Case>
          <Switch.Case if={viewType === ViewType.Week}>
            <WeeklyDatePicker
              value={selectedDate}
              onChange={(date) => {
                setSelectedDate(date);
              }}
            />
          </Switch.Case>
        </Switch>
        <SwitchBusinessDropdown scene="working" onChange={() => {}} className="moe-h-[40px]"></SwitchBusinessDropdown>
      </div>
      <IconButton
        tooltip="Print playgroups"
        className="moe-shrink-0"
        icon={<MajorPrintOutlined />}
        variant="secondary"
        size="l"
        onPress={showPrintCardModal.open}
        data-testid={PlaygroupTestIds.PlaygroupPrintBtn}
      />
      <MultiTypePrintModal
        from="playgroup"
        isOpen={showPrintCardModal.value}
        onClose={showPrintCardModal.close}
        cardTypeOptions={[PrintCardType.Playgroup]}
        date={selectedDate}
        defaultCardType={PrintCardType.Playgroup}
        title="Print playgroups"
      />
    </div>
  );
};
