import { type ListStaffGroupByRoleResultRoleStaffGroup } from '@moego/api-web/moego/api/organization/v1/staff_api';
import {
  AppointmentTaskCategory,
  type AppointmentTaskSchedule,
} from '@moego/api-web/moego/models/appointment/v1/appointment_task_enums';
import { Tabs } from '@moego/ui';
import { type ColumnSort } from '@moego/ui/dist/esm/components/Table/Table.types';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useEffect, useRef, useState } from 'react';
import { useSetState } from 'react-use';
import { MultiTypePrintModal } from '../../components/MultiTypePrintModal/MultiTypePrintModal';
import { PrintCardType } from '../../components/MultiTypePrintModal/MultiTypePrintModal.options';
import { LayoutContainer } from '../../layout/LayoutContainer';
import { PATH_HOME_TASK } from '../../router/paths';
import { currentBusinessIdBox } from '../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { listStaffGroupByRole } from '../../store/staff/staff.actions';
import { staffMapBox } from '../../store/staff/staff.boxes';
import { selectBusinessStaffs } from '../../store/staff/staff.selectors';
import { DATE_FORMAT_EXCHANGE } from '../../utils/DateTimeUtil';
import { useRouteParams } from '../../utils/RoutePath';
import { useAsyncEffect } from '../../utils/hooks/useAsyncEffect';
import { useBool } from '../../utils/hooks/useBool';
import { stringToDateMessage } from '../../utils/utils';
import { TaskContext } from './Tasks.context';
import { RowSelectionActions } from './components/RowSelectionActions';
import { type TaskFilterRef, TasksFilter } from './components/TasksFilter';
import { TasksHeader } from './components/TasksHeader';
import { TasksTable } from './components/TasksTable';
import { useFilter } from './hooks/useFilter';
import { useGetTabGroupList } from './hooks/useGetTabGroupList';
import { useGetTasksParams } from './hooks/useGetTasksParams';
import { useTasksList } from './hooks/useTasksList';

interface TasksStateType {
  date: dayjs.Dayjs;
}

export const Tasks = memo(() => {
  const { date } = useRouteParams(PATH_HOME_TASK);
  const dispatch = useDispatch();
  const [business, staffList, staffMap, businessId] = useSelector(
    selectCurrentBusiness,
    selectBusinessStaffs(),
    staffMapBox,
    currentBusinessIdBox,
  );
  const [state, setState] = useSetState<TasksStateType>({ date: dayjs(date) });
  const [tableSelectedStatus, setTableSelectedStatus] = useSetState({
    selectedRowIds: {}, // 非 group 级别的单列 item 被选中
    selectedGroupIds: {}, // group 级别的 checkbox 被选中，不论 group 下的 item 是否全选
    selectedAllGroupIds: {}, // group 级别的 checkbox 被选中，group 下的 item 必须全选
  });
  const [fetchedGroupId, setFetchedGroupId] = useState<string | undefined>();
  const [staffsGroupByRole, setStaffsGroupByRole] = useState<ListStaffGroupByRoleResultRoleStaffGroup[]>([]);
  const [sorting, setSorting] = useState<ColumnSort>();
  const showPrintCardModal = useBool(false);
  const { filterData, updateFilterData, resetFilterData } = useFilter();
  const [taskType, setTaskType] = useState<AppointmentTaskSchedule | AppointmentTaskCategory>(
    AppointmentTaskCategory.FEEDING,
  );
  const filterRef = useRef<TaskFilterRef>(null);
  const scrollY = useRef<HTMLDivElement>(null);

  useAsyncEffect(async () => {
    const staffsRoleList = await dispatch(
      listStaffGroupByRole({
        businessId: business.id?.toString(),
        date: stringToDateMessage(state.date.format(DATE_FORMAT_EXCHANGE)),
      }),
    );
    setStaffsGroupByRole(staffsRoleList);
  }, [businessId, state.date]);

  const {
    tabCounts,
    refreshTaskList,
    tableLoading: taskLoading,
    groupLoading,
    taskGroupIdsMap,
    taskList,
    clearTaskGroupIdsMap,
    groupDetailInfoMap,
  } = useTasksList();

  const tabOptions = useGetTabGroupList(tabCounts, filterData.groupBy);

  const taskListInput = useGetTasksParams(businessId, state.date, filterData, taskType, sorting);

  // 获取 tab 级别下的数据
  useEffect(() => {
    refreshTaskList(taskListInput, fetchedGroupId);
  }, [taskListInput, filterData, fetchedGroupId, sorting]);

  // 切换 tab、switch、date、biz 时清空 table 的折叠、选中态
  useEffect(() => {
    refreshTaskList(taskListInput);
    setFetchedGroupId(undefined);
    resetTableSelectedStatus();
  }, [taskType, businessId, state.date, filterData.groupBy]);

  // 改变 table 查询结果时，置空之前选中状态
  useEffect(() => {
    resetTableSelectedStatus();
  }, [filterData.keywords, filterData.staffId, filterData.status, filterData.careType]);

  const resetTableSelectedStatus = () => {
    clearTaskGroupIdsMap();
    setTableSelectedStatus({
      selectedRowIds: {},
      selectedGroupIds: {},
      selectedAllGroupIds: {},
    });
  };

  return (
    <TaskContext.Provider
      value={{
        taskType,
        taskState: state,
        staffsRoleList: staffsGroupByRole,
        groupBy: filterData.groupBy,
        filter: taskListInput.filter,
        refreshList: async () => {
          await refreshTaskList(taskListInput, fetchedGroupId);
          filterRef.current?.refetchFilterStatus();
        },
      }}
    >
      <LayoutContainer className="moe-bg-white moe-px-0">
        <div className="moe-px-spacing-m moe-overflow-auto" ref={scrollY}>
          <div className="moe-flex moe-pb-spacing-m moe-items-center">
            <TasksHeader
              onDateChange={(date) => {
                setState({ date });
              }}
              value={state.date}
            />
            {/* 暂时先不加 */}
            {/*  eslint-disable-next-line sonarjs/no-commented-code */}
            {/* <IconButton
              tooltip="Print"
              className="moe-shrink-0"
              icon={<MajorPrintOutlined />}
              variant="secondary"
              size="l"
              onPress={() => {
                showPrintCardModal.open();
              }}
            /> */}
          </div>

          <TasksFilter
            ref={filterRef}
            currentDate={state.date.format(DATE_FORMAT_EXCHANGE)}
            data={filterData}
            updateFilterData={updateFilterData}
            resetFilterData={resetFilterData}
            staffGroupData={staffsGroupByRole}
          />
          <div className="moe-sticky moe-top-[-20px] moe-z-10 moe-bg-white moe-pt-m">
            <Tabs
              selectedKey={taskType + ''}
              onChange={(key) => {
                if (taskType !== Number(key)) {
                  setTaskType(Number(key));
                }
              }}
              classNames={{ panel: 'moe-p-0' }}
            >
              {tabOptions.map((tab) => {
                return <Tabs.Item key={tab.key} label={`${tab.label} (${tab.count})`}></Tabs.Item>;
              })}
            </Tabs>
          </div>
          <TasksTable
            fetchedGroupId={fetchedGroupId}
            taskLoading={taskLoading}
            groupLoading={groupLoading}
            tableSelectedStatus={tableSelectedStatus}
            setTableSelectedStatus={setTableSelectedStatus}
            scrollYContainer={scrollY.current}
            setFetchedGroupId={setFetchedGroupId}
            groupBy={filterData.groupBy}
            tabOptions={tabOptions}
            taskType={taskType}
            taskList={taskList}
            onSortingChange={setSorting}
            selectGroupRowMap={taskGroupIdsMap}
          />
        </div>
      </LayoutContainer>
      <RowSelectionActions
        groupDetailInfoMap={groupDetailInfoMap}
        taskGroupIdsMap={taskGroupIdsMap}
        selectedRowIds={tableSelectedStatus.selectedRowIds}
        selectedAllGroupIds={tableSelectedStatus.selectedAllGroupIds}
        onClear={resetTableSelectedStatus}
      />
      <MultiTypePrintModal
        from="tasks"
        isOpen={showPrintCardModal.value}
        onClose={showPrintCardModal.close}
        date={state.date}
        staffList={staffList
          .map((id) => {
            const staff = staffMap.mustGetItem(id);
            return {
              id: `${staff.id}`,
              name: staff.fullName(),
            };
          })
          .toJSON()}
        serviceItemTypes={filterData.careType ? [filterData.careType] : []}
        defaultCardType={PrintCardType.Activity}
      />
    </TaskContext.Provider>
  );
});
