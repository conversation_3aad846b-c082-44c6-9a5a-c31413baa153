import React from 'react';
import SvgNavOnlineBookingSvg from '../../assets/svg/nav-online-booking.svg';
import { LeftNavIcon } from './LeftNavIcon';
import { useDispatch, useSelector } from 'amos';
import { selectBusinessOnlineBookingRequests } from '../../store/onlineBooking/onlineBooking.selectors';
import { OnlineBookingRequestListType } from '../../store/onlineBooking/models/OnlineBookingRequest';
import { useMount } from 'react-use';
import { getBookingRequestListForRedDot } from '../../store/onlineBooking/actions/private/onlineBooking.actions';

export const OnlineBookingIcon = () => {
  const [requestList] = useSelector(selectBusinessOnlineBookingRequests(OnlineBookingRequestListType.Requests));
  const dispatch = useDispatch();

  const hasPendingBooking = requestList.total > 0;

  useMount(() => {
    dispatch(getBookingRequestListForRedDot());
  });

  return <LeftNavIcon src={SvgNavOnlineBookingSvg} dotVisible={hasPendingBooking} />;
};
