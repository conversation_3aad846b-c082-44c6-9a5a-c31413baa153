import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { type Key } from '@moego/ui';
import { useSelector } from 'amos';
import React, { type ReactNode, useMemo } from 'react';
import SvgIconClockSvg from '../../assets/svg/icon-clock.svg';
import SvgNavAgreementSvg from '../../assets/svg/nav-agreement.svg';
import SvgNavAutomatedWorkflowSvg from '../../assets/svg/nav-automated-workflow.svg';
import SvgNavCalendarSvg from '../../assets/svg/nav-calendar.svg';
import SvgNavHomeSvg from '../../assets/svg/nav-home.svg';
import SvgNavIntakeFormSvg from '../../assets/svg/nav-intake-form.svg';
import SvgNavOnlineBookingSvg from '../../assets/svg/nav-online-booking.svg';
import SvgNavRemindersSvg from '../../assets/svg/nav-reminders.svg';
import SvgNavRetailSvg from '../../assets/svg/nav-retail.svg';
import SvgNavReviewBoosterSvg from '../../assets/svg/nav-review-booster.svg';
import { MOE_ENABLE_ACTIVITY_LOG_KEY } from '../../config/host/const';
import {
  PATH_ACTIVITY_LOG,
  PATH_AGREEMENT_SETTING,
  PATH_AUTOMATED_WORKFLOW,
  PATH_DISCOUNT,
  PATH_DISCOUNTS,
  PATH_DISCOUNT_CREATE,
  PATH_DISCOUNT_EDIT,
  PATH_GROOMING_CALENDAR,
  PATH_HOME_OVERVIEW,
  PATH_INTAKE_FORM,
  PATH_MARKETING_CAMPAIGN,
  PATH_MARKETING_CAMPAIGN_PANEL,
  PATH_MESSAGE_CENTER,
  PATH_ONLINE_BOOKING,
  PATH_ONLINE_BOOKING_ABANDON_LIST,
  PATH_PRODUCT,
  PATH_REMINDER_LIST,
  PATH_REVIEW_BOOSTER,
  PATH_SETTING,
} from '../../router/paths';
import { selectCurrentPermissions } from '../../store/business/role.selectors';
import { type PricingPermissionKey } from '../../store/company/company.boxes';
import { selectBDFeatureEnable, selectPricingPermission } from '../../store/company/company.selectors';
import { type RoutePath } from '../../utils/RoutePath';
import { GrowthBookFeatureList } from '../../utils/growthBook/growthBook.config';
import { ReportActionName } from '../../utils/reportType';
import { reportData } from '../../utils/tracker';
import { useClientListMenuData } from './useClientListMenuData';
import { useFinanceMenuData } from '../components/FinanceMenu/useFinanceMenuData';
import { MessageCenterIcon } from '../components/LeftNavIcon';
import { MarketingCampaignIcon } from '../components/MarketingCampaignIcon';
import { SettingsIcon } from '../components/SettingsIcon';
import { useGetBDCalendarNav } from './useGetBDCanlenderNav';
import { useLoyaltyMenuData } from './useLoyaltyMenuData';
import { usePaymentMenuData } from './usePaymentMenuData';
import { useReportMenuData } from './useReportMenuData';
import { OnlineBookingIcon } from '../components/OnlineBookingIcon';

export interface NavMenuItemData {
  /**
   * 菜单项的唯一标识（包括不同层级 Menu），用于存储数据，没有特殊含义，理论上可以随意设置，推荐使用路由路径
   */
  id: Key;
  route?: RoutePath;
  /**
   * 设置菜单跳转路由，link 有值时，for 不生效
   */
  link?: string;
  /**
   * 链接到的路由，值为有效菜单项的 id
   */
  for?: Key;
  onAction?: () => void;
  title?: ReactNode;
  icon?: ReactNode;
  limit?: PricingPermissionKey | PricingPermissionKey[];
  children?: NavMenuItemData[];

  tooltip?: string;
  /**
   * 有部分路由的首页可能并不是 link 本身，例如 link 为 /foo 但实际首页为 /foo/list，此时通过该属性指定以防止点击菜单时重复刷新首页
   */
  mainPath?: string;
  /**
   * 手动指定子路径，用于补充路由无法自动匹配的情况
   */
  subPaths?: string[];

  /**
   * 自定义渲染
   * menu 当前菜单的渲染结果
   * isNarrow 是否为窄菜单
   */
  render?: (menu: ReactNode, isNarrow: boolean) => ReactNode;
}

export interface Entity<T> {
  key: Key;
  data: T;
  parent: Entity<T> | null;
  level: number;
  path: Key[];
  isLeaf: boolean;
}

export const useNavMenuData = () => {
  const [permissions, pricingPermission, boardingDaycareFeatureEnable] = useSelector(
    selectCurrentPermissions,
    selectPricingPermission(),
    selectBDFeatureEnable,
  );
  const workflowEnable = useFeatureIsOn(GrowthBookFeatureList.EnableWorkflow);
  const bDCalendarNav = useGetBDCalendarNav();

  const enableActivityLog = useFeatureIsOn('activity_log') || !!sessionStorage.getItem(MOE_ENABLE_ACTIVITY_LOG_KEY);

  const loyaltyMenuData = useLoyaltyMenuData();
  const reportMenuData = useReportMenuData();
  const paymentMenuData = usePaymentMenuData();
  const financeMenuData = useFinanceMenuData();
  const clientListMenuData = useClientListMenuData();

  const navMenuData: NavMenuItemData[] = useMemo(() => {
    const calendarMenuData: NavMenuItemData[] = boardingDaycareFeatureEnable
      ? [
          {
            id: 'home',
            icon: SvgNavHomeSvg,
            title: 'Home',
            link: PATH_HOME_OVERVIEW.build(),
          },
          {
            id: 'calendar',
            icon: SvgNavCalendarSvg,
            title: 'Calendar',
            for: 'calendar/calendar',
            children: bDCalendarNav,
          },
        ]
      : [
          {
            id: 'calendar',
            icon: SvgNavCalendarSvg,
            title: 'Appointments',
            link: PATH_GROOMING_CALENDAR.build(),
          },
        ];

    const onlineBookingMenuData: (NavMenuItemData | null)[] = [
      permissions.has('viewOnlineBooking')
        ? {
            id: 'onlineBooking',
            icon: <OnlineBookingIcon />,
            link: PATH_ONLINE_BOOKING.build(),
            title: 'Online booking',
            limit: 'onlineBooking',
          }
        : permissions.has('canAccessAbandonBookings')
          ? {
              id: 'abandonBookings',
              icon: SvgNavOnlineBookingSvg,
              link: PATH_ONLINE_BOOKING_ABANDON_LIST.build(),
              title: 'Online booking',
              limit: 'onlineBooking',
            }
          : null,
    ];

    return (
      [
        ...calendarMenuData,
        ...clientListMenuData,

        permissions.has('viewProductSetting') &&
          // pricingPermission.enable.has('retail') 其实就是取得后端retail_enable的白名单配置
          pricingPermission.enable.has('retail') && {
            id: 'retail',
            icon: SvgNavRetailSvg,
            tooltip: '',
            title: 'Retail',
            link: PATH_PRODUCT.build(),
          },
        permissions.has('viewMessageCenter') && {
          id: 'messages',
          icon: <MessageCenterIcon />,
          tooltip: '',
          title: 'Messages',
          link: PATH_MESSAGE_CENTER.build(),
          limit: 'autoMessage',
        },
        workflowEnable && {
          id: 'automatedWorkflow',
          icon: SvgNavAutomatedWorkflowSvg,
          tooltip: '',
          title: 'Workflow',
          link: PATH_AUTOMATED_WORKFLOW.build(),
          onAction: () => {
            reportData(ReportActionName.sideBarWorkflowClick);
          },
        },
        {
          id: 'reminders',
          icon: SvgNavRemindersSvg,
          title: 'Auto reminders',
          link: PATH_REMINDER_LIST.build(),
          limit: 'autoMessage',
        },
        ...onlineBookingMenuData,
        permissions.has('viewIntakeForm') && {
          id: 'intakeFrom',
          icon: SvgNavIntakeFormSvg,
          link: PATH_INTAKE_FORM.build(),
          title: 'Intake form',
          limit: 'intakeForm',
        },
        permissions.has('viewAgreement') && {
          id: 'agreements',
          icon: SvgNavAgreementSvg,
          link: PATH_AGREEMENT_SETTING.build(),
          title: 'Agreements',
          limit: 'agreement',
        },
        ...paymentMenuData,
        ...financeMenuData,
        (permissions.has('canAccessMarketingCampaigns') || permissions.has('canAccessDiscountModule')) && {
          id: 'marketing',
          icon: <MarketingCampaignIcon />,
          title: 'Marketing',
          for: 'marketing/discountCodes',
          limit: ['marketingCampaign', 'discountCode'],
          children: [
            ...(permissions.has('canAccessDiscountModule')
              ? ([
                  {
                    id: 'marketing/discountCodes',
                    link: PATH_DISCOUNTS.build(),
                    title: 'Discounts',
                    limit: 'discountCode',
                    subPaths: [PATH_DISCOUNT.path, PATH_DISCOUNT_CREATE.path, PATH_DISCOUNT_EDIT.path],
                  },
                ] satisfies NavMenuItemData[])
              : []),
            ...(permissions.has('canAccessMarketingCampaigns')
              ? ([
                  {
                    id: 'marketing/campaigns',
                    link: PATH_MARKETING_CAMPAIGN.build(),
                    title: 'Email campaigns',
                    limit: 'marketingCampaign',
                    mainPath: PATH_MARKETING_CAMPAIGN_PANEL.build({ panel: 'email' }),
                  },
                ] satisfies NavMenuItemData[])
              : []),
          ],
        },
        permissions.has('editReviewBooster') && {
          id: 'reviewBooster',
          link: PATH_REVIEW_BOOSTER.build(),
          icon: SvgNavReviewBoosterSvg,
          title: 'Review booster',
          limit: 'reviewBooster',
        },
        ...loyaltyMenuData,
        ...reportMenuData,
        (permissions.has('viewSetting') ||
          permissions.has('accessServiceSettings') ||
          permissions.has('accessClientPetSettings')) && {
          id: 'settings',
          link: PATH_SETTING.build(),
          icon: <SettingsIcon />,
          title: 'Settings',
        },
        enableActivityLog && {
          id: 'activityLog',
          link: PATH_ACTIVITY_LOG.build(),
          icon: SvgIconClockSvg,
          title: 'Activity Log',
        },
      ] satisfies (NavMenuItemData | null | false)[]
    ).filter(Boolean) as NavMenuItemData[];
  }, [
    boardingDaycareFeatureEnable,
    bDCalendarNav,
    permissions,
    pricingPermission.enable,
    workflowEnable,
    paymentMenuData,
    financeMenuData,
    loyaltyMenuData,
    reportMenuData,
    enableActivityLog,
    clientListMenuData,
  ]);

  const keyMap = useMemo(() => {
    const map: Record<Key, Entity<NavMenuItemData>> = {};
    const loop = (data: NavMenuItemData, parent: Entity<NavMenuItemData> | null = null, level = 0) => {
      const entity: Entity<NavMenuItemData> = {
        key: data.id,
        data,
        parent,
        level,
        path: parent ? [...parent.path, parent.key] : [],
        isLeaf: true,
      };
      map[data.id] = entity;
      if (data.children && data.children.length > 0) {
        entity.isLeaf = false;
        data.children.forEach((child) => loop(child, entity, level + 1));
      }
    };

    navMenuData.map((item) => loop(item, null, 0));

    return map;
  }, [navMenuData]);

  const { routeList, pathToKey } = useMemo(() => {
    const routeList: Set<string> = new Set();
    const pathToKey: Record<string, Key> = {};

    for (const [key, entity] of Object.entries(keyMap)) {
      const data = entity.data;
      if (!data || !data.link) continue;

      routeList.add(data.link);
      pathToKey[data.link] = key;
    }

    return {
      routeList,
      pathToKey,
    };
  }, [keyMap]);

  return {
    data: navMenuData,
    keyMap,
    routeList,
    pathToKey,
  };
};
