package com.moego.api.v3.appointment.service;

import com.google.protobuf.Timestamp;
import com.google.type.Interval;
import com.moego.api.v3.appointment.converter.LimitationGroupConverter;
import com.moego.api.v3.appointment.dto.CapacityTimeslotDTO;
import com.moego.api.v3.appointment.dto.OneDayTimeslotsDTO;
import com.moego.api.v3.shared.helper.CompanyHelper;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.api.appointment.v1.ListDaySlotInfosResult;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.organization.v1.AvailabilityType;
import com.moego.idl.models.organization.v1.LimitationGroup;
import com.moego.idl.models.organization.v1.LimitationGroupHitView;
import com.moego.idl.models.organization.v1.ScheduleType;
import com.moego.idl.models.organization.v1.SlotAvailabilityDay;
import com.moego.idl.models.organization.v1.SlotDailySetting;
import com.moego.idl.models.organization.v1.SlotFreeStaffServiceDef;
import com.moego.idl.models.organization.v1.SlotHourSetting;
import com.moego.idl.models.organization.v1.StaffAvailability;
import com.moego.idl.service.appointment.v1.GetStaffPetDetailsRequest;
import com.moego.idl.service.appointment.v1.GetStaffPetDetailsResponse;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.offering.v1.ListServiceRequest;
import com.moego.idl.service.offering.v1.ListServiceResponse;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.ListSlotFreeServicesRequest;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.utils.model.Pair;
import com.moego.server.business.client.IBusinessClosedDateClient;
import com.moego.server.business.dto.AppointmentPetIdDTO;
import com.moego.server.business.dto.ParsedCloseDate;
import com.moego.server.business.dto.PetSizeDTO;
import com.moego.server.business.dto.StaffTimeslotPetCountDTO;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.MoePetBreedDTO;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
public class OnlineBookingSlotService {

    private final OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub
            obStaffAvailabilityServiceBlockingStub;
    private final com.moego.idl.service.organization.v1.StaffServiceGrpc.StaffServiceBlockingStub staffService;
    private final IGroomingOnlineBookingService onlineBookingApi;
    private final IBusinessClosedDateClient iBusinessClosedDateClient;
    private final CompanyHelper companyHelper;
    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailServiceStub;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceClient;

    public static void offsetFillSlotWithMultiPetCal(
            final Map<String, Map<Long, OneDayTimeslotsDTO>> timeslotMapByDate,
            final Collection<StaffTimeslotPetCountDTO> petCountDTOList) {

        if (CollectionUtils.isEmpty(timeslotMapByDate) || CollectionUtils.isEmpty(petCountDTOList)) {
            return;
        }

        processSmallDataSetWithMultiCal(timeslotMapByDate, petCountDTOList);
    }

    private static void processSmallDataSetWithMultiCal(
            final Map<String, Map<Long, OneDayTimeslotsDTO>> timeslotMapByDate,
            final Collection<StaffTimeslotPetCountDTO> petCountDTOList) {

        petCountDTOList.stream()
                .sorted(Comparator.comparing(StaffTimeslotPetCountDTO::getSlotTime))
                .forEach(slotInfo -> {
                    slotInfo.getPetDetails().forEach(petDetail -> {
                        // time slot
                        setAppointmentPetPairsAndResetSlotTime(
                                findMatchingSlotByDateTime(timeslotMapByDate, slotInfo, petDetail), petDetail);
                    });
                    // daily
                    setDailyAppointmentPetPairs(
                            findMatchingSlotByDate(timeslotMapByDate, slotInfo.getSlotDate(), slotInfo.getStaffId()),
                            slotInfo);
                });

        // reset slot time
        petCountDTOList.clear();
        petCountDTOList.addAll(timeslotMapByDate.entrySet().stream()
                .map(entry -> entry.getValue().entrySet().stream()
                        .map(staffEntry -> {
                            return staffEntry.getValue().getTimeSlot().stream()
                                    .collect(Collectors.groupingBy(CapacityTimeslotDTO::getStartTime))
                                    .entrySet()
                                    .stream()
                                    .map(timeList -> {
                                        var staffTimeslotPetCountDTO = new StaffTimeslotPetCountDTO();
                                        staffTimeslotPetCountDTO.setStaffId(Math.toIntExact(staffEntry.getKey()));
                                        staffTimeslotPetCountDTO.setSlotDate(entry.getKey());
                                        staffTimeslotPetCountDTO.setSlotTime(timeList.getKey());
                                        staffTimeslotPetCountDTO.setPetDetails(timeList.getValue().stream()
                                                .flatMap(slot -> slot.getPetDetails().stream())
                                                .toList());

                                        // time slot
                                        var matchingSlotByDateTime = findMatchingSlotByDateTime(
                                                timeslotMapByDate,
                                                staffTimeslotPetCountDTO.getSlotDate(),
                                                staffTimeslotPetCountDTO.getStaffId(),
                                                staffTimeslotPetCountDTO.getSlotTime());
                                        matchingSlotByDateTime.ifPresent(
                                                capacityTimeslotDTO -> staffTimeslotPetCountDTO.setSlotTime(
                                                        capacityTimeslotDTO.getStartTime()));
                                        return staffTimeslotPetCountDTO;
                                    })
                                    .toList();
                        })
                        .flatMap(Collection::stream)
                        .toList())
                .flatMap(Collection::stream)
                .toList());
    }

    private static Optional<CapacityTimeslotDTO> findMatchingSlotByDateTime(
            Map<String, Map<Long, OneDayTimeslotsDTO>> slotIndexMap,
            StaffTimeslotPetCountDTO slotPet,
            final SmartScheduleGroomingDetailsDTO currentPetDetail) {

        var date = slotPet.getSlotDate();
        var staffId = slotPet.getStaffId();
        var startTime = slotPet.getSlotTime();

        // 获取指定日期和员工的时间槽位列表
        Map<Long, OneDayTimeslotsDTO> staffSlots = slotIndexMap.get(date);
        if (staffSlots == null) {
            return Optional.empty();
        }

        OneDayTimeslotsDTO timeSlot = staffSlots.get(staffId.longValue());
        if (Objects.isNull(timeSlot)) {
            return Optional.empty();
        }

        // 查找包含指定开始时间的时间槽位
        return timeSlot.getTimeSlot().stream()
                .sorted(Comparator.comparing(CapacityTimeslotDTO::getStartTime))
                .filter(slot -> {
                    if (!CollectionUtils.isEmpty(slot.getUsedAppointmentPetPairs())) {
                        // 如果已经有相同 appointment id + pet id 数据，则直接返回 true
                        if (slot.getUsedAppointmentPetPairs()
                                .contains(Pair.of(currentPetDetail.getGroomingId(), currentPetDetail.getPetId()))) {
                            return true;
                        }

                        // 如果已经占用，则判断当前 pet service 是否可以找到前序 appointment，且剩余容量是否足够
                        var beforeAppointmentFound = !CollectionUtils.isEmpty(slot.getPetDetails())
                                && slot.getPetDetails().stream()
                                        .filter(Predicate.not(SmartScheduleGroomingDetailsDTO::getIsSlotFreeService))
                                        .anyMatch(prevPetDetail -> Objects.equals(
                                                prevPetDetail.getGroomingId(), currentPetDetail.getGroomingId()));
                        var usedAppointmentPetPairs = new ArrayList<>(slot.getUsedAppointmentPetPairs());
                        usedAppointmentPetPairs.addAll(Stream.of(currentPetDetail)
                                .filter(Predicate.not(SmartScheduleGroomingDetailsDTO::getIsSlotFreeService))
                                .map(dto -> Pair.of(dto.getGroomingId(), dto.getPetId()))
                                .toList());
                        var occupiedPetPairsCount = usedAppointmentPetPairs.size();
                        if (beforeAppointmentFound && occupiedPetPairsCount <= slot.getCapacity()) {
                            return true;
                        }
                    }

                    // 如果没有占用，则直接判断 serviceStartTime 是否在 slot 内
                    return slot.contains(startTime);
                })
                .findFirst();
    }

    private static void setAppointmentPetPairsAndResetSlotTime(
            final Optional<CapacityTimeslotDTO> timeslotMapByDate, final SmartScheduleGroomingDetailsDTO petDetail) {
        if (timeslotMapByDate.isEmpty()) {
            return;
        }
        var capacityTimeslotDTO = timeslotMapByDate.get();
        var usedPetIds = capacityTimeslotDTO.getUsedAppointmentPetPairs();
        if (CollectionUtils.isEmpty(usedPetIds)) {
            usedPetIds = new HashSet<>();
        }
        usedPetIds.addAll(Stream.of(petDetail)
                .filter(Predicate.not(SmartScheduleGroomingDetailsDTO::getIsSlotFreeService))
                .map(dto -> Pair.of(dto.getGroomingId(), dto.getPetId()))
                .toList());
        capacityTimeslotDTO.setUsedAppointmentPetPairs(usedPetIds);

        var usedPetIdsIgnore = capacityTimeslotDTO.getUsedAppointmentPetPairsIgnoreSlotFreeService();
        if (CollectionUtils.isEmpty(usedPetIdsIgnore)) {
            usedPetIdsIgnore = new HashSet<>();
        }
        usedPetIdsIgnore.addAll(Stream.of(petDetail)
                .map(dto -> Pair.of(dto.getGroomingId(), dto.getPetId()))
                .toList());
        capacityTimeslotDTO.setUsedAppointmentPetPairsIgnoreSlotFreeService(usedPetIdsIgnore);

        var petDetails = capacityTimeslotDTO.getPetDetails();
        if (CollectionUtils.isEmpty(petDetails)) {
            petDetails = new ArrayList<>();
        }
        petDetails.add(petDetail);
        capacityTimeslotDTO.setPetDetails(petDetails);

        // slotInfo.setSlotTime(capacityTimeslotDTO.getStartTime());
    }

    private static void setDailyAppointmentPetPairs(
            final Optional<CapacityTimeslotDTO> timeslotMapByDate, final StaffTimeslotPetCountDTO slotInfo) {
        if (timeslotMapByDate.isEmpty()) {
            return;
        }
        var capacityTimeslotDTO = timeslotMapByDate.get();
        var usedPetIds = capacityTimeslotDTO.getUsedAppointmentPetPairs();
        if (CollectionUtils.isEmpty(usedPetIds)) {
            usedPetIds = new HashSet<>();
        }
        usedPetIds.addAll(slotInfo.getPetDetails().stream()
                .filter(Predicate.not(SmartScheduleGroomingDetailsDTO::getIsSlotFreeService))
                .map(dto -> Pair.of(dto.getGroomingId(), dto.getPetId()))
                .toList());
        capacityTimeslotDTO.setUsedAppointmentPetPairs(usedPetIds);

        var usedPetIdsIgnore = capacityTimeslotDTO.getUsedAppointmentPetPairsIgnoreSlotFreeService();
        if (CollectionUtils.isEmpty(usedPetIdsIgnore)) {
            usedPetIdsIgnore = new HashSet<>();
        }
        usedPetIdsIgnore.addAll(slotInfo.getPetDetails().stream()
                .map(dto -> Pair.of(dto.getGroomingId(), dto.getPetId()))
                .toList());
        capacityTimeslotDTO.setUsedAppointmentPetPairsIgnoreSlotFreeService(usedPetIdsIgnore);

        // slotInfo.setSlotTime(capacityTimeslotDTO.getStartTime());
    }

    private static Optional<CapacityTimeslotDTO> findMatchingSlotByDateTime(
            Map<String, Map<Long, OneDayTimeslotsDTO>> slotIndexMap, String date, long staffId, int startTime) {

        // 获取指定日期和员工的时间槽位列表
        Map<Long, OneDayTimeslotsDTO> staffSlots = slotIndexMap.get(date);
        if (staffSlots == null) {
            return Optional.empty();
        }

        OneDayTimeslotsDTO timeSlot = staffSlots.get(staffId);
        if (Objects.isNull(timeSlot)) {
            return Optional.empty();
        }

        // 查找包含指定开始时间的时间槽位
        return timeSlot.getTimeSlot().stream()
                .sorted(Comparator.comparing(CapacityTimeslotDTO::getStartTime))
                .filter(slot -> slot.contains(startTime))
                .findFirst();
    }

    private static Optional<CapacityTimeslotDTO> findMatchingSlotByDate(
            Map<String, Map<Long, OneDayTimeslotsDTO>> slotIndexMap, String date, long staffId) {

        // 获取指定日期和员工的时间槽位列表
        Map<Long, OneDayTimeslotsDTO> staffSlots = slotIndexMap.get(date);
        if (staffSlots == null) {
            return Optional.empty();
        }

        OneDayTimeslotsDTO timeSlot = staffSlots.get(staffId);
        if (Objects.isNull(timeSlot)) {
            return Optional.empty();
        }

        // 查找包含指定开始时间的时间槽位
        return Optional.ofNullable(timeSlot.getDailyTimeSlot());
    }

    public Map<Long, Set<Long>> getSlotFreeServices(Long companyId, Long businessId, Collection<Long> staffIds) {
        var obSetting = onlineBookingApi.getOBSetting(businessId.intValue());
        if (Objects.isNull(obSetting)) {
            return Map.of();
        }

        List<SlotFreeStaffServiceDef> slotFreeStaffServiceDefs;
        if (BooleanEnum.VALUE_TRUE.equals(obSetting.getAvailableTimeSync())) {
            slotFreeStaffServiceDefs = staffService
                    .listSlotFreeServices(com.moego.idl.service.organization.v1.ListSlotFreeServicesRequest.newBuilder()
                            .setBusinessId(businessId)
                            .addAllStaffIds(staffIds)
                            .build())
                    .getDefsList();
        } else {
            slotFreeStaffServiceDefs = obStaffAvailabilityServiceBlockingStub
                    .listSlotFreeServices(ListSlotFreeServicesRequest.newBuilder()
                            .setBusinessId(businessId)
                            .addAllStaffIds(staffIds)
                            .build())
                    .getDefsList();
        }

        var allAddonId = fetchAllAddOn(companyId, businessId).stream()
                .map(ServiceModel::getServiceId)
                .distinct()
                .toList();

        Map<Long, Set<Long>> staffServiceMap = slotFreeStaffServiceDefs.stream()
                .collect(Collectors.toMap(
                        SlotFreeStaffServiceDef::getStaffId,
                        slotFreeStaffServiceDef -> new HashSet<>(slotFreeStaffServiceDef.getServiceIdsList()),
                        (a, b) -> {
                            Set<Long> set = new HashSet<>(a);
                            set.addAll(b);
                            return set;
                        }));
        staffServiceMap.forEach((staffId, serviceIds) -> {
            serviceIds.addAll(allAddonId);
        });

        return staffServiceMap;
    }

    public List<ServiceModel> fetchAllAddOn(long companyId, long businessId) {
        List<ServiceModel> serviceModels = new ArrayList<>();
        fetchAllAddOn(companyId, businessId, 1, 1000, serviceModels);
        return serviceModels;
    }

    private void fetchAllAddOn(
            long companyId, long businessId, int pageNum, int pageSize, List<ServiceModel> serviceModels) {
        ListServiceResponse listServiceResponse = serviceClient.listService(ListServiceRequest.newBuilder()
                .setPagination(PaginationRequest.newBuilder()
                        .setPageNum(pageNum)
                        .setPageSize(pageSize)
                        .build())
                .setServiceType(ServiceType.ADDON)
                .addBusinessIds(businessId)
                .setTokenCompanyId(companyId)
                .setInactive(false)
                .build());
        if (CollectionUtils.isEmpty(listServiceResponse.getServicesList())) {
            return;
        }
        serviceModels.addAll(listServiceResponse.getServicesList());

        if (listServiceResponse.getPagination().getTotal() > pageNum * pageSize) {
            fetchAllAddOn(companyId, businessId, pageNum + 1, pageSize, serviceModels);
        }
    }

    public Map<String /* date */, Map<Long /* staff id */, OneDayTimeslotsDTO>> getStaffAvailabilityMapByDate(
            String startDate, String endDate, Long companyId, Long businessId, Collection<Long> staffIds) {

        Map<Long, StaffAvailability> shiftManagementStaffTimeMap;
        Map<Long, List<SlotAvailabilityDay>> shiftManagementOverrideStaffTime;
        var obSetting = onlineBookingApi.getOBSetting(businessId.intValue());
        if (Objects.isNull(obSetting)) {
            return Map.of();
        }
        if (BooleanEnum.VALUE_TRUE.equals(obSetting.getAvailableTimeSync())) {
            shiftManagementStaffTimeMap = queryShiftManagementStaffSlot(businessId, companyId, staffIds);
            shiftManagementOverrideStaffTime = queryShiftManagementOverrideStaffSlot(businessId, companyId, staffIds);
        } else {
            shiftManagementStaffTimeMap = getOnlineBookingStaffAvailabilityMap(companyId, businessId, staffIds);
            shiftManagementOverrideStaffTime = Map.of();
        }

        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        List<ParsedCloseDate> allClosedDate = iBusinessClosedDateClient.getParsedCloseDateByStartDateEndDate(
                businessId.intValue(), startDate, endDate);

        var dateRange = start.datesUntil(end.plusDays(1))
                .filter(date -> !isInClosedDate(date.toString(), allClosedDate))
                .toList();

        // 从timeslot的配置解析出需要查询日期里的所有的timeslot，以date为key，每个staff的timeslot的map为值
        Map<String /* date */, Map<Long /* staff id */, OneDayTimeslotsDTO>> timeslotMapByDate =
                new LinkedHashMap<>(dateRange.size());

        dateRange.forEach(localDate -> {
            var staffMap = shiftManagementStaffTimeMap.entrySet().stream()
                    .map(entry -> shiftManagementOverrideStaffTime.getOrDefault(entry.getKey(), List.of()).stream()
                            .filter(override -> Objects.equals(override.getOverrideDate(), localDate.toString()))
                            .findFirst()
                            .orElseGet(() -> getStaffSlotTimeDataByCurrentDate(entry.getValue(), localDate)))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(SlotAvailabilityDay::getStaffId, Function.identity()));

            staffMap.forEach((staffId, slotAvailabilityDay) -> {
                if (!staffIds.contains(staffId) || !slotAvailabilityDay.getIsAvailable()) {
                    return;
                }

                AtomicInteger endTime = new AtomicInteger(1439);
                var timeslotDTOList = slotAvailabilityDay.getSlotHourSettingListList().stream()
                        .map(OnlineBookingSlotService::convertSettingToDto)
                        .sorted(Comparator.comparing(CapacityTimeslotDTO::getStartTime)
                                .reversed())
                        .peek(currentTimeslotDTO -> {
                            currentTimeslotDTO.setEndTime(endTime.get());
                            endTime.set(currentTimeslotDTO.getStartTime());
                        })
                        .toList();

                var slotDailySetting = slotAvailabilityDay.getSlotDailySetting();

                // convert dto
                OneDayTimeslotsDTO timeslotDTO = new OneDayTimeslotsDTO();
                timeslotDTO.setIsSelected(slotAvailabilityDay.getIsAvailable());
                timeslotDTO.setTimeSlot(timeslotDTOList);
                timeslotDTO.setDailyTimeSlot(convertDailySettingToDto(slotDailySetting));

                // 以staffId为key，存放当天内的timeslot
                timeslotMapByDate
                        .computeIfAbsent(localDate.toString(), k -> new HashMap<>())
                        .put(staffId, timeslotDTO);
            });
        });

        return timeslotMapByDate;
    }

    @Nonnull
    private static CapacityTimeslotDTO convertSettingToDto(final SlotHourSetting timeSlot) {
        CapacityTimeslotDTO capacityTimeslotDTO = new CapacityTimeslotDTO();
        capacityTimeslotDTO.setCapacity(timeSlot.getCapacity());
        capacityTimeslotDTO.setStartTime(timeSlot.getStartTime());
        capacityTimeslotDTO.setUsedAppointmentPetPairs(new HashSet<>());
        capacityTimeslotDTO.setUsedAppointmentPetPairsIgnoreSlotFreeService(new HashSet<>());
        capacityTimeslotDTO.setLimitationGroups(timeSlot.getLimitationGroupsList());
        capacityTimeslotDTO.setPetDetails(new ArrayList<>());
        capacityTimeslotDTO.setNote(timeSlot.getNote());
        return capacityTimeslotDTO;
    }

    @Nonnull
    private static CapacityTimeslotDTO convertDailySettingToDto(SlotDailySetting slotDailySetting) {
        var capacityTimeSlot = new CapacityTimeslotDTO();
        capacityTimeSlot.setStartTime(slotDailySetting.getStartTime());
        capacityTimeSlot.setCapacity(slotDailySetting.getCapacity());
        capacityTimeSlot.setUsedAppointmentPetPairs(new HashSet<>());
        capacityTimeSlot.setUsedAppointmentPetPairsIgnoreSlotFreeService(new HashSet<>());
        capacityTimeSlot.setLimitationGroups(slotDailySetting.getLimitationGroupsList());
        capacityTimeSlot.setPetDetails(new ArrayList<>());
        return capacityTimeSlot;
    }

    private static boolean isInClosedDate(String currDay, List<ParsedCloseDate> ranges) {
        LocalDate curDate = LocalDate.parse(currDay);
        for (ParsedCloseDate range : ranges) {
            // [2020-05-22, 2020-05-27] 22到27的所有日期都是close day， currDay = 2020-05-24
            // close 条件： currDay >= range.start && currDay <= range.end
            if (!curDate.isBefore(range.getStart()) && !curDate.isAfter(range.getEnd())) {
                return true;
            }
        }
        return false;
    }

    public Map<Long, StaffAvailability> queryShiftManagementStaffSlot(
            Long businessId, Long companyId, Collection<Long> staffIds) {
        var requestBuilder = com.moego.idl.service.organization.v1.GetStaffAvailabilityRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setAvailabilityType(AvailabilityType.BY_SLOT);
        if (!CollectionUtils.isEmpty(staffIds)) {
            requestBuilder.addAllStaffIdList(staffIds);
        }

        var response = staffService.getStaffAvailability(requestBuilder.build());
        return response.getStaffAvailabilityListList().stream()
                .collect(Collectors.toMap(StaffAvailability::getStaffId, Function.identity(), (a, b) -> b));
    }

    public Map<Long, List<SlotAvailabilityDay>> queryShiftManagementOverrideStaffSlot(
            Long businessId, Long companyId, Collection<Long> staffIds) {
        var requestBuilder = com.moego.idl.service.organization.v1.GetStaffAvailabilityOverrideRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setAvailabilityType(AvailabilityType.BY_SLOT);
        if (!CollectionUtils.isEmpty(staffIds)) {
            requestBuilder.addAllStaffIds(staffIds);
        }

        var response = staffService.getStaffAvailabilityOverride(requestBuilder.build());
        return response.getOverrideDaysMap().entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey, staffAvailability -> staffAvailability.getValue().getSlotsList().stream()
                                .filter(day -> !(day.getIsAvailable()
                                        && day.getSlotDailySetting().getStartTime() < 0
                                        && day.getSlotDailySetting().getEndTime() < 0))
                                .toList()));
    }

    private Map<Long, StaffAvailability> getOnlineBookingStaffAvailabilityMap(
            Long companyId, Long businessId, final Collection<Long> staffIds) {
        var requestBuilder = GetStaffAvailabilityRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setAvailabilityType(AvailabilityType.BY_SLOT);
        if (!CollectionUtils.isEmpty(staffIds)) {
            requestBuilder.addAllStaffIdList(staffIds);
        }
        var response = obStaffAvailabilityServiceBlockingStub.getStaffAvailability(requestBuilder.build());
        return response.getStaffAvailabilityListList().stream()
                .filter(StaffAvailability::getIsAvailable)
                .collect(Collectors.toMap(StaffAvailability::getStaffId, Function.identity(), (a, b) -> b));
    }

    @Nullable
    static SlotAvailabilityDay getStaffSlotTimeDataByCurrentDate(
            StaffAvailability availability, LocalDate currentLocalDate) {
        var slotAvailabilityDayList = availability.getSlotAvailabilityDayListList();
        var scheduleType =
                availability.getScheduleType().getNumber() > 0 ? availability.getScheduleType() : ScheduleType.ONE_WEEK;
        var dayOfWeek = currentLocalDate.getDayOfWeek();

        // 如果设置为每周相同时间，则直接按星期获取时间数据
        if (ScheduleType.ONE_WEEK.equals(scheduleType)) {
            return slotAvailabilityDayList.stream()
                    .filter(availabilityDay -> availabilityDay.getScheduleType().getNumber() < 1
                            || ScheduleType.ONE_WEEK.equals(availabilityDay.getScheduleType()))
                    .filter(availabilityDay -> Objects.equals(
                            dayOfWeek.getValue(), availabilityDay.getDayOfWeek().getNumber()))
                    .findFirst()
                    .orElse(null);
        }

        var workingHourStartDate = StringUtils.hasText(availability.getSlotStartSunday())
                ? LocalDate.parse(availability.getSlotStartSunday())
                : currentLocalDate;
        // 计算 date 距离 absoluteStartDate 的天数差
        long daysBetween = ChronoUnit.DAYS.between(workingHourStartDate, currentLocalDate);
        // 计算 date 是距离 absoluteStartDate 的星期差
        long weeksBetween = Math.floorDiv(daysBetween, 7);
        // 计算循环中第几周，1 是第一周，2 是第二周，以此类推
        int weekNumber = Math.toIntExact(Math.floorMod(weeksBetween, scheduleType.getNumber())) + 1;

        return slotAvailabilityDayList.stream()
                .filter(availabilityDay -> Objects.equals(
                        weekNumber, availabilityDay.getScheduleType().getNumber()))
                .filter(availabilityDay -> Objects.equals(
                        dayOfWeek.getValue(), availabilityDay.getDayOfWeek().getNumber()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 查询员工时间槽宠物数量统计
     */
    public Collection<StaffTimeslotPetCountDTO> queryStaffTimeslotPetCount(
            Long companyId,
            Long businessId,
            List<String> needQueryDays,
            Set<Long> availableStaffIds,
            final Map<Long, Set<Long>> slotFreeServices) {
        // 构造当前时间段已有的预约数据
        var staffTimeslotPetCounts = queryStaffTimeslotPetCountByTime(
                companyId, businessId, new HashSet<>(needQueryDays), Set.of(), availableStaffIds, null);

        if (CollectionUtils.isEmpty(slotFreeServices)) {
            return staffTimeslotPetCounts;
        }

        return getStaffTimeslotPetCountDTOS(slotFreeServices, staffTimeslotPetCounts);
    }

    @Nonnull
    private static Collection<StaffTimeslotPetCountDTO> getStaffTimeslotPetCountDTOS(
            final Map<Long, Set<Long>> slotFreeServices,
            final Collection<StaffTimeslotPetCountDTO> staffTimeslotPetCounts) {
        Map<Integer /* appointment id*/, Map<Integer /* pet id */, Set<Integer> /* service id */>>
                appointmentPetServiceMap = new HashMap<>();
        staffTimeslotPetCounts.forEach(
                slotInfo -> slotInfo.getPetDetails().forEach(petDetail -> appointmentPetServiceMap
                        .computeIfAbsent(petDetail.getGroomingId(), k -> new HashMap<>())
                        .computeIfAbsent(petDetail.getPetId(), k -> new HashSet<>())
                        .add(petDetail.getServiceId())));

        // slot free service
        staffTimeslotPetCounts.forEach(slotInfo -> slotInfo.getPetDetails().forEach(petDetail -> {
            var serviceIds = Optional.of(appointmentPetServiceMap)
                    .map(map -> map.get(petDetail.getGroomingId()))
                    .map(map -> map.get(petDetail.getPetId()))
                    .orElse(Set.of());
            if (CollectionUtils.isEmpty(serviceIds)) {
                return;
            }
            var isSlotFreeService = slotFreeServices.containsKey(
                            slotInfo.getStaffId().longValue())
                    && slotFreeServices
                            .get(slotInfo.getStaffId().longValue())
                            .containsAll(serviceIds.stream().map(Long::valueOf).toList());
            petDetail.setIsSlotFreeService(isSlotFreeService);
        }));

        return staffTimeslotPetCounts;
    }

    /**
     * 根据appointmentDates、startTimes查找所选时间内appointment的pet 数量，同一个appt pet去重，不同appt不去重
     *
     * @param businessId
     * @param appointmentDates
     * @param startTimes
     * @return
     */
    public Collection<StaffTimeslotPetCountDTO> queryStaffTimeslotPetCountByTime(
            Long companyId,
            Long businessId,
            Set<String> appointmentDates,
            Set<Integer> startTimes,
            Set<Long> staffIds,
            Integer filterAppointmentId) {
        if (CollectionUtils.isEmpty(appointmentDates) || Objects.isNull(businessId)) {
            return Collections.emptyList();
        }

        String companyTimeZone = companyHelper.getCompanyTimeZoneName(companyId);
        var startDate = appointmentDates.stream().min(Comparator.naturalOrder()).orElseThrow();
        var endDate = appointmentDates.stream().max(Comparator.naturalOrder()).orElseThrow();

        LocalDate currentStartDate = LocalDate.parse(startDate);
        LocalDate finalEndDate = LocalDate.parse(endDate);

        // 创建异步任务列表
        List<CompletableFuture<List<SmartScheduleGroomingDetailsDTO>>> futures = new ArrayList<>();

        // 内部接口只支持最多查询 60 天，因此这里需要分段查询，step = 30 天
        // 这里不直接一次性查询60天是因为 queryByBusinessIdBetweenDates 方法会将 startDateRange=[endDate-60,endDate]
        // 如果 step = 60, 那么 endDate 经过 +60 和 -60 后，会导致 appointmentDate 在 endDate-60 之前的都查询不到
        while (currentStartDate.isBefore(finalEndDate) || currentStartDate.isEqual(finalEndDate)) {
            // 计算当前查询的结束日期：当前开始日期+30天），但不超过最终结束日期
            LocalDate periodStartDate = currentStartDate;
            LocalDate periodEndDate =
                    currentStartDate.plusDays(30).isAfter(finalEndDate) ? finalEndDate : currentStartDate.plusDays(30);

            // 创建异步任务
            futures.add(CompletableFuture.supplyAsync(
                    () -> queryByBusinessIdBetweenDates(
                            companyId, businessId, periodStartDate, periodEndDate, staffIds, companyTimeZone),
                    ThreadPool.getSubmitExecutor()));

            if (periodEndDate.isEqual(finalEndDate)) {
                break;
            }
            currentStartDate = periodEndDate;
        }

        var petDetails = futures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .filter(e -> PrimitiveTypeUtil.isNullOrZero(filterAppointmentId)
                        || !Objects.equals(filterAppointmentId, e.getGroomingId()))
                .filter(e -> appointmentDates.contains(e.getStartDate())
                        && (CollectionUtils.isEmpty(startTimes)
                                || startTimes.contains(e.getStartTime().intValue())))
                .toList();

        return getStaffTimeslotPetCountByPetDetails(petDetails);
    }

    /**
     * 根据 petDetails 计算每个 timeslot 的宠物数量和预约数量
     * 同一个pet下：
     * - service 可以连接上，(service2.start_time = service1.end_time)，这个pet只占用 service1 的 start_time 的slot
     * - service 不连接，中间有间隔，这个pet占用各个 service 的 start time 的那个slot
     *
     * @param petDetails 宠物预约详情列表
     * @return 每个 timeslot 的统计结果
     */
    public static List<StaffTimeslotPetCountDTO> getStaffTimeslotPetCountByPetDetails(
            List<SmartScheduleGroomingDetailsDTO> petDetails) {
        // 过滤无效数据
        var filteredDetails = petDetails.stream()
                .filter(e -> e.getPetId() != 0)
                .filter(e -> e.getStartTime() != null && e.getEndTime() != null)
                .toList();

        // 按照 date、startTime、staffId 分组
        Map<Triple<String, Integer, Integer>, List<SmartScheduleGroomingDetailsDTO>> slotStaffDetails = new HashMap<>();

        // 根据连续性处理每个 pet_detail
        filteredDetails.stream()
                .collect(Collectors.groupingBy(dto -> Triple.of(dto.getStartDate(), dto.getStaffId(), dto.getPetId())))
                .forEach((petDetailKey, details) -> {
                    String date = petDetailKey.getLeft();
                    Integer staffId = petDetailKey.getMiddle();

                    // 对详情按开始时间排序
                    details.sort(Comparator.comparing(SmartScheduleGroomingDetailsDTO::getStartTime));

                    // 检查连续性并记录 timeslot
                    for (int i = 0; i < details.size(); ) {
                        SmartScheduleGroomingDetailsDTO currentPetDetail = details.get(i);
                        Integer slotTime = currentPetDetail.getStartTime().intValue();
                        Triple<String, Integer, Integer> slotKey = Triple.of(date, slotTime, staffId);
                        List<SmartScheduleGroomingDetailsDTO> slotDetails =
                                slotStaffDetails.computeIfAbsent(slotKey, k -> new ArrayList<>());
                        slotDetails.add(currentPetDetail);

                        // 检查是否有连续的服务
                        int j = i + 1;
                        while (j < details.size()
                                && details.get(j - 1)
                                        .getEndTime()
                                        .equals(details.get(j).getStartTime())) {
                            slotDetails.add(details.get(j));
                            j++;
                        }

                        // 跳过已处理的连续 pet_detail
                        i = j;
                    }
                });

        // 构建结果
        var results = new ArrayList<StaffTimeslotPetCountDTO>();

        for (Map.Entry<Triple<String, Integer, Integer>, List<SmartScheduleGroomingDetailsDTO>> entry :
                slotStaffDetails.entrySet()) {
            Triple<String, Integer, Integer> slotGroup = entry.getKey();
            List<SmartScheduleGroomingDetailsDTO> slotDetails = entry.getValue();

            StaffTimeslotPetCountDTO dto = new StaffTimeslotPetCountDTO();
            dto.setSlotDate(slotGroup.getLeft());
            dto.setSlotTime(slotGroup.getMiddle());
            dto.setStaffId(slotGroup.getRight());
            dto.setPetDetails(slotDetails); // 设置原始详情列表

            dto.setPetCount((int) slotDetails.stream()
                    .map(SmartScheduleGroomingDetailsDTO::getPetId)
                    .distinct()
                    .count());

            dto.setAppointmentCount((int) slotDetails.stream()
                    .map(SmartScheduleGroomingDetailsDTO::getGroomingId)
                    .distinct()
                    .count());

            results.add(dto);
        }

        return results;
    }

    public List<SmartScheduleGroomingDetailsDTO> queryByBusinessIdBetweenDates(
            Long companyId,
            Long businessId,
            LocalDate startDate,
            LocalDate endDate,
            Set<Long> staffIds,
            String zoneId) {

        List<SmartScheduleGroomingDetailsDTO> petDetailDTOList = new ArrayList<>();
        ZonedDateTime startTime = startDate.atStartOfDay(ZoneId.of(zoneId));
        ZonedDateTime endTime = endDate.atTime(LocalTime.MAX).atZone(ZoneId.of(zoneId));

        GetStaffPetDetailsResponse staffPetDetails =
                petDetailServiceStub.getStaffPetDetails(GetStaffPetDetailsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addAllStaffIds(staffIds)
                        // 限制查询 60 天，避免查询过多数据导致索引失效
                        .setStartTimeRange(
                                buildInterval(endTime.minusDays(60).toEpochSecond(), endTime.toEpochSecond() - 1))
                        .setEndTimeRange(buildInterval(
                                startTime.toEpochSecond(),
                                startTime.plusDays(60).toEpochSecond() - 1))
                        .build());

        if (Objects.isNull(staffPetDetails) || staffPetDetails.getStaffPetDetailsCount() == 0) {
            return petDetailDTOList;
        }

        staffPetDetails.getStaffPetDetailsList().forEach(staffPetDetail -> {
            SmartScheduleGroomingDetailsDTO dto = new SmartScheduleGroomingDetailsDTO();
            dto.setId((int) staffPetDetail.getId());
            dto.setPetId((int) staffPetDetail.getPetId());
            dto.setStaffId((int) staffPetDetail.getStaffId());
            dto.setGroomingId((int) staffPetDetail.getAppointmentId());
            dto.setCustomerId((int) staffPetDetail.getCustomerId());
            dto.setServiceId((int) staffPetDetail.getServiceId());
            dto.setAppointmentDate(staffPetDetail.getAppointmentDate());
            dto.setStartDate(staffPetDetail.getStartDate());
            dto.setStartTime((long) staffPetDetail.getStartTime());
            dto.setEndTime((long) staffPetDetail.getEndTime());
            dto.setServiceItemType(staffPetDetail.getServiceItemType());
            dto.setIsBlock(staffPetDetail.getIsBlock());
            petDetailDTOList.add(dto);
        });

        return petDetailDTOList;
    }

    private static Interval buildInterval(long startTimestamp, long endTimestamp) {
        return Interval.newBuilder()
                .setStartTime(Timestamp.newBuilder().setSeconds(startTimestamp).build())
                .setEndTime(Timestamp.newBuilder().setSeconds(endTimestamp).build())
                .build();
    }

    @Nonnull
    public static List<ListDaySlotInfosResult.DaySlotInfo> getSlotInfoList(
            Map<String /* date */, Map<Long /* staff id */, OneDayTimeslotsDTO>> staffTimeSlotMap,
            Collection<StaffTimeslotPetCountDTO> petIdDTOList,
            Map<Integer, MoePetBreedDTO> petBreedMap,
            Map<Long, PetSizeDTO> petSizeMap,
            Map<Long, ServiceModel> serviceMap,
            Map<Integer, CustomerPetDetailDTO> petIdDetailMap) {

        // 构造当前时间段已有的预约数据
        Map<String, List<AppointmentPetIdDTO>> petAppointmentMap = petIdDTOList.stream()
                .map(dto -> dto.getPetDetails().stream()
                        .map(petDetail -> AppointmentPetIdDTO.builder()
                                .petId(petDetail.getPetId())
                                .staffId(dto.getStaffId())
                                .appointmentDate(dto.getSlotDate())
                                .appointmentStartTime(dto.getSlotTime())
                                .serviceIds(List.of(petDetail.getServiceId()))
                                .appointmentId(petDetail.getGroomingId())
                                .build())
                        .toList())
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(AppointmentPetIdDTO::getAppointmentDate));

        // 需要把当前的 breed 也放入 petBreeMap，因为 breed 可能会在 B 端被删除
        petIdDetailMap.values().stream()
                .filter(pet -> StringUtils.hasText(pet.getBreed()))
                .forEach(pet -> {
                    if (petBreedMap.values().stream().noneMatch(b -> b.getName().equals(pet.getBreed()))) {
                        petBreedMap.put(
                                pet.getBreed().hashCode(),
                                MoePetBreedDTO.builder()
                                        .id(pet.getBreed().hashCode())
                                        .name(pet.getBreed())
                                        .petTypeId(pet.getPetTypeId())
                                        .build());
                    }
                });

        return staffTimeSlotMap.entrySet().stream()
                .map(dateMap -> dateMap.getValue().entrySet().stream()
                        .map(staffMap -> {
                            return staffMap.getValue().getTimeSlot().stream()
                                    .map(timeSlot -> {
                                        var staffId = staffMap.getKey();
                                        var date = dateMap.getKey();

                                        var limitationGroupHitViews = judgePetLimitAndGetPetRemainQuantityV4(
                                                petAppointmentMap,
                                                petIdDetailMap,
                                                petBreedMap,
                                                petSizeMap,
                                                serviceMap,
                                                Math.toIntExact(staffId),
                                                date,
                                                timeSlot.getLimitationGroups(),
                                                timeSlot.getStartTime());
                                        var hasHitLimit = limitationGroupHitViews.stream()
                                                .anyMatch(LimitationGroupHitView::getHitLimit);

                                        return ListDaySlotInfosResult.DaySlotInfo.newBuilder()
                                                .setDate(date)
                                                .setStaffId(staffId)
                                                .setStartTime(timeSlot.getStartTime())
                                                .setEndTime(timeSlot.getEndTime())
                                                .setPetCapacity(timeSlot.getCapacity())
                                                .setUsedPetCapacity(timeSlot.getUsedAppointmentPetPairs()
                                                        .size())
                                                .setUsedFamilyCapacity(timeSlot.getUsedAppointmentPetPairs().stream()
                                                        .map(Pair::key)
                                                        .distinct()
                                                        .toList()
                                                        .size())
                                                .addAllLimitationGroups(timeSlot.getLimitationGroups())
                                                .setIncludeSlotFreeServices(timeSlot.getPetDetails().stream()
                                                        .anyMatch(
                                                                SmartScheduleGroomingDetailsDTO::getIsSlotFreeService))
                                                .setNote(timeSlot.getNote())
                                                .addAllLimitationGroupHitViews(limitationGroupHitViews)
                                                .setHitLimit(hasHitLimit)
                                                .build();
                                    })
                                    .toList();
                        })
                        .flatMap(Collection::stream)
                        .toList())
                .flatMap(Collection::stream)
                .toList();
    }

    @Nonnull
    public static List<ListDaySlotInfosResult.SettingDaySlotInfo> getDaySlotInfoList(
            final Map<String, Map<Long, OneDayTimeslotsDTO>> staffTimeSlotMap) {
        return staffTimeSlotMap.entrySet().stream()
                .map(dateMap -> dateMap.getValue().entrySet().stream()
                        .map(staffMap -> {
                            var dailyTimeSlot = staffMap.getValue().getDailyTimeSlot();
                            return ListDaySlotInfosResult.SettingDaySlotInfo.newBuilder()
                                    .setDate(dateMap.getKey())
                                    .setStaffId(staffMap.getKey())
                                    .setPetCapacity(dailyTimeSlot.getCapacity())
                                    .setUsedPetCapacity(dailyTimeSlot
                                            .getUsedAppointmentPetPairs()
                                            .size())
                                    .setAppointmentCount(
                                            dailyTimeSlot.getUsedAppointmentPetPairsIgnoreSlotFreeService().stream()
                                                    .map(Pair::key)
                                                    .distinct()
                                                    .toList()
                                                    .size())
                                    .addAllLimitationGroups(dailyTimeSlot.getLimitationGroups())
                                    .build();
                        })
                        .toList())
                .flatMap(Collection::stream)
                .toList();
    }

    public Map<Long, ServiceModel> getServiceMap(Long companyId, Integer businessId) {
        return fetchAllGroomingService(companyId, businessId).stream()
                .collect(Collectors.toMap(ServiceModel::getServiceId, Function.identity()));
    }

    public List<ServiceModel> fetchAllGroomingService(long companyId, long businessId) {
        List<ServiceModel> serviceModels = new ArrayList<>();
        fetchAllGroomingService(companyId, businessId, 1, 1000, serviceModels);
        return serviceModels;
    }

    private void fetchAllGroomingService(
            long companyId, long businessId, int pageNum, int pageSize, List<ServiceModel> serviceModels) {
        ListServiceResponse listServiceResponse = serviceClient.listService(ListServiceRequest.newBuilder()
                .setPagination(PaginationRequest.newBuilder()
                        .setPageNum(pageNum)
                        .setPageSize(pageSize)
                        .build())
                .addServiceItemTypes(ServiceItemType.GROOMING)
                .addBusinessIds(businessId)
                .setTokenCompanyId(companyId)
                .setInactive(false)
                .build());
        if (CollectionUtils.isEmpty(listServiceResponse.getServicesList())) {
            return;
        }
        serviceModels.addAll(listServiceResponse.getServicesList());

        if (listServiceResponse.getPagination().getTotal() > pageNum * pageSize) {
            fetchAllGroomingService(companyId, businessId, pageNum + 1, pageSize, serviceModels);
        }
    }

    private static List<LimitationGroupHitView> judgePetLimitAndGetPetRemainQuantityV4(
            Map<String, List<AppointmentPetIdDTO>> petAppointmentMap,
            Map<Integer, CustomerPetDetailDTO> petIdDetailMap,
            Map<Integer, MoePetBreedDTO> petBreedMap,
            Map<Long, PetSizeDTO> petSizeMap,
            Map<Long, ServiceModel> serviceMap,
            final Integer staffId,
            final String date,
            final List<LimitationGroup> limitGroups,
            final Integer startTime) {

        if (CollectionUtils.isEmpty(limitGroups)) {
            return List.of();
        }

        // subtract from existing appointment's pet quantity
        if (!petAppointmentMap.containsKey(date)) {
            return LimitationGroupConverter.INSTANCE.convertLimitationGroupListToHitViewList(limitGroups);
        }

        var matchedAppointmentPetIdDTOs = petAppointmentMap.get(date).stream()
                .filter(appointmentPetIdDTO -> {
                    if (Objects.isNull(startTime)) {
                        // working hour
                        return Objects.equals(appointmentPetIdDTO.getStaffId(), staffId);
                    } else {
                        // time slot
                        return (Objects.equals(appointmentPetIdDTO.getStaffId(), staffId)
                                && Objects.equals(appointmentPetIdDTO.getAppointmentStartTime(), startTime));
                    }
                })
                .toList();

        // subtract by size
        var weightList = matchedAppointmentPetIdDTOs.stream()
                .map(AppointmentPetIdDTO::getPetId)
                .distinct()
                .map(petIdDetailMap::get)
                .filter(Objects::nonNull)
                .filter(pet -> StringUtils.hasText(pet.getWeight()))
                .map(pet -> new BigDecimal(pet.getWeight()).setScale(2, RoundingMode.HALF_UP))
                .toList();
        List<LimitationGroupHitView> limitationGroupHitViewList =
                new ArrayList<>(subtractSizeQuantity(limitGroups, petSizeMap, weightList));

        // subtract by breed
        var breedList = matchedAppointmentPetIdDTOs.stream()
                .map(AppointmentPetIdDTO::getPetId)
                .distinct()
                .map(petIdDetailMap::get)
                .filter(Objects::nonNull)
                .map(CustomerPetDetailDTO::getBreed)
                .filter(StringUtils::hasText)
                .toList();
        limitationGroupHitViewList.addAll(subtractBreedQuantity(limitGroups, petBreedMap, breedList));

        // subtract by service
        var serviceIdList = matchedAppointmentPetIdDTOs.stream()
                .map(AppointmentPetIdDTO::getServiceIds)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .toList();
        limitationGroupHitViewList.addAll(subtractServiceQuantity(limitGroups, serviceMap, serviceIdList));

        return limitationGroupHitViewList;
    }

    private static List<LimitationGroupHitView> subtractSizeQuantity(
            List<LimitationGroup> limitGroups, final Map<Long, PetSizeDTO> petSizeMap, List<BigDecimal> weights) {

        List<LimitationGroupHitView> limitationGroupHitViewList = new ArrayList<>();

        for (final var limitGroup : limitGroups) {
            if (CollectionUtils.isEmpty(limitGroup.getPetSizeLimitsList())) {
                continue;
            }

            var onlyAcceptSelected = limitGroup.getOnlyAcceptSelected();

            Map<Set<org.apache.commons.lang3.tuple.Pair<BigDecimal, BigDecimal>>, Integer> limitSizeMap =
                    new HashMap<>(8);

            limitGroup.getPetSizeLimitsList().forEach(petSizeLimit -> {
                Set<org.apache.commons.lang3.tuple.Pair<BigDecimal, BigDecimal>> pairList =
                        petSizeLimit.getPetSizeIdsList().stream()
                                .map(petSizeMap::get)
                                .filter(Objects::nonNull)
                                .map(petSize -> {
                                    BigDecimal weightLow = new BigDecimal(petSize.getWeightLow());
                                    BigDecimal weightHigh = new BigDecimal(petSize.getWeightHigh());
                                    return org.apache.commons.lang3.tuple.Pair.of(weightLow, weightHigh);
                                })
                                .collect(Collectors.toSet());

                limitSizeMap.put(pairList, limitSizeMap.getOrDefault(pairList, 0) + petSizeLimit.getCapacity());
            });

            if (onlyAcceptSelected && !CollectionUtils.isEmpty(limitGroup.getPetSizeLimitsList())) {
                // 简化为整数精度：在 [0, 500] 的整数范围内计算未覆盖的连续区间
                int lowerBound = 0;
                int upperBound = 500;

                // 展开并收集所有已选择的区间
                List<org.apache.commons.lang3.tuple.Pair<BigDecimal, BigDecimal>> existingPairs = new ArrayList<>();
                limitSizeMap.keySet().forEach(setPairs -> {
                    if (!CollectionUtils.isEmpty(setPairs)) {
                        existingPairs.addAll(setPairs);
                    }
                });

                if (CollectionUtils.isEmpty(existingPairs)) {
                    // 如果没有已选择的区间，则整个 [0, 500] 都是未覆盖
                    limitSizeMap.put(
                            Set.of(org.apache.commons.lang3.tuple.Pair.of(
                                    BigDecimal.valueOf(lowerBound), BigDecimal.valueOf(upperBound))),
                            0);
                } else {
                    // 按整数覆盖标记
                    boolean[] covered = new boolean[upperBound - lowerBound + 1]; // 0..500 共 501 个点
                    for (org.apache.commons.lang3.tuple.Pair<BigDecimal, BigDecimal> p : existingPairs) {
                        if (p == null || p.getLeft() == null || p.getRight() == null) continue;
                        int l = Math.max(
                                lowerBound,
                                p.getLeft().setScale(0, RoundingMode.CEILING).intValue());
                        int r = Math.min(
                                upperBound,
                                p.getRight().setScale(0, RoundingMode.FLOOR).intValue());
                        if (l > r) continue;
                        for (int i = l; i <= r; i++) covered[i - lowerBound] = true;
                    }

                    // 扫描未覆盖的连续整数段，生成 gaps
                    Set<org.apache.commons.lang3.tuple.Pair<BigDecimal, BigDecimal>> gaps = new HashSet<>();
                    int i = lowerBound;
                    while (i <= upperBound) {
                        // 寻找未覆盖段起点
                        while (i <= upperBound && covered[i - lowerBound]) i++;
                        if (i > upperBound) break;
                        int start = i;
                        // 扩展到连续未覆盖段的终点
                        while (i <= upperBound && !covered[i - lowerBound]) i++;
                        int end = i - 1;
                        gaps.add(org.apache.commons.lang3.tuple.Pair.of(
                                BigDecimal.valueOf(start), BigDecimal.valueOf(end)));
                    }

                    if (!CollectionUtils.isEmpty(gaps)) {
                        limitSizeMap.put(gaps, 0);
                    }
                }
            }

            var builder = LimitationGroupHitView.newBuilder()
                    .addAllServiceLimits(limitGroup.getServiceLimitsList())
                    .addAllPetSizeLimits(limitGroup.getPetSizeLimitsList())
                    .addAllPetBreedLimits(limitGroup.getPetBreedLimitsList())
                    .setOnlyAcceptSelected(limitGroup.getOnlyAcceptSelected());
            if (CollectionUtils.isEmpty(limitSizeMap)) {
                limitationGroupHitViewList.add(builder.setHitLimit(false).build());
                continue;
            }

            limitSizeMap.forEach((setWeightPair, remainQuantity) -> {
                weights.stream()
                        .filter(weight -> setWeightPair.stream().anyMatch(weightPair -> {
                            BigDecimal weightLow = weightPair.getLeft();
                            BigDecimal weightHigh = weightPair.getRight();
                            return weightLow.compareTo(weight) <= 0 && weight.compareTo(weightHigh) <= 0;
                        }))
                        .forEach(weight -> limitSizeMap.put(setWeightPair, limitSizeMap.get(setWeightPair) - 1));
            });

            limitSizeMap.entrySet().stream()
                    .filter(entry -> entry.getValue() <= 0)
                    .findAny()
                    .ifPresentOrElse(hitLimit -> builder.setHitLimit(true), () -> builder.setHitLimit(false));

            limitationGroupHitViewList.add(builder.build());
        }
        return limitationGroupHitViewList;
    }

    private static List<LimitationGroupHitView> subtractBreedQuantity(
            List<LimitationGroup> limitGroups, final Map<Integer, MoePetBreedDTO> petBreedMap, List<String> breeds) {

        List<LimitationGroupHitView> limitationGroupHitViewList = new ArrayList<>();

        for (final var limitGroup : limitGroups) {
            if (CollectionUtils.isEmpty(limitGroup.getPetBreedLimitsList())) {
                continue;
            }

            var onlyAcceptSelected = limitGroup.getOnlyAcceptSelected();

            Map<Set<String>, Integer> limitBreedMap = new HashMap<>(256);

            limitGroup.getPetBreedLimitsList().forEach(petTypeBreed -> {
                Set<String> breedSet;

                if (petTypeBreed.getIsAllBreed()) {
                    breedSet = petBreedMap.values().stream()
                            .filter(breed ->
                                    Objects.equals(breed.getPetTypeId().longValue(), petTypeBreed.getPetTypeId()))
                            .map(MoePetBreedDTO::getName)
                            .collect(Collectors.toSet());
                } else {
                    breedSet = petTypeBreed.getBreedIdsList().stream()
                            .map(breedId -> petBreedMap.get(breedId.intValue()))
                            .filter(Objects::nonNull)
                            .map(MoePetBreedDTO::getName)
                            .collect(Collectors.toSet());
                }

                limitBreedMap.put(breedSet, limitBreedMap.getOrDefault(breedSet, 0) + petTypeBreed.getCapacity());
            });

            if (onlyAcceptSelected && !CollectionUtils.isEmpty(limitGroup.getPetBreedLimitsList())) {
                Set<String> selectedBreedSet = limitBreedMap.keySet().stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());
                petBreedMap.values().stream()
                        .map(MoePetBreedDTO::getName)
                        .filter(breedName -> !selectedBreedSet.contains(breedName))
                        .forEach(breedName -> limitBreedMap.put(Set.of(breedName), 0));
            }

            var builder = LimitationGroupHitView.newBuilder()
                    .addAllServiceLimits(limitGroup.getServiceLimitsList())
                    .addAllPetSizeLimits(limitGroup.getPetSizeLimitsList())
                    .addAllPetBreedLimits(limitGroup.getPetBreedLimitsList())
                    .setOnlyAcceptSelected(limitGroup.getOnlyAcceptSelected());
            if (CollectionUtils.isEmpty(limitBreedMap)) {
                limitationGroupHitViewList.add(builder.setHitLimit(false).build());
                continue;
            }

            limitBreedMap.forEach((breedSet, remainQuantity) -> {
                breeds.stream()
                        .filter(breedSet::contains)
                        .forEach(breed -> limitBreedMap.put(breedSet, limitBreedMap.get(breedSet) - 1));
            });

            limitBreedMap.entrySet().stream()
                    .filter(entry -> entry.getValue() <= 0)
                    .findAny()
                    .ifPresentOrElse(hitLimit -> builder.setHitLimit(true), () -> builder.setHitLimit(false));

            limitationGroupHitViewList.add(builder.build());
        }
        return limitationGroupHitViewList;
    }

    private static List<LimitationGroupHitView> subtractServiceQuantity(
            List<LimitationGroup> limitGroups, final Map<Long, ServiceModel> serviceMap, List<Integer> serviceIds) {

        // only use service, no need to consider add-on
        var onlyServiceMap = serviceMap.entrySet().stream()
                .filter(entry -> ServiceType.SERVICE.equals(entry.getValue().getType()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        List<LimitationGroupHitView> limitationGroupHitViewList = new ArrayList<>();

        for (final var limitGroup : limitGroups) {
            if (CollectionUtils.isEmpty(limitGroup.getServiceLimitsList())) {
                continue;
            }

            var onlyAcceptSelected = limitGroup.getOnlyAcceptSelected();

            Map<Set<Long>, Integer> limitServiceMap = new HashMap<>(64);

            limitGroup.getServiceLimitsList().forEach(serviceLimit -> {
                Set<Long> key;
                if (serviceLimit.getIsAllService()) {
                    key = onlyServiceMap.keySet();
                } else {
                    key = new HashSet<>(serviceLimit.getServiceIdsList());
                }
                limitServiceMap.put(key, limitServiceMap.getOrDefault(key, 0) + serviceLimit.getCapacity());
            });

            if (onlyAcceptSelected && !CollectionUtils.isEmpty(limitGroup.getServiceLimitsList())) {
                Set<Long> selectedServiceIds = limitServiceMap.keySet().stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());
                onlyServiceMap.keySet().stream()
                        .filter(serviceId -> !selectedServiceIds.contains(serviceId))
                        .forEach(serviceId -> limitServiceMap.put(Set.of(serviceId), 0));
            }

            var builder = LimitationGroupHitView.newBuilder()
                    .addAllServiceLimits(limitGroup.getServiceLimitsList())
                    .addAllPetSizeLimits(limitGroup.getPetSizeLimitsList())
                    .addAllPetBreedLimits(limitGroup.getPetBreedLimitsList())
                    .setOnlyAcceptSelected(limitGroup.getOnlyAcceptSelected());
            if (CollectionUtils.isEmpty(limitServiceMap)) {
                limitationGroupHitViewList.add(builder.setHitLimit(false).build());
                continue;
            }

            limitServiceMap.forEach((serviceIdSet, remainQuantity) -> {
                serviceIds.stream()
                        .map(Integer::longValue)
                        .filter(serviceIdSet::contains)
                        .forEach(serviceId -> limitServiceMap.put(serviceIdSet, limitServiceMap.get(serviceIdSet) - 1));
            });

            limitServiceMap.entrySet().stream()
                    .filter(entry -> entry.getValue() <= 0)
                    .findAny()
                    .ifPresentOrElse(hitLimit -> builder.setHitLimit(true), () -> builder.setHitLimit(false));

            limitationGroupHitViewList.add(builder.build());
        }
        return limitationGroupHitViewList;
    }
}
