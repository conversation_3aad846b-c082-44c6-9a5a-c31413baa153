package com.moego.api.v3.offering.helper;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.service.offering.v1.ListServiceRequest;
import com.moego.idl.service.offering.v1.ListServiceResponse;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@RequiredArgsConstructor
public class ServiceManagementHelper {

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceClient;

    public List<ServiceModel> fetchAllGroomingService(long companyId) {
        List<ServiceModel> serviceModels = new ArrayList<>();
        fetchAllGroomingService(companyId, 1, 1000, serviceModels);
        return serviceModels;
    }

    private void fetchAllGroomingService(long companyId, int pageNum, int pageSize, List<ServiceModel> serviceModels) {
        ListServiceResponse listServiceResponse = serviceClient.listService(ListServiceRequest.newBuilder()
                .setPagination(PaginationRequest.newBuilder()
                        .setPageNum(pageNum)
                        .setPageSize(pageSize)
                        .build())
                .addServiceItemTypes(ServiceItemType.GROOMING)
                .setTokenCompanyId(companyId)
                .setInactive(false)
                .build());
        if (CollectionUtils.isEmpty(listServiceResponse.getServicesList())) {
            return;
        }
        serviceModels.addAll(listServiceResponse.getServicesList());

        if (listServiceResponse.getPagination().getTotal() > pageNum * pageSize) {
            fetchAllGroomingService(companyId, pageNum + 1, pageSize, serviceModels);
        }
    }

    public List<ServiceModel> fetchAllAddOn(long companyId) {
        List<ServiceModel> serviceModels = new ArrayList<>();
        fetchAllAddOn(companyId, 1, 1000, serviceModels);
        return serviceModels;
    }

    private void fetchAllAddOn(long companyId, int pageNum, int pageSize, List<ServiceModel> serviceModels) {
        ListServiceResponse listServiceResponse = serviceClient.listService(ListServiceRequest.newBuilder()
                .setPagination(PaginationRequest.newBuilder()
                        .setPageNum(pageNum)
                        .setPageSize(pageSize)
                        .build())
                .setServiceType(ServiceType.ADDON)
                .setTokenCompanyId(companyId)
                .setInactive(false)
                .build());
        if (CollectionUtils.isEmpty(listServiceResponse.getServicesList())) {
            return;
        }
        serviceModels.addAll(listServiceResponse.getServicesList());

        if (listServiceResponse.getPagination().getTotal() > pageNum * pageSize) {
            fetchAllAddOn(companyId, pageNum + 1, pageSize, serviceModels);
        }
    }
}
