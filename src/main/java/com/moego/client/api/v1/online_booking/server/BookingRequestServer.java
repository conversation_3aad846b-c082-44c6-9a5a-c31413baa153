package com.moego.client.api.v1.online_booking.server;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.idl.client.online_booking.v1.Service.ServiceCase.BOARDING;
import static com.moego.idl.client.online_booking.v1.Service.ServiceCase.DAYCARE;
import static com.moego.idl.service.agreement.v1.AgreementServiceGrpc.AgreementServiceBlockingStub;
import static com.moego.idl.service.online_booking.v1.OBAvailabilitySettingServiceGrpc.OBAvailabilitySettingServiceBlockingStub;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static java.lang.Math.toIntExact;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.partitioningBy;
import static java.util.stream.Collectors.toMap;

import com.moego.client.api.v1.converter.DateConverter;
import com.moego.client.api.v1.customer.service.CustomerService;
import com.moego.client.api.v1.enterprise.service.CompanyService;
import com.moego.client.api.v1.online_booking.converter.ServiceConverter;
import com.moego.client.api.v1.online_booking.dto.DepositOrderParams;
import com.moego.client.api.v1.online_booking.service.BookingService;
import com.moego.client.api.v1.online_booking.service.CashierService;
import com.moego.client.api.v1.online_booking.service.DepositService;
import com.moego.client.api.v1.online_booking.service.MembershipService;
import com.moego.client.api.v1.online_booking.utils.OBSessionUtil;
import com.moego.client.api.v1.shared.helper.CompanyHelper;
import com.moego.client.api.v1.shared.helper.OBDepositHelper;
import com.moego.client.api.v1.shared.util.ProtobufUtil;
import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.BookOnlineDepositConst;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.ClientSourceEnum;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.PaymentStatusEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.order.DiscountType;
import com.moego.common.enums.order.LineApplyType;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.params.CustomerIdsParams;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.client.online_booking.v1.Address;
import com.moego.idl.client.online_booking.v1.Agreement;
import com.moego.idl.client.online_booking.v1.BoardingAddon;
import com.moego.idl.client.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.client.online_booking.v1.CalculateBookingRequestParams;
import com.moego.idl.client.online_booking.v1.CalculateBookingRequestResult;
import com.moego.idl.client.online_booking.v1.CancelBookingRequestParams;
import com.moego.idl.client.online_booking.v1.CancelBookingRequestResult;
import com.moego.idl.client.online_booking.v1.Customer;
import com.moego.idl.client.online_booking.v1.DaycareAddon;
import com.moego.idl.client.online_booking.v1.DiscountCode;
import com.moego.idl.client.online_booking.v1.Pet;
import com.moego.idl.client.online_booking.v1.PetServices;
import com.moego.idl.client.online_booking.v1.PreAuth;
import com.moego.idl.client.online_booking.v1.PrePay;
import com.moego.idl.client.online_booking.v1.RescheduleBookingRequestParams;
import com.moego.idl.client.online_booking.v1.RescheduleBookingRequestResult;
import com.moego.idl.client.online_booking.v1.Service;
import com.moego.idl.client.online_booking.v1.SubmitBookingRequestParams;
import com.moego.idl.client.online_booking.v1.SubmitBookingRequestResult;
import com.moego.idl.client.online_booking.v1.UpdateBookingRequestParams;
import com.moego.idl.client.online_booking.v1.UpdateBookingRequestResult;
import com.moego.idl.models.activity_log.v1.Resource;
import com.moego.idl.models.agreement.v1.AgreementModel;
import com.moego.idl.models.agreement.v1.ServiceType;
import com.moego.idl.models.appointment.v1.AppointmentBookOnlineStatus;
import com.moego.idl.models.appointment.v1.AppointmentNoShowStatus;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.idl.models.appointment.v1.CalendarCardType;
import com.moego.idl.models.appointment.v1.GroomingRescheduleDef;
import com.moego.idl.models.appointment.v2.PricingRuleApplySourceType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.grooming.v1.AppointmentSource;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.EvaluationModel;
import com.moego.idl.models.offering.v1.GroupClassInstance;
import com.moego.idl.models.offering.v1.ServiceCategoryModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v2.PetDetailCalculateDef;
import com.moego.idl.models.offering.v2.PetDetailCalculateResultDef;
import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceAvailabilityModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.online_booking.v1.DateRangeDef;
import com.moego.idl.models.online_booking.v1.DaycareAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceAvailabilityModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetailModel;
import com.moego.idl.models.order.v1.OrderLineDiscountModel;
import com.moego.idl.models.order.v1.OrderLineItemModel;
import com.moego.idl.models.order.v1.OrderLineTaxModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderModelV1;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.models.order.v1.OrderStatus;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.activity_log.v1.ActivityLogServiceGrpc.ActivityLogServiceBlockingStub;
import com.moego.idl.service.activity_log.v1.CreateActivityLogRequest;
import com.moego.idl.service.agreement.v1.ListUnsignedAgreementRequest;
import com.moego.idl.service.appointment.v1.AppointmentScheduleServiceGrpc.AppointmentScheduleServiceBlockingStub;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc.AppointmentServiceBlockingStub;
import com.moego.idl.service.appointment.v1.CancelAppointmentRequest;
import com.moego.idl.service.appointment.v1.RescheduleGroomingServiceRequest;
import com.moego.idl.service.appointment.v1.UpdateAppointmentSelectiveRequest;
import com.moego.idl.service.appointment.v2.ApplyPricingRuleRequest;
import com.moego.idl.service.appointment.v2.PricingRuleApplyServiceGrpc;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.service.offering.v1.GetEvaluationRequest;
import com.moego.idl.service.offering.v1.GetEvaluationResponse;
import com.moego.idl.service.offering.v1.GetServiceListRequest;
import com.moego.idl.service.offering.v1.GroupClassServiceGrpc;
import com.moego.idl.service.offering.v1.ListInstancesRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc.BookingRequestServiceBlockingStub;
import com.moego.idl.service.online_booking.v1.CreateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.CustomerAvailabilityServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetBoardingServiceAvailabilitySettingRequest;
import com.moego.idl.service.online_booking.v1.GetBoardingServiceAvailabilitySettingResponse;
import com.moego.idl.service.online_booking.v1.GetBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.GetBookingRequestResponse;
import com.moego.idl.service.online_booking.v1.GetDaycareServiceAvailabilitySettingRequest;
import com.moego.idl.service.online_booking.v1.GetDaycareServiceAvailabilitySettingResponse;
import com.moego.idl.service.online_booking.v1.ListAcceptedCustomerSettingRequest;
import com.moego.idl.service.online_booking.v1.ListBlockedCustomerRequest;
import com.moego.idl.service.online_booking.v1.TriggerBookingRequestAutoAcceptedRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestStatusRequest;
import com.moego.idl.service.online_booking.v1.UpdateGroomingAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateGroomingServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.UpsertGroomingAutoAssignRequest;
import com.moego.idl.service.order.v1.CreateOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.order.v1.SetTipsRequest;
import com.moego.idl.service.order.v1.UpdateOrderIncrRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.common.util.RequestUtils;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.server.business.client.IBusinessTaxClient;
import com.moego.server.business.dto.MoeBusinessTaxDto;
import com.moego.server.customer.api.ICustomerComposeService;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.api.ICustomerProfileRequestService;
import com.moego.server.customer.api.IPetService;
import com.moego.server.customer.client.ICustomerOnlineBookingClient;
import com.moego.server.customer.dto.AdditionalContactDTO;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.dto.SaveCustomerPetResultDto;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import com.moego.server.customer.params.CreateOBLoginTokenParams;
import com.moego.server.customer.params.CustomerPetAddParams;
import com.moego.server.customer.params.SaveWithPetCustomerVo;
import com.moego.server.grooming.api.IBookOnlineDepositService;
import com.moego.server.grooming.api.IBookOnlineQuestionService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.api.IOBService;
import com.moego.server.grooming.api.IOrderDecouplingFlowMarkerService;
import com.moego.server.grooming.client.IAbandonRecordClient;
import com.moego.server.grooming.client.IBookOnlineDepositClient;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.appointment.history.CancelLogDTO;
import com.moego.server.grooming.dto.appointment.history.ChangeTimeLogDTO;
import com.moego.server.grooming.dto.ob.BookOnlineQuestionSaveDTO;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.OBRequestSubmittedAutoTypeEnum;
import com.moego.server.grooming.params.InvoiceValueType;
import com.moego.server.grooming.params.MoeBookOnlineDepositVO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.payment.api.IPaymentPaymentService;
import com.moego.server.payment.api.IPaymentRefundService;
import com.moego.server.payment.api.IPaymentStripeService;
import com.moego.server.payment.client.IPaymentRefundClient;
import com.moego.server.payment.dto.PaymentDTO;
import com.moego.server.payment.params.CreateRefundByPaymentIdParams;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.IdentityHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class BookingRequestServer extends BookingRequestServiceGrpc.BookingRequestServiceImplBase {

    private static final String PREAUTH_PREFIX = "preauth_";

    private final IGroomingOnlineBookingService onlineBookingApi;
    private final ICustomerComposeService iCustomerComposeApi;
    private final ICustomerCustomerService customerApi;
    private final IPetService petApi;
    private final BookingRequestServiceBlockingStub bookingRequestStub;
    private final IBookOnlineQuestionService bookOnlineQuestionApi;
    private final ICustomerOnlineBookingClient customerOnlineBookingApi;
    private final IOBService obApi;
    private final OBAvailabilitySettingServiceBlockingStub obAvailabilitySettingStub;
    private final AgreementServiceBlockingStub agreementStub;
    private final CompanyService companyService;
    private final CustomerService customerService;
    private final AppointmentServiceBlockingStub appointmentService;
    private final AppointmentScheduleServiceBlockingStub appointmentScheduleService;
    private final ActivityLogServiceBlockingStub activityLogService;
    private final IBookOnlineDepositClient bookOnlineDepositClient;
    private final OBDepositHelper obDepositHelper;
    private final IPaymentRefundClient paymentRefundClient;
    private final BookingService bookingService;
    private final CustomerAvailabilityServiceGrpc.CustomerAvailabilityServiceBlockingStub
            customerAvailabilityServiceBlockingStub;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementServiceClient;
    private final IAbandonRecordClient abandonRecordApi;
    private final IBookOnlineDepositService bookOnlineDepositApi;
    private final OrderServiceGrpc.OrderServiceBlockingStub orderStub;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;
    private final EvaluationServiceGrpc.EvaluationServiceBlockingStub evaluationStub;
    private final IBusinessTaxClient taxApi;
    private final IPaymentPaymentService paymentApi;
    private final IPaymentStripeService paymentStripeApi;
    private final IPaymentRefundService paymentRefundApi;
    private final PricingRuleApplyServiceGrpc.PricingRuleApplyServiceBlockingStub pricingRuleApplyStub;
    private final IOrderDecouplingFlowMarkerService orderDecouplingFlowMarkerServiceApi;
    private final MembershipService membershipService;
    private final CashierService cashierService;
    private final GroupClassServiceGrpc.GroupClassServiceBlockingStub groupClassStub;
    private final DepositService depositService;
    private final FeatureFlagApi featureFlagApi;
    private final ICustomerProfileRequestService customerProfileRequestApi;
    private final CompanyHelper companyHelper;

    public static final String CANCEL_REASON = "Online booking request canceled by client in pet parent app";

    @Override
    @Auth(AuthType.OB)
    public void submitBookingRequest(
            SubmitBookingRequestParams request, StreamObserver<SubmitBookingRequestResult> responseObserver) {
        var obAnonymous = new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName());

        var biz = mustGetBusiness(obAnonymous);

        checkOBSubmitParam(request, biz);

        // 1. Create a customer if not exists
        var isNewCustomer = !isExistingCustomer();
        // 必须在创建 customer 之前调用才准确
        var depositOrderParams = buildDepositOrderParams(biz, getCustomerId(), request);
        var customerId = isNewCustomer ? addCustomer(request.getCustomer(), biz) : getCustomerId();

        var ctx = new CreateBookingRequestContext(biz.getCompanyId(), biz.getBusinessId(), isNewCustomer, customerId);

        // 应用 business/pet override 规则，C 端看到和提交的数据都是没有考虑 override 的
        // 在 C 端提交数据考虑 override 的情况下，这个逻辑可以下掉
        var customizedServices = listCustomizedService(ctx, request.getPetServicesList(), request.getCustomer());
        var req = buildSubmitBookingRequestParamsWithOverride(request, customizedServices);

        try {
            // 2. Save card on file
            saveCard(req.getCustomer(), ctx);

            // 3. Create pets if not exists
            var petToId = addPets(getPets(req), ctx);

            // 4. Add question save
            addQuestionSave(req, ctx, petToId);

            // 5. Create booking request
            long bookingRequestId = addBookingRequest(req, ctx, petToId);
            long orderId;

            // 6. Create order
            try {
                orderId = createOrder(ctx, req, bookingRequestId, customizedServices, depositOrderParams);
            } catch (Exception e) {
                // Create order failed, mark booking request as deleted, not show in booking request list
                updateBookingRequestStatus(bookingRequestId, BookingRequestStatus.DELETED);
                throw e;
            }

            // 7. Create agreements
            if (!req.getAgreementsList().isEmpty()) {
                ThreadPool.execute(() -> addAgreements(req.getAgreementsList(), bookingRequestId));
            }

            // 8. Create a session for new customer
            if (ctx.isNewCustomer()) {
                createSession(ctx, obAnonymous);
            }

            // 9. 标记 booking request 为已支付，处理 auto accept，发送通知等逻辑
            // 在不需要 payment 的情况下，处理 auto accept，发送通知等逻辑，整个流程结束
            // 如果需要 payment，会在 payment 成功后再处理 auto accept，发送通知等逻辑
            var isAutoAccepted = !isOrderDecouplingFlow(req) && triggerBookingRequestAutoAccepted(bookingRequestId);

            // 10. Remove abandon record
            ThreadPool.execute(() -> deleteAbandonRecord(ctx, req.getCustomer().getPhoneNumber()));

            responseObserver.onNext(SubmitBookingRequestResult.newBuilder()
                    .setId(bookingRequestId)
                    .setOrderId(orderId)
                    .setCustomerId(customerId)
                    .setAutoAcceptRequest(isAutoAccepted)
                    .build());
            responseObserver.onCompleted();
        } catch (Exception e) {

            // Delete customer if new
            if (ctx.isNewCustomer()) {
                customerApi.deleteCustomer((int) ctx.businessId(), (int) ctx.customerId());
            }

            // Refund deposit
            // 这里需要注意：
            // 在 prepay 在 submit 之前的流程里（老），如果 submit 失败，需要退款
            // 在 submit 在 prepay 之前的流程里（新），deposit 这时候还不存在
            // 这里 refund 需要兼容上面这两种情况
            refund(req.getPrepayGuid());

            throw e;
        }
    }

    @Nullable
    private DepositOrderParams buildDepositOrderParams(
            OBBusinessDTO biz, Long customerIdFromAuth, SubmitBookingRequestParams request) {
        if (!featureFlagApi.isOn(
                FeatureFlags.NEW_ORDER_FLOW,
                FeatureFlagContext.builder().company(biz.getCompanyId()).build())) {
            return null;
        }

        return depositService.getDepositOrderParams(biz, customerIdFromAuth, request);
    }

    private boolean triggerBookingRequestAutoAccepted(long bookingRequestId) {
        return bookingRequestStub
                .triggerBookingRequestAutoAccepted(TriggerBookingRequestAutoAcceptedRequest.newBuilder()
                        .setId(bookingRequestId)
                        .build())
                .getIsAutoAccepted();
    }

    private void updateBookingRequestStatus(long bookingRequestId, BookingRequestStatus status) {
        bookingRequestStub.updateBookingRequestStatus(UpdateBookingRequestStatusRequest.newBuilder()
                .setId(bookingRequestId)
                .setStatus(status)
                .build());
    }

    /**
     * See {@code com.moego.server.grooming.web.BookOnlineV2Controller#submit(com.moego.server.grooming.web.params.OBClientInfoParams, com.moego.server.grooming.params.ob.OBAnonymousParams)}
     */
    private void refund(@Nullable String prepayGuid) {

        if (!StringUtils.hasText(prepayGuid)) {
            return;
        }

        var deposit = bookOnlineDepositApi.getByGuid(prepayGuid);
        if (deposit == null) {
            return;
        }

        // Refund
        var refundParams = new CreateRefundByPaymentIdParams();
        refundParams.setPaymentId(deposit.getPaymentId());
        refundParams.setReason("Online Booking request failed to submit, deposit cancel.");
        paymentRefundApi.createRefundByPaymentId(deposit.getBusinessId(), refundParams);

        // Update deposit status
        var updateDepositParam = IBookOnlineDepositService.UpdateParam.builder()
                .id(deposit.getId())
                .status(BookOnlineDepositConst.CANCEL)
                .build();
        bookOnlineDepositApi.update(updateDepositParam);
    }

    private long createOrder(
            CreateBookingRequestContext ctx,
            SubmitBookingRequestParams request,
            long bookingRequestId,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServices,
            @Nullable DepositOrderParams depositOrderParams) {

        var bookingRequest = mustGetBookingRequest(bookingRequestId);

        // 只有 SUBMITTED 的 BookingRequest 才需要创建 order
        // TODO(Freeman): waitlist 也应该直接创建 order？
        if (bookingRequest.getStatus() != BookingRequestStatus.SUBMITTED) {
            return 0;
        }

        long orderId = doCreateOrder(bookingRequest, customizedServices, depositOrderParams, request.hasPreAuth());

        // 标记 order 是通过新流程创建的，这样才能在 payment 回调里判断是否需要进行 auto accept 和 send notification 等逻辑
        if (isOrderDecouplingFlow(request) && orderId > 0) {
            orderDecouplingFlowMarkerServiceApi.insertOrderDecouplingFlowMarker(orderId);
        }

        useDiscount(request, bookingRequest, orderId);

        // membership info for obc submit boarding&daycare
        if (request.hasMembership() && orderId > 0) {
            ThreadPool.execute(() -> membershipService.applyMemberships(
                    bookingRequest.getBusinessId(),
                    bookingRequest.getCompanyId(),
                    0L,
                    orderId,
                    request.getMembership().getMembershipIdsList()));
        }

        if (!Objects.equals(request.getPreAuth(), PreAuth.getDefaultInstance())) {
            handlePreAuth(ctx, request, bookingRequest, orderId);
        } else if (!Objects.equals(request.getPrePay(), PrePay.getDefaultInstance())) {
            handlePrePay(request, bookingRequest, orderId);
        }

        return orderId;
    }

    private boolean isOrderDecouplingFlow(SubmitBookingRequestParams request) {
        // 需要支付又没有 deposit，说明是新流程：先 submit 后 payment
        return needPayment(request) && bookOnlineDepositApi.getByGuid(request.getPrepayGuid()) == null;
    }

    private static boolean needPayment(SubmitBookingRequestParams request) {
        return StringUtils.hasText(request.getPrepayGuid());
    }

    private SubmitBookingRequestParams buildSubmitBookingRequestParamsWithOverride(
            SubmitBookingRequestParams request,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServices) {

        var builder = request.toBuilder().clearPetServices();

        for (var petServices : request.getPetServicesList()) {

            var psBuilder = petServices.toBuilder().clearServices();

            var pet = petServices.getPet();

            for (var service : petServices.getServicesList()) {
                var serviceId = getServiceId(service);
                if (!isNormal(serviceId)) {
                    psBuilder.addServices(service);
                    continue;
                }

                var customizedService = findCustomizedService(customizedServices, serviceId, pet.getPetId(), null);
                if (customizedService == null) {
                    psBuilder.addServices(service);
                } else {
                    psBuilder.addServices(
                            buildServiceWithOverride(service, customizedService, customizedServices, pet));
                }
            }

            builder.addPetServices(psBuilder.build());
        }

        return builder.build();
    }

    private static Service buildServiceWithOverride(
            Service service,
            CustomizedServiceView customizedService,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServices,
            Pet pet) {
        return switch (service.getServiceCase()) {
            case BOARDING -> {
                var serviceBuilder = service.getBoarding().toBuilder().clearAddons();
                serviceBuilder.setServicePrice(customizedService.getPrice());
                for (var addon : service.getBoarding().getAddonsList()) {
                    var customizedAddon =
                            findCustomizedService(customizedServices, addon.getId(), pet.getPetId(), null);
                    if (customizedAddon != null) {
                        serviceBuilder.addAddons(addon.toBuilder().setServicePrice(customizedAddon.getPrice()));
                    } else {
                        serviceBuilder.addAddons(addon);
                    }
                }
                yield service.toBuilder().setBoarding(serviceBuilder.build()).build();
            }
            case DAYCARE -> {
                var serviceBuilder = service.getDaycare().toBuilder().clearAddons();
                serviceBuilder.setServicePrice(customizedService.getPrice());
                for (var addon : service.getDaycare().getAddonsList()) {
                    var customizedAddon =
                            findCustomizedService(customizedServices, addon.getId(), pet.getPetId(), null);
                    if (customizedAddon != null) {
                        serviceBuilder.addAddons(addon.toBuilder().setServicePrice(customizedAddon.getPrice()));
                    } else {
                        serviceBuilder.addAddons(addon);
                    }
                }
                yield service.toBuilder().setDaycare(serviceBuilder.build()).build();
            }
            default -> service;
        };
    }

    @Nullable
    private static Long getServiceId(Service service) {
        // TODO(Freeman): 如果这个接口需要支持 grooming，需要修改这里
        return switch (service.getServiceCase()) {
            case BOARDING -> service.getBoarding().getServiceId();
            case DAYCARE -> service.getDaycare().getServiceId();
            default -> null;
        };
    }

    @Nullable
    private static Long getStaffId(Service service) {
        // 暂时只支持 boarding 和 daycare，都不支持选择 staff
        return null;
    }

    @Nullable
    private static CustomizedServiceView findCustomizedService(
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            long serviceId,
            @Nullable Long petId,
            @Nullable Long staffId) {
        return customizedServiceList.stream()
                .filter(e -> {
                    var cond = e.getQueryCondition();
                    return serviceId == cond.getServiceId()
                            && (!isNormal(petId) && !isNormal(cond.getPetId())
                                    || isNormal(petId) && petId == cond.getPetId())
                            && (!isNormal(staffId) && !isNormal(cond.getStaffId())
                                    || isNormal(staffId) && staffId == cond.getStaffId());
                })
                .findFirst()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .orElse(null);
    }

    private List<PetDetailCalculateResultDef> calculatePricingRuleResults(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList) {

        var petDetailCalculateDefs = new ArrayList<PetDetailCalculateDef>();
        for (var bookingRequestItem : bookingRequest.getServicesList()) {
            var defs =
                    switch (bookingRequestItem.getServiceCase()) {
                        case BOARDING -> {
                            BoardingServiceDetailModel boarding =
                                    bookingRequestItem.getBoarding().getService();
                            CustomizedServiceView customizedService = findCustomizedService(
                                    customizedServiceList, boarding.getServiceId(), boarding.getPetId(), null);
                            if (Objects.nonNull(customizedService)
                                    && Objects.equals(
                                            ServiceOverrideType.CLIENT, customizedService.getPriceOverrideType())) {
                                yield List.<PetDetailCalculateDef>of();
                            }
                            yield buildPetDetailCalculateDefForBoarding(
                                    bookingRequestItem.getBoarding(), serviceIdToService);
                        }
                        case DAYCARE -> {
                            DaycareServiceDetailModel daycare =
                                    bookingRequestItem.getDaycare().getService();
                            CustomizedServiceView customizedService = findCustomizedService(
                                    customizedServiceList, daycare.getServiceId(), daycare.getPetId(), null);
                            if (Objects.nonNull(customizedService)
                                    && Objects.equals(
                                            ServiceOverrideType.CLIENT, customizedService.getPriceOverrideType())) {
                                yield List.<PetDetailCalculateDef>of();
                            }
                            yield buildPetDetailCalculateDefForDaycare(bookingRequestItem.getDaycare());
                        }
                        default -> List.<PetDetailCalculateDef>of();
                    };
            petDetailCalculateDefs.addAll(defs);
        }

        if (petDetailCalculateDefs.isEmpty()) {
            return List.of();
        }

        return pricingRuleApplyStub
                .applyPricingRule(ApplyPricingRuleRequest.newBuilder()
                        .setCompanyId(bookingRequest.getCompanyId())
                        .setBusinessId(bookingRequest.getBusinessId())
                        .setSourceId(bookingRequest.getId())
                        .setSourceType(PricingRuleApplySourceType.SOURCE_TYPE_BOOKING_REQUEST)
                        .addAllPetDetails(petDetailCalculateDefs)
                        .build())
                .getPetDetailsList();
    }

    private static List<PetDetailCalculateDef> buildPetDetailCalculateDefForDaycare(
            BookingRequestModel.DaycareService daycare) {

        var service = daycare.getService();

        var result = new ArrayList<PetDetailCalculateDef>();

        for (var date : service.getSpecificDatesList()) {
            var builder = PetDetailCalculateDef.newBuilder();
            builder.setPetId(service.getPetId());
            builder.setServiceId(service.getServiceId());
            builder.setServicePrice(service.getServicePrice());
            builder.setServiceDate(date);
            result.add(builder.build());
        }

        return result;
    }

    private static List<PetDetailCalculateDef> buildPetDetailCalculateDefForBoarding(
            BookingRequestModel.BoardingService boarding, Map<Long, ServiceModel> serviceIdToService) {

        var petDetail = boarding.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        Stream<LocalDate> dates = isCalculateByDay(service)
                ? LocalDate.parse(petDetail.getStartDate())
                        .datesUntil(LocalDate.parse(petDetail.getEndDate()).plusDays(1))
                : LocalDate.parse(petDetail.getStartDate()).datesUntil(LocalDate.parse(petDetail.getEndDate()));

        return dates.map(date -> {
                    var builder = PetDetailCalculateDef.newBuilder();
                    builder.setPetId(petDetail.getPetId());
                    builder.setServiceId(petDetail.getServiceId());
                    builder.setServicePrice(petDetail.getServicePrice());
                    builder.setServiceDate(date.toString());
                    return builder.build();
                })
                .toList();
    }

    private long doCreateOrder(
            BookingRequestModel bookingRequest,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            DepositOrderParams depositOrderParams,
            boolean hasPreAuth) {

        var serviceIdToService = listService(bookingRequest);
        var evaluationIdToEvaluation = listEvaluation(bookingRequest);
        var taxIdToTax = listTax(serviceIdToService.values());
        var customer = mustGetCustomer(bookingRequest.getCustomerId());
        var pricingRuleResults = calculatePricingRuleResults(bookingRequest, serviceIdToService, customizedServiceList);

        // New invoice 只可能创建 deposit order
        if (featureFlagApi.isOn(
                FeatureFlags.NEW_ORDER_FLOW,
                FeatureFlagContext.builder()
                        .company(bookingRequest.getCompanyId())
                        .build())) {
            if (depositOrderParams.depositOrderPreview() != null) {
                // Preview deposit order 时还没创建 customer，这里要填入
                var orderDetail = depositOrderParams.depositOrderPreview().toBuilder()
                        .mergeOrder(OrderModelV1.newBuilder()
                                .setCustomerId(bookingRequest.getCustomerId())
                                .build())
                        .build();
                return depositService
                        .createDepositOrder(
                                bookingRequest.getId(),
                                orderDetail,
                                Optional.ofNullable(depositOrderParams.depositOrderPriceItems())
                                        .orElse(List.of()))
                        .getOrder()
                        .getId();
            }
            // DONE new order flow 没开 deposit 和 pre-auth 时不创建 order
            if (!hasPreAuth) {
                return 0L;
            }
        }

        return orderStub
                .createOrder(CreateOrderRequest.newBuilder()
                        .setOrder(
                                buildOrderModel(bookingRequest, serviceIdToService, evaluationIdToEvaluation, customer))
                        .addAllLineItems(buildLineItemsForServiceDetails(
                                bookingRequest,
                                serviceIdToService,
                                evaluationIdToEvaluation,
                                taxIdToTax,
                                pricingRuleResults))
                        .build())
                .getId();
    }

    /**
     * See {@code com.moego.server.grooming.service.OrderService#setDiscountWithDiscountCodeId(java.lang.Integer, java.lang.Integer, java.lang.Long)}
     */
    private void useDiscount(SubmitBookingRequestParams request, BookingRequestModel bookingRequest, long orderId) {
        var discountCode = request.getDiscountCode();
        if (discountCode.getDiscountCode().isBlank() || orderId == 0) {
            return;
        }

        var discountBuilder = OrderLineDiscountModel.newBuilder()
                .setBusinessId(bookingRequest.getBusinessId())
                .setOrderId(orderId)
                .setDiscountCodeId(discountCode.getDiscountCodeId())
                .setApplyType(LineApplyType.TYPE_ALL.getType())
                .setDiscountType(DiscountType.AMOUNT.getType());

        orderStub.updateOrderIncremental(UpdateOrderIncrRequest.newBuilder()
                .setOrderId(orderId)
                .addLineDiscounts(discountBuilder.build())
                .build());
    }

    /**
     * See {@code com.moego.server.grooming.service.ob.PrepayService#updateDepositAndPaymentRecord(com.moego.server.grooming.mapperbean.MoeBookOnlineDeposit, com.moego.server.grooming.params.BookOnlineSubmitParams, java.lang.Integer)}
     */
    private void handlePrePay(SubmitBookingRequestParams request, BookingRequestModel bookingRequest, long orderId) {
        var guid = request.getPrepayGuid();
        if (!StringUtils.hasText(guid)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "guid is required for prepay");
        }

        var deposit = bookOnlineDepositApi.getByGuid(guid);
        if (deposit == null) {
            // 这里存在两套逻辑：
            // 1. prepay 在 submit 之前（老）：这时候 deposit 肯定存在
            // 2. prepay 在 submit 之后（新）：这时候 deposit 不存在
            // 这里要兼容两种逻辑，所以这里不抛异常
            return;
        }

        var prePay = request.getPrePay();

        var builder = IBookOnlineDepositService.UpdateParam.builder()
                .id(deposit.getId())
                .bookingRequestId(bookingRequest.getId())
                .status(BookOnlineDepositConst.REQUIRE_CAPTURE)
                .serviceTotal(BigDecimal.valueOf(prePay.getServiceTotal()))
                .taxAmount(BigDecimal.valueOf(prePay.getTaxAmount()))
                .serviceChargeAmount(BigDecimal.valueOf(prePay.getServiceChargeAmount()));

        if (!Objects.equals(request.getDiscountCode(), DiscountCode.getDefaultInstance())) {
            builder.discountCodeId(request.getDiscountCode().getDiscountCodeId())
                    .discountAmount(BigDecimal.valueOf(request.getDiscountCode().getDiscountAmount()));
        }

        bookOnlineDepositApi.update(builder.build());

        // 把invoiceId更新到payment记录中
        // 之前的逻辑是异步执行，但其实没必要
        var paymentDTO = new PaymentDTO();
        paymentDTO.setInvoiceId(toIntExact(orderId));
        paymentDTO.setCustomerId(toIntExact(bookingRequest.getCustomerId()));
        paymentDTO.setId(deposit.getPaymentId());
        paymentDTO.setStatus(PaymentStatusEnum.PROCESSING);
        paymentApi.updatePaymentRecord(paymentDTO);

        // 绑定stripe customer
        var stripeCustomerId = request.getCustomer().getStripeCustomerId();
        if (StringUtils.hasText(stripeCustomerId)) {
            paymentStripeApi.saveStripeCustomer(
                    toIntExact(bookingRequest.getBusinessId()),
                    toIntExact(bookingRequest.getCustomerId()),
                    stripeCustomerId);
        }
    }

    /**
     * See {@code com.moego.server.grooming.service.ob.PrepayService#insertPreAuthRecord(com.moego.server.grooming.params.BookOnlineSubmitParams, java.lang.Integer)}
     */
    private void handlePreAuth(
            CreateBookingRequestContext ctx,
            SubmitBookingRequestParams request,
            BookingRequestModel bookingRequest,
            long orderId) {

        var preAuth = request.getPreAuth();

        // Yunxiang: 在 PreAuth 的场景，因为整体的支付流程会非常长，并且存在最后 Capture 的时候调整实际收取
        // 的金额的场景。复用其他即时支付的增加 Tips 的逻辑（即支付成功后再增加到 Order 上）会有 Tips 丢失的风险。
        // 此外，在 PreAuth Capture 之前，Business 都无法在订单上看到 Client 添加的 Tips 导致误解。
        // 因此，当 PreAuth 有 Tips 时，应当在创建订单的时候就添加到订单上。

        // 在新流程下，Tips 会在创建 Deposit Order 时就设置到 Order 上
        // 这里不需要再设置 Tips
        if (!featureFlagApi.isOn(
                FeatureFlags.NEW_ORDER_FLOW,
                FeatureFlagContext.builder().company(ctx.companyId).build())) {
            if (BigDecimal.valueOf(request.getPreAuth().getTipsAmount()).compareTo(BigDecimal.ZERO) > 0) {
                orderStub.setTips(SetTipsRequest.newBuilder()
                        .setBusinessId(bookingRequest.getBusinessId())
                        .setInvoiceId(orderId)
                        .setValueType(InvoiceValueType.AMOUNT.value())
                        .setValue(request.getPreAuth().getTipsAmount())
                        .build());
            }
        }

        // 没有 paymentMethodId 说明是新卡，需要保存
        // see com.moego.server.grooming.service.ob.OBGroomingService.assembleAppointmentInfo
        if (preAuth.getPaymentMethodId().isBlank()) {
            var response = obApi.saveCard(IOBService.SaveCardParam.builder()
                    .chargeToken(preAuth.getChargeToken())
                    .customerId(toIntExact(bookingRequest.getCustomerId()))
                    .businessId(toIntExact(ctx.businessId()))
                    .isNewCustomer(ctx.isNewCustomer())
                    .build());
            if (response != null) {
                preAuth = preAuth.toBuilder()
                        .setPaymentMethodId(response.paymentMethodId())
                        .setCardNumber(response.cardType() + response.cardNumber())
                        .build();
            }
        }

        insertDeposit(preAuth, request.getDiscountCode(), bookingRequest);
    }

    private void insertDeposit(PreAuth preAuth, DiscountCode discountCode, BookingRequestModel bookingRequest) {

        var builder = IBookOnlineDepositService.InsertParam.builder()
                .companyId(bookingRequest.getCompanyId())
                .businessId(toIntExact(bookingRequest.getBusinessId()))
                .status(BookOnlineDepositConst.PROCESSING)
                .bookingRequestId(bookingRequest.getId())
                .serviceTotal(BigDecimal.valueOf(preAuth.getServiceTotal()))
                .taxAmount(BigDecimal.valueOf(preAuth.getTaxAmount()))
                .tipsAmount(BigDecimal.valueOf(preAuth.getTipsAmount()))
                .serviceChargeAmount(BigDecimal.valueOf(preAuth.getServiceChargeAmount()))
                .guid(PREAUTH_PREFIX + CommonUtil.getUuid())
                .depositType(DepositPaymentTypeEnum.PreAuth);

        var total = BigDecimal.valueOf(preAuth.getServiceTotal())
                .add(BigDecimal.valueOf(preAuth.getTaxAmount()))
                .add(BigDecimal.valueOf(preAuth.getTipsAmount()))
                .add(BigDecimal.valueOf(preAuth.getServiceChargeAmount()));

        builder.amount(total);

        if (!Objects.equals(discountCode, DiscountCode.getDefaultInstance())) {
            builder.discountCodeId(discountCode.getDiscountCodeId());
            builder.discountAmount(BigDecimal.valueOf(discountCode.getDiscountAmount()));
        }

        builder.preauthInfo(buildPreauthInfo(preAuth));

        bookOnlineDepositApi.insert(builder.build());
    }

    private static BookOnlineDepositDTO.PreAuth buildPreauthInfo(PreAuth preAuth) {
        var preauthInfo = new BookOnlineDepositDTO.PreAuth();
        preauthInfo.setTipsAmount(preAuth.getTipsAmount());
        preauthInfo.setServiceTotal(preAuth.getServiceTotal());
        preauthInfo.setTaxAmount(preAuth.getTaxAmount());
        preauthInfo.setServiceChargeAmount(preAuth.getServiceChargeAmount());
        preauthInfo.setPaymentMethodId(preAuth.getPaymentMethodId());
        preauthInfo.setCardNumber(preAuth.getCardNumber());
        preauthInfo.setChargeToken(preAuth.getChargeToken());
        return preauthInfo;
    }

    private static OrderModel buildOrderModel(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, EvaluationModel> evaluationIdToEvaluation,
            MoeBusinessCustomerDTO customer) {

        var desc = String.format("%s %s", customer.getFirstName(), customer.getLastName());
        var title = buildTitle(bookingRequest, serviceIdToService, evaluationIdToEvaluation);

        return OrderModel.newBuilder()
                .setCompanyId(bookingRequest.getCompanyId())
                .setBusinessId(bookingRequest.getBusinessId())
                .setCustomerId(bookingRequest.getCustomerId())
                .setSourceType(OrderSourceType.BOOKING_REQUEST.name().toLowerCase())
                .setStatus(OrderStatus.CREATED.getNumber())
                .setSource(AppointmentSource.APPOINTMENT_SOURCE_OB)
                .setSourceId(bookingRequest.getId())
                .setDescription(desc)
                .setTitle(title)
                .setCreateBy(0)
                .setUpdateBy(0)
                .build();
    }

    private static String buildTitle(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, EvaluationModel> evaluationIdToEvaluation) {
        return bookingRequest.getServicesList().stream()
                .map(service -> switch (service.getServiceCase()) {
                    case GROOMING -> {
                        var serviceId = service.getGrooming().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceModel::getName)
                                .orElse(null);
                    }
                    case BOARDING -> {
                        var serviceId = service.getBoarding().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceModel::getName)
                                .orElse(null);
                    }
                    case DAYCARE -> {
                        var serviceId = service.getDaycare().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceModel::getName)
                                .orElse(null);
                    }
                    case EVALUATION -> {
                        var evaluationId = service.getEvaluation().getService().getEvaluationId();
                        yield Optional.ofNullable(evaluationIdToEvaluation.get(evaluationId))
                                .map(EvaluationModel::getName)
                                .orElse(null);
                    }
                    case DOG_WALKING -> {
                        var serviceId = service.getDogWalking().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceModel::getName)
                                .orElse(null);
                    }
                    case GROUP_CLASS -> {
                        var serviceId = service.getGroupClass().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceModel::getName)
                                .orElse(null);
                    }
                    default -> null;
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.joining(", "));
    }

    private Map<Long, EvaluationModel> listEvaluation(BookingRequestModel bookingRequest) {
        var evaluationIds = bookingRequest.getServicesList().stream()
                .map(service -> switch (service.getServiceCase()) {
                    case EVALUATION -> service.getEvaluation().getService().getEvaluationId();
                    default -> null;
                })
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        return evaluationIds.stream()
                .map(evaluationId -> evaluationStub.getEvaluation(GetEvaluationRequest.newBuilder()
                        .setId(evaluationId)
                        .build())) // 一个 booking request 只会有一个 evaluation，没有提供批量查询接口
                .filter(GetEvaluationResponse::hasEvaluationModel)
                .map(GetEvaluationResponse::getEvaluationModel)
                .collect(toMap(EvaluationModel::getId, identity(), (o, n) -> o));
    }

    private Map<Long, ServiceModel> listService(BookingRequestModel bookingRequest) {

        var serviceIds = bookingRequest.getServicesList().stream()
                .map(service -> switch (service.getServiceCase()) {
                    case GROOMING -> service.getGrooming().getService().getServiceId();
                    case BOARDING -> service.getBoarding().getService().getServiceId();
                    case DAYCARE -> service.getDaycare().getService().getServiceId();
                    case DOG_WALKING -> service.getDogWalking().getService().getServiceId();
                    case GROUP_CLASS -> service.getGroupClass().getService().getServiceId();
                    default -> null;
                })
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        var addonIds = bookingRequest.getServicesList().stream()
                .map(service -> switch (service.getServiceCase()) {
                    case BOARDING -> service.getBoarding().getAddonsList().stream()
                            .map(BoardingAddOnDetailModel::getAddOnId)
                            .toList();
                    case DAYCARE -> service.getDaycare().getAddonsList().stream()
                            .map(DaycareAddOnDetailModel::getAddOnId)
                            .toList();
                    default -> List.<Long>of();
                })
                .flatMap(List::stream)
                .distinct()
                .toList();

        var allIds = new ArrayList<>(serviceIds);
        allIds.addAll(addonIds);

        return !allIds.isEmpty()
                ? serviceStub
                        .getServiceList(GetServiceListRequest.newBuilder()
                                .setTokenCompanyId(bookingRequest.getCompanyId())
                                .addBusinessIds(bookingRequest.getBusinessId())
                                .addAllServiceIds(allIds)
                                .build())
                        .getCategoryListList()
                        .stream()
                        .map(ServiceCategoryModel::getServicesList)
                        .flatMap(Collection::stream)
                        .collect(toMap(ServiceModel::getServiceId, identity(), (o, n) -> o))
                : Map.of();
    }

    private List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> listCustomizedService(
            CreateBookingRequestContext ctx, List<PetServices> items, final Customer customer) {

        var builder = BatchGetCustomizedServiceRequest.newBuilder();

        builder.setCompanyId(ctx.companyId());

        String zipcode = Optional.ofNullable(customer)
                .map(Customer::getAddress)
                .map(Address::getZipcode)
                .orElse(null);
        String lat = Optional.ofNullable(customer)
                .map(Customer::getAddress)
                .map(Address::getLat)
                .orElse(null);
        String lng = Optional.ofNullable(customer)
                .map(Customer::getAddress)
                .map(Address::getLng)
                .orElse(null);

        for (var petAndServiceList : buildPetToServicesMap(items).entrySet()) {
            var pet = petAndServiceList.getKey();
            for (var service : petAndServiceList.getValue()) {
                var serviceId = getServiceId(service);
                if (!isNormal(serviceId)) {
                    continue;
                }

                var condBuilder = CustomizedServiceQueryCondition.newBuilder()
                        .setServiceId(serviceId)
                        .setBusinessId(ctx.businessId());
                if (isNormal(pet.getPetId())) {
                    condBuilder.setPetId(pet.getPetId());
                }
                var staffId = getStaffId(service);
                if (isNormal(staffId)) {
                    condBuilder.setStaffId(staffId);
                }
                if (StringUtils.hasText(zipcode) && StringUtils.hasText(lat) && StringUtils.hasText(lng)) {
                    condBuilder
                            .setZipcode(zipcode)
                            .setCoordinate(com.google.type.LatLng.newBuilder()
                                    .setLatitude(Double.parseDouble(lat))
                                    .setLongitude(Double.parseDouble(lng))
                                    .build());
                }
                builder.addQueryConditionList(condBuilder.build());
            }
        }

        for (var petAndAddonList : buildPetToAddonsMapForBoarding(items).entrySet()) {
            var pet = petAndAddonList.getKey();
            for (var addon : petAndAddonList.getValue()) {
                var condBuilder = CustomizedServiceQueryCondition.newBuilder()
                        .setServiceId(addon.getId())
                        .setBusinessId(ctx.businessId());
                if (isNormal(pet.getPetId())) {
                    condBuilder.setPetId(pet.getPetId());
                }
                builder.addQueryConditionList(condBuilder.build());
            }
        }

        for (var petAndAddonList : buildPetToAddonsMapForDaycare(items).entrySet()) {
            var pet = petAndAddonList.getKey();
            for (var addon : petAndAddonList.getValue()) {
                var condBuilder = CustomizedServiceQueryCondition.newBuilder()
                        .setServiceId(addon.getId())
                        .setBusinessId(ctx.businessId());
                if (isNormal(pet.getPetId())) {
                    condBuilder.setPetId(pet.getPetId());
                }
                builder.addQueryConditionList(condBuilder.build());
            }
        }

        if (builder.getQueryConditionListList().isEmpty()) {
            return List.of();
        }

        return serviceManagementServiceClient
                .batchGetCustomizedService(builder.build())
                .getCustomizedServiceListList();
    }

    private static IdentityHashMap<Pet, List<Service>> buildPetToServicesMap(List<PetServices> items) {

        var petToServices = new IdentityHashMap<Pet, List<Service>>();

        for (var petAndServices : items) {
            petToServices.put(petAndServices.getPet(), petAndServices.getServicesList());
        }

        return petToServices;
    }

    private static IdentityHashMap<Pet, List<BoardingAddon>> buildPetToAddonsMapForBoarding(List<PetServices> items) {

        var petToAddons = new IdentityHashMap<Pet, List<BoardingAddon>>();

        for (var petAndServices : items) {
            for (var service : petAndServices.getServicesList()) {
                if (service.getServiceCase() == BOARDING) {
                    petToAddons
                            .computeIfAbsent(petAndServices.getPet(), k -> new ArrayList<>())
                            .addAll(service.getBoarding().getAddonsList());
                }
            }
        }

        return petToAddons;
    }

    private static IdentityHashMap<Pet, List<DaycareAddon>> buildPetToAddonsMapForDaycare(List<PetServices> items) {

        var petToAddons = new IdentityHashMap<Pet, List<DaycareAddon>>();

        for (var petAndServices : items) {
            for (var service : petAndServices.getServicesList()) {
                if (service.getServiceCase() == DAYCARE) {
                    petToAddons
                            .computeIfAbsent(petAndServices.getPet(), k -> new ArrayList<>())
                            .addAll(service.getDaycare().getAddonsList());
                }
            }
        }

        return petToAddons;
    }

    private static List<OrderLineItemModel> buildLineItemsForServiceDetails(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, EvaluationModel> evaluationIdToEvaluation,
            Map<Integer, MoeBusinessTaxDto> taxIdToTax,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var petDetails = bookingRequest.getServicesList();
        if (petDetails.isEmpty()) {
            return List.of();
        }

        var mainServiceItemType = ServiceItemEnum.getMainServiceItemType(bookingRequest.getServiceTypeInclude());

        var result =
                switch (mainServiceItemType) {
                    case BOARDING -> buildLineItemsForBoarding(
                            bookingRequest, serviceIdToService, taxIdToTax, pricingRuleResults);
                    case DAYCARE -> buildLineItemsForDaycare(
                            bookingRequest, serviceIdToService, taxIdToTax, pricingRuleResults);
                    case GROOMING -> buildLineItemsForGrooming(bookingRequest, serviceIdToService, taxIdToTax);
                    case EVALUATION -> buildLineItemsForEvaluation(bookingRequest, evaluationIdToEvaluation);
                    case DOG_WALKING -> buildLineItemsForDogWalking(bookingRequest, serviceIdToService, taxIdToTax);
                    case GROUP_CLASS -> buildLineItemsForGroupClass(bookingRequest, serviceIdToService, taxIdToTax);
                    default -> throw bizException(
                            Code.CODE_PARAMS_ERROR, "Unsupported service type: " + mainServiceItemType);
                };

        return mergeByServiceIdAndPrice(result);
    }

    private static List<OrderLineItemModel> buildLineItemsForGroupClass(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Integer, MoeBusinessTaxDto> taxIdToTax) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var groupClassService : getGroupClassServices(bookingRequest)) {
            result.addAll(
                    buildLineItemForGroupClass(bookingRequest, serviceIdToService, taxIdToTax, groupClassService));
        }

        return result;
    }

    private static List<OrderLineItemModel> buildLineItemsForDogWalking(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Integer, MoeBusinessTaxDto> taxIdToTax) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var dogWalkingService : getDogWalkingServices(bookingRequest)) {
            result.addAll(
                    buildLineItemForDogWalking(bookingRequest, serviceIdToService, taxIdToTax, dogWalkingService));
        }

        return result;
    }

    private static List<OrderLineItemModel> buildLineItemsForEvaluation(
            BookingRequestModel bookingRequest, Map<Long, EvaluationModel> evaluationIdToEvaluation) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var evaluationService : getEvaluationServices(bookingRequest)) {
            result.addAll(buildLineItemForEvaluation(bookingRequest, evaluationIdToEvaluation, evaluationService));
        }

        return result;
    }

    private static List<OrderLineItemModel> buildLineItemsForGrooming(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Integer, MoeBusinessTaxDto> taxIdToTax) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var groomingService : getGroomingServices(bookingRequest)) {
            result.addAll(buildLineItemForGrooming(bookingRequest, serviceIdToService, taxIdToTax, groomingService));
        }

        return result;
    }

    private static List<OrderLineItemModel> buildLineItemsForDaycare(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Integer, MoeBusinessTaxDto> taxIdToTax,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var result = new ArrayList<OrderLineItemModel>();

        // 特别注意：daycare 只收第一天的钱

        String firstDate = getFirstDateForDaycare(bookingRequest);

        var daycareServices = getDaycareServices(bookingRequest).stream()
                .filter(e -> e.getService().getSpecificDatesList().contains(firstDate))
                .toList();

        for (var daycareService : daycareServices) {
            result.addAll(buildLineItemForDaycare(
                    bookingRequest, serviceIdToService, taxIdToTax, daycareService, pricingRuleResults));
        }

        var groomingServicesInFirstDate = getGroomingServices(bookingRequest).stream()
                .filter(e -> Objects.equals(e.getService().getStartDate(), firstDate))
                .toList();

        for (var groomingService : groomingServicesInFirstDate) {
            result.addAll(buildLineItemForGrooming(bookingRequest, serviceIdToService, taxIdToTax, groomingService));
        }

        return result;
    }

    private static String getFirstDateForDaycare(BookingRequestModel bookingRequest) {
        return getDaycareServices(bookingRequest).stream()
                .flatMap(e -> e.getService().getSpecificDatesList().stream())
                .sorted()
                .findFirst()
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "There is no specific date for daycare service"));
    }

    private static List<OrderLineItemModel> buildLineItemsForBoarding(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Integer, MoeBusinessTaxDto> taxIdToTax,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var boardingService : getBoardingServices(bookingRequest)) {
            result.addAll(buildLineItemForBoarding(
                    bookingRequest, serviceIdToService, taxIdToTax, boardingService, pricingRuleResults));
        }

        for (var groomingService : getGroomingServices(bookingRequest)) {
            result.addAll(buildLineItemForGrooming(bookingRequest, serviceIdToService, taxIdToTax, groomingService));
        }

        return result;
    }

    private static List<BookingRequestModel.BoardingService> getBoardingServices(BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasBoarding)
                .map(BookingRequestModel.Service::getBoarding)
                .toList();
    }

    private static List<BookingRequestModel.DaycareService> getDaycareServices(BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasDaycare)
                .map(BookingRequestModel.Service::getDaycare)
                .toList();
    }

    private static List<BookingRequestModel.GroomingService> getGroomingServices(BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasGrooming)
                .map(BookingRequestModel.Service::getGrooming)
                .toList();
    }

    private static List<BookingRequestModel.EvaluationService> getEvaluationServices(
            BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .map(BookingRequestModel.Service::getEvaluation)
                .toList();
    }

    private static List<BookingRequestModel.DogWalkingService> getDogWalkingServices(
            BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasDogWalking)
                .map(BookingRequestModel.Service::getDogWalking)
                .toList();
    }

    private static List<BookingRequestModel.GroupClassService> getGroupClassServices(
            BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasGroupClass)
                .map(BookingRequestModel.Service::getGroupClass)
                .toList();
    }

    private static List<OrderLineItemModel> mergeByServiceIdAndPrice(List<OrderLineItemModel> lineItems) {
        var uniqKeyToLineItem = lineItems.stream()
                .collect(Collectors.groupingBy(
                        e -> e.getObjectId() + ":" + e.getUnitPrice(), // 按照 serviceId 和 price 分组
                        Collectors.collectingAndThen(Collectors.toList(), list -> {
                            var builder = list.get(0).toBuilder();
                            builder.setQuantity(list.stream()
                                    .mapToInt(OrderLineItemModel::getQuantity)
                                    .sum());
                            return builder.build();
                        })));
        return List.copyOf(uniqKeyToLineItem.values());
    }

    private static List<OrderLineItemModel> buildLineItemForEvaluation(
            BookingRequestModel bookingRequest,
            Map<Long, EvaluationModel> evaluationIdToEvaluation,
            BookingRequestModel.EvaluationService evaluation) {

        var petDetail = evaluation.getService();
        var evaluationModel = Optional.ofNullable(evaluationIdToEvaluation.get(petDetail.getEvaluationId()))
                .orElseThrow(() ->
                        bizException(Code.CODE_PARAMS_ERROR, "Evaluation not found: " + petDetail.getEvaluationId()));

        var builder = OrderLineItemModel.newBuilder();
        builder.setBusinessId(bookingRequest.getBusinessId());
        builder.setObjectId(petDetail.getEvaluationId());
        builder.setType(OrderItemType.ITEM_TYPE_EVALUATION_SERVICE.getType());
        builder.setName(evaluationModel.getName());
        builder.setUnitPrice(petDetail.getServicePrice());
        builder.setQuantity(1);

        return List.of(builder.build());
    }

    private static List<OrderLineItemModel> buildLineItemForDaycare(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Integer, MoeBusinessTaxDto> taxIdToTax,
            BookingRequestModel.DaycareService daycare,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var petDetail = daycare.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        // NOTE: 这里有个特殊逻辑：daycare 为多天时，只计算第一天的价格
        // See https://moego.atlassian.net/browse/MER-1091
        var date =
                petDetail.getSpecificDatesList().stream().sorted().findFirst().orElse(null);

        if (date == null) {
            return List.of();
        }

        var result = new ArrayList<OrderLineItemModel>();

        // service
        var serviceBuilder = OrderLineItemModel.newBuilder();
        serviceBuilder.setBusinessId(bookingRequest.getBusinessId());
        serviceBuilder.setObjectId(petDetail.getServiceId());
        serviceBuilder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        serviceBuilder.setName(service.getName());
        serviceBuilder.setDescription(service.getDescription());
        serviceBuilder.setUnitPrice(getPrice(pricingRuleResults, petDetail, date));
        serviceBuilder.setQuantity(1);

        var serviceTaxId = service.getTaxId();
        if (serviceTaxId > 0) {
            var tax = Optional.ofNullable(taxIdToTax.get(toIntExact(serviceTaxId)))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + serviceTaxId));
            serviceBuilder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
        }

        result.add(serviceBuilder.build());

        // addons
        for (var addon : daycare.getAddonsList()) {
            if (!addon.getIsEveryday() && !addon.getSpecificDatesList().contains(date)) { // 只算第一天的 addon
                continue;
            }

            var addonModel = Optional.ofNullable(serviceIdToService.get(addon.getAddOnId()))
                    .orElseGet(ServiceModel::getDefaultInstance);

            var builder = OrderLineItemModel.newBuilder();
            builder.setBusinessId(bookingRequest.getBusinessId());
            builder.setObjectId(addon.getAddOnId());
            builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
            builder.setName(addonModel.getName());
            builder.setDescription(addonModel.getDescription());
            builder.setUnitPrice(addon.getServicePrice());
            builder.setQuantity(addon.getQuantityPerDay());

            var taxId = addonModel.getTaxId();
            if (taxId > 0) {
                var tax = Optional.ofNullable(taxIdToTax.get(toIntExact(taxId)))
                        .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
                builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
            }

            result.add(builder.build());
        }

        return result;
    }

    private static Double getPrice(
            List<PetDetailCalculateResultDef> pricingRuleResults, DaycareServiceDetailModel petDetail, String date) {
        return pricingRuleResults.stream()
                .filter(e -> e.getPetId() == petDetail.getPetId()
                        && e.getServiceId() == petDetail.getServiceId()
                        && Objects.equals(e.getServiceDate(), date))
                .findFirst()
                .map(PetDetailCalculateResultDef::getAdjustedPrice)
                .orElseGet(petDetail::getServicePrice);
    }

    private static List<OrderLineItemModel> buildLineItemForBoarding(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Integer, MoeBusinessTaxDto> taxIdToTax,
            BookingRequestModel.BoardingService boarding,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var petDetail = boarding.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        var result = new ArrayList<OrderLineItemModel>();

        var start = LocalDate.parse(petDetail.getStartDate());
        var end = isCalculateByDay(service)
                ? LocalDate.parse(petDetail.getEndDate()).plusDays(1)
                : LocalDate.parse(petDetail.getEndDate());

        var dates = start.datesUntil(end).toList();

        for (var date : dates) {
            var price = pricingRuleResults.stream()
                    .filter(e -> e.getPetId() == petDetail.getPetId()
                            && e.getServiceId() == petDetail.getServiceId()
                            && Objects.equals(e.getServiceDate(), date.toString()))
                    .findFirst()
                    .map(PetDetailCalculateResultDef::getAdjustedPrice)
                    .orElseGet(petDetail::getServicePrice);

            var builder = OrderLineItemModel.newBuilder();
            builder.setBusinessId(bookingRequest.getBusinessId());
            builder.setObjectId(petDetail.getServiceId());
            builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
            builder.setName(service.getName());
            builder.setDescription(service.getDescription());
            builder.setUnitPrice(price);
            builder.setQuantity(1);

            var taxId = service.getTaxId();
            if (taxId > 0) {
                var tax = Optional.ofNullable(taxIdToTax.get(toIntExact(taxId)))
                        .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
                builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
            }

            result.add(builder.build());
        }

        for (var addon : boarding.getAddonsList()) {

            long days =
                    switch (addon.getDateType()) {
                        case PET_DETAIL_DATE_EVERYDAY, PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY -> LocalDate.parse(
                                        petDetail.getStartDate())
                                .datesUntil(LocalDate.parse(petDetail.getEndDate()))
                                .count();
                        case PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> LocalDate.parse(petDetail.getStartDate())
                                        .datesUntil(LocalDate.parse(petDetail.getEndDate()))
                                        .count()
                                + 1;
                        case PET_DETAIL_DATE_LAST_DAY -> 1;
                        case PET_DETAIL_DATE_FIRST_DAY -> 1;
                        case PET_DETAIL_DATE_SPECIFIC_DATE -> addon.getSpecificDatesCount();
                        case PET_DETAIL_DATE_DATE_POINT -> 1;
                        default -> throw bizException(
                                Code.CODE_PARAMS_ERROR, "Invalid date type: " + addon.getDateType());
                    };

            var addonModel = Optional.ofNullable(serviceIdToService.get(addon.getAddOnId()))
                    .orElseGet(ServiceModel::getDefaultInstance);

            for (int i = 0; i < days; i++) {
                var builder = OrderLineItemModel.newBuilder();
                builder.setBusinessId(bookingRequest.getBusinessId());
                builder.setObjectId(addon.getAddOnId());
                builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
                builder.setName(addonModel.getName());
                builder.setDescription(addonModel.getDescription());
                builder.setUnitPrice(addon.getServicePrice());
                builder.setQuantity(addon.getQuantityPerDay());

                var taxId = addonModel.getTaxId();
                if (taxId > 0) {
                    var tax = Optional.ofNullable(taxIdToTax.get(toIntExact(taxId)))
                            .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
                    builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
                }

                result.add(builder.build());
            }
        }

        return result;
    }

    private static boolean isCalculateByDay(ServiceModel service) {
        return service.getServiceItemType() == ServiceItemType.DAYCARE
                || (service.getServiceItemType() == ServiceItemType.BOARDING
                        && service.getPriceUnit() == ServicePriceUnit.PER_DAY);
    }

    private static List<OrderLineItemModel> buildLineItemForGrooming(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Integer, MoeBusinessTaxDto> taxIdToTax,
            BookingRequestModel.GroomingService grooming) {

        var petDetail = grooming.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        var builder = OrderLineItemModel.newBuilder();
        builder.setBusinessId(bookingRequest.getBusinessId());
        builder.setObjectId(petDetail.getServiceId());
        builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        builder.setName(service.getName());
        builder.setDescription(service.getDescription());
        builder.setUnitPrice(petDetail.getServicePrice());
        builder.setQuantity(1);

        var taxId = service.getTaxId();
        if (taxId > 0) {
            var tax = Optional.ofNullable(taxIdToTax.get(toIntExact(taxId)))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
            builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
        }

        return List.of(builder.build());
    }

    private static List<OrderLineItemModel> buildLineItemForDogWalking(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Integer, MoeBusinessTaxDto> taxIdToTax,
            BookingRequestModel.DogWalkingService dogWalking) {

        var petDetail = dogWalking.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        var builder = OrderLineItemModel.newBuilder();
        builder.setBusinessId(bookingRequest.getBusinessId());
        builder.setObjectId(petDetail.getServiceId());
        builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        builder.setName(service.getName());
        builder.setDescription(service.getDescription());
        builder.setUnitPrice(petDetail.getServicePrice());
        builder.setQuantity(1);

        var taxId = service.getTaxId();
        if (taxId > 0) {
            var tax = Optional.ofNullable(taxIdToTax.get(toIntExact(taxId)))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
            builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
        }

        return List.of(builder.build());
    }

    private static List<OrderLineItemModel> buildLineItemForGroupClass(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Integer, MoeBusinessTaxDto> taxIdToTax,
            BookingRequestModel.GroupClassService groupClass) {

        var petDetail = groupClass.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        var builder = OrderLineItemModel.newBuilder();
        builder.setBusinessId(bookingRequest.getBusinessId());
        builder.setObjectId(petDetail.getServiceId());
        builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        builder.setName(service.getName());
        builder.setDescription(service.getDescription());
        builder.setUnitPrice(petDetail.getServicePrice());
        builder.setQuantity(1);

        var taxId = service.getTaxId();
        if (taxId > 0) {
            var tax = Optional.ofNullable(taxIdToTax.get(toIntExact(taxId)))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
            builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
        }

        return List.of(builder.build());
    }

    private static OrderLineTaxModel buildOrderLineTax(BookingRequestModel bookingRequest, MoeBusinessTaxDto tax) {
        return OrderLineTaxModel.newBuilder()
                .setBusinessId(bookingRequest.getBusinessId())
                .setApplyType(LineApplyType.TYPE_ITEM.getType())
                .setTaxId(tax.getId())
                .setTaxRate(tax.getTaxRate())
                .build();
    }

    private Map<Integer, MoeBusinessTaxDto> listTax(Collection<ServiceModel> services) {

        var taxIds = services.stream()
                .map(ServiceModel::getTaxId)
                .filter(CommonUtil::isNormal)
                .map(Long::intValue)
                .distinct()
                .toList();

        if (taxIds.isEmpty()) {
            return Map.of();
        }

        return taxApi.getTaxListByIds(taxIds).stream()
                .collect(toMap(MoeBusinessTaxDto::getId, identity(), (o, n) -> o));
    }

    private static List<Pet> getPets(SubmitBookingRequestParams request) {
        return request.getPetServicesList().stream().map(PetServices::getPet).toList();
    }

    private OBBusinessDTO mustGetBusiness(OBAnonymousParams request) {
        return onlineBookingApi.mustGetBusinessDTOByOBNameOrDomain(request);
    }

    private void checkOBSubmitParam(SubmitBookingRequestParams request, OBBusinessDTO biz) {

        var bookOnline = mustGetBookOnline(biz.getBusinessId());
        var businessDate =
                companyHelper.mustGetCompanyDateTime(biz.getCompanyId()).toLocalDate();

        checkOBEnabled(bookOnline);

        checkAddress(bookOnline, request);

        checkAgreement(request, biz);

        checkCustomer(bookOnline, request, biz);

        checkBoarding(request, biz, businessDate);

        checkDaycare(request, biz, businessDate);
    }

    private void checkCustomer(BookOnlineDTO bookOnline, SubmitBookingRequestParams request, OBBusinessDTO biz) {

        checkCustomerType(request, biz);

        checkBlocked(bookOnline, request);
    }

    private void checkCustomerType(SubmitBookingRequestParams request, OBBusinessDTO biz) {
        boolean isExisting = isExistingCustomer();

        var acceptClientTypes = obAvailabilitySettingStub
                .listAcceptedCustomerSetting(ListAcceptedCustomerSettingRequest.newBuilder()
                        .setCompanyId(biz.getCompanyId())
                        .setBusinessId(biz.getBusinessId())
                        .addAllServiceItemTypes(getPetServiceTypes(request))
                        .build())
                .getAcceptCustomerTypesList();

        boolean isOnlyAcceptNew = acceptClientTypes.stream()
                .anyMatch(e -> e.getAcceptCustomerType().equals(AcceptCustomerType.NEW_CUSTOMER));
        boolean isOnlyAcceptExisting = acceptClientTypes.stream()
                .anyMatch(e -> e.getAcceptCustomerType().equals(AcceptCustomerType.EXISTING_CUSTOMER));

        if (isOnlyAcceptNew && isExisting) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Only accept new customer.");
        }

        if (isOnlyAcceptExisting && !isExisting) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Only accept existing customer.");
        }
    }

    private static Set<ServiceItemType> getPetServiceTypes(SubmitBookingRequestParams request) {
        return request.getPetServicesList().stream()
                .flatMap(petServices -> petServices.getServicesList().stream())
                .map(BookingRequestServer::fromService)
                .collect(Collectors.toSet());
    }

    private static ServiceItemType fromService(Service service) {
        return switch (service.getServiceCase()) {
            case GROOMING -> ServiceItemType.GROOMING;
            case BOARDING -> ServiceItemType.BOARDING;
            case DAYCARE -> ServiceItemType.DAYCARE;
            case EVALUATION -> ServiceItemType.EVALUATION;
            case DOG_WALKING -> ServiceItemType.DOG_WALKING;
            case GROUP_CLASS -> ServiceItemType.GROUP_CLASS;
            default -> throw new IllegalArgumentException("Unknown service case: " + service.getServiceCase());
        };
    }

    private void checkBlocked(BookOnlineDTO bookOnline, SubmitBookingRequestParams request) {
        if (!isExistingCustomer()) {
            return;
        }

        Long customerId = getCustomerId();
        var serviceItemTypes = request.getPetServicesList().stream()
                .flatMap(petServices -> petServices.getServicesList().stream())
                .map(service -> convert(service.getServiceCase()))
                .distinct()
                .toList();

        // get blocked service item types
        var listBlockCustomerResponse =
                customerAvailabilityServiceBlockingStub.listBlockedCustomer(ListBlockedCustomerRequest.newBuilder()
                        .setCompanyId(bookOnline.getCompanyId())
                        .addAllServiceItemTypes(serviceItemTypes)
                        .addCustomerIds(customerId)
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(10)
                                .build())
                        .build());
        listBlockCustomerResponse.getCustomerBlockInfosList().stream()
                .filter(customerBlockInfo -> Objects.equals(customerBlockInfo.getCustomerId(), customerId))
                .findFirst()
                .ifPresent(customerBlockInfo -> {
                    throw bizException(Code.CODE_PARAMS_ERROR, "You have been blocked from online booking.");
                });
    }

    private ServiceItemType convert(Service.ServiceCase serviceCase) {
        return switch (serviceCase) {
            case GROOMING -> ServiceItemType.GROOMING;
            case BOARDING -> ServiceItemType.BOARDING;
            case DAYCARE -> ServiceItemType.DAYCARE;
            case EVALUATION -> ServiceItemType.EVALUATION;
            case DOG_WALKING -> ServiceItemType.DOG_WALKING;
            case GROUP_CLASS -> ServiceItemType.GROUP_CLASS;
            default -> throw new IllegalArgumentException("Unknown service case: " + serviceCase);
        };
    }

    private static boolean isExistingCustomer() {
        return isNormal(getCustomerId());
    }

    private static Long getCustomerId() {
        return AuthContext.get().customerId();
    }

    private void checkAgreement(SubmitBookingRequestParams request, OBBusinessDTO biz) {
        var needSignAgreements = listUnsignedAgreement(biz);

        if (request.getAgreementsCount() < needSignAgreements.size()) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR,
                    "Agreement count not match, expected: " + needSignAgreements.size() + ", actual: "
                            + request.getAgreementsCount());
        }

        for (Agreement agreement : request.getAgreementsList()) {
            if (!StringUtils.hasText(agreement.getSignature())) {
                throw bizException(Code.CODE_PARAMS_ERROR, "Signature is empty.");
            }
        }
    }

    private List<AgreementModel> listUnsignedAgreement(OBBusinessDTO biz) {
        var builder = ListUnsignedAgreementRequest.newBuilder();
        builder.setCompanyId(biz.getCompanyId());
        builder.addBusinessIds(biz.getBusinessId());
        builder.setServiceTypes(ServiceType.SERVICE_TYPE_ONLINE_BOOKING_VALUE);
        Optional.ofNullable(getCustomerId()).filter(CommonUtil::isNormal).ifPresent(builder::setCustomerId);
        return agreementStub.listUnsignedAgreement(builder.build()).getAgreementsList();
    }

    private static void checkOBEnabled(BookOnlineDTO bookOnline) {
        if (!Objects.equals(bookOnline.getIsEnable(), CommonConstant.ENABLE)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Online booking is not enabled");
        }
    }

    private static void checkAddress(BookOnlineDTO bookOnline, SubmitBookingRequestParams request) {
        if (Objects.equals(bookOnline.getIsNeedAddress(), BooleanEnum.VALUE_TRUE)
                && Objects.equals(bookOnline.getIsCheckExistingClient(), BooleanEnum.VALUE_TRUE)
                && !request.getCustomer().hasAddress()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Online booking requires address.");
        }
    }

    private void checkBoarding(SubmitBookingRequestParams request, OBBusinessDTO biz, LocalDate businessDate) {
        var boardingSetting = mustGetBoardingServiceAvailability(biz);

        checkBoardingRange(boardingSetting, request, businessDate);

        checkBoardingPetType(request, boardingSetting.getAcceptedPetTypesValueList());
    }

    private void checkDaycare(SubmitBookingRequestParams request, OBBusinessDTO biz, LocalDate businessDate) {
        var daycareSetting = mustGetDaycareServiceAvailability(biz);

        checkDaycareRange(daycareSetting, request, businessDate);

        checkDaycarePetType(request, daycareSetting.getAcceptedPetTypesValueList());
    }

    private void checkBoardingPetType(SubmitBookingRequestParams request, List<Integer> acceptedPetTypes) {
        var isExistingToPets = request.getPetServicesList().stream()
                .filter(e -> e.getServicesList().stream().anyMatch(it -> it.getServiceCase() == BOARDING))
                .map(PetServices::getPet)
                .collect(partitioningBy(e -> isNormal(e.getPetId())));

        checkPetType(isExistingToPets, acceptedPetTypes);
    }

    private void checkDaycarePetType(SubmitBookingRequestParams request, List<Integer> acceptedPetTypes) {
        var isExistingToPets = request.getPetServicesList().stream()
                .filter(e -> e.getServicesList().stream().anyMatch(it -> it.getServiceCase() == DAYCARE))
                .map(PetServices::getPet)
                .collect(partitioningBy(e -> isNormal(e.getPetId())));

        checkPetType(isExistingToPets, acceptedPetTypes);
    }

    private void checkPetType(Map<Boolean, List<Pet>> isExistingToPets, List<Integer> acceptedPetTypes) {
        Optional.ofNullable(isExistingToPets.get(true))
                .map(pets ->
                        pets.stream().map(Pet::getPetId).map(Long::intValue).toList())
                .map(petApi::getCustomerPetListByIdList)
                .stream()
                .flatMap(List::stream)
                .map(CustomerPetDetailDTO::getPetTypeId)
                .filter(Predicate.not(acceptedPetTypes::contains))
                .findFirst()
                .ifPresent(e -> {
                    throw bizException(
                            Code.CODE_PARAMS_ERROR, "Pet type not accepted: " + e + ", accepted: " + acceptedPetTypes);
                });

        Optional.ofNullable(isExistingToPets.get(false))
                .map(pets -> pets.stream().map(Pet::getPetTypeId).toList())
                .stream()
                .flatMap(List::stream)
                .filter(Predicate.not(acceptedPetTypes::contains))
                .findFirst()
                .ifPresent(e -> {
                    throw bizException(
                            Code.CODE_PARAMS_ERROR, "Pet type not accepted: " + e + ", accepted: " + acceptedPetTypes);
                });
    }

    private void checkDaycareRange(
            DaycareServiceAvailabilityModel daycareSetting,
            SubmitBookingRequestParams request,
            LocalDate businessDate) {
        request.getPetServicesList().stream()
                .map(PetServices::getServicesList)
                .flatMap(List::stream)
                .filter(e -> e.getServiceCase() == DAYCARE)
                .map(Service::getDaycare)
                .forEach(service -> {
                    List<LocalDate> dates = service.getDatesList().stream()
                            .map(LocalDate::parse)
                            .sorted()
                            .toList();
                    if (!dates.isEmpty()) {
                        checkRange(
                                daycareSetting.getBookingDateRange(),
                                businessDate,
                                dates.get(0),
                                dates.get(dates.size() - 1));
                    }
                });
    }

    private BookOnlineDTO mustGetBookOnline(int businessId) {
        return Optional.ofNullable(onlineBookingApi.getOBSetting(businessId))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "OB setting not found for business: " + businessId));
    }

    private DaycareServiceAvailabilityModel mustGetDaycareServiceAvailability(OBBusinessDTO biz) {
        Tenant tenant = Tenant.newBuilder()
                .setCompanyId(biz.getCompanyId())
                .setBusinessId(biz.getBusinessId())
                .build();
        GetDaycareServiceAvailabilitySettingResponse resp =
                obAvailabilitySettingStub.getDaycareServiceAvailabilitySetting(
                        GetDaycareServiceAvailabilitySettingRequest.newBuilder()
                                .setTenant(tenant)
                                .build());
        if (!resp.hasDaycareServiceAvailabilitySetting()) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR,
                    "Daycare service availability setting not found for tenant: " + JsonUtil.toJson(tenant));
        }
        return resp.getDaycareServiceAvailabilitySetting();
    }

    private void checkBoardingRange(
            BoardingServiceAvailabilityModel boardingSetting,
            SubmitBookingRequestParams request,
            LocalDate businessDate) {
        request.getPetServicesList().stream()
                .map(PetServices::getServicesList)
                .flatMap(List::stream)
                .filter(e -> e.getServiceCase() == BOARDING)
                .filter(e -> e.getBoarding().hasStartDate() && e.getBoarding().hasEndDate())
                .map(Service::getBoarding)
                .forEach(service -> checkRange(
                        boardingSetting.getBookingDateRange(),
                        businessDate,
                        LocalDate.parse(service.getStartDate()),
                        LocalDate.parse(service.getEndDate())));
    }

    private void checkRange(
            DateRangeDef range, LocalDate businessDate, LocalDate requestStartDate, LocalDate requestEndDate) {
        LocalDate startDate =
                switch (range.getStartDateType()) {
                    case DATE_TYPE_OFFSET -> businessDate.plusDays(range.getMaxStartDateOffset());
                    case DATE_TYPE_SPECIFIC -> ProtobufUtil.toLocalDate(range.getSpecificStartDate());
                    default -> throw bizException(
                            Code.CODE_PARAMS_ERROR, "Unknown start date type: " + range.getStartDateType());
                };
        LocalDate endDate =
                switch (range.getEndDateType()) {
                    case DATE_TYPE_OFFSET -> businessDate.plusDays(range.getMaxEndDateOffset());
                    case DATE_TYPE_SPECIFIC -> ProtobufUtil.toLocalDate(range.getSpecificEndDate());
                    default -> throw bizException(
                            Code.CODE_PARAMS_ERROR, "Unknown end date type: " + range.getEndDateType());
                };
        if (requestStartDate.isBefore(startDate) || requestStartDate.isAfter(endDate)) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR,
                    "start date out of range: " + requestStartDate + ", valid range: " + startDate + " - " + endDate);
        }
        if (requestEndDate.isBefore(startDate) || requestEndDate.isAfter(endDate)) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR,
                    "end date out of range: " + requestEndDate + ", valid range: " + startDate + " - " + endDate);
        }
    }

    private BoardingServiceAvailabilityModel mustGetBoardingServiceAvailability(OBBusinessDTO biz) {
        Tenant tenant = Tenant.newBuilder()
                .setCompanyId(biz.getCompanyId())
                .setBusinessId(biz.getBusinessId())
                .build();
        GetBoardingServiceAvailabilitySettingResponse resp =
                obAvailabilitySettingStub.getBoardingServiceAvailabilitySetting(
                        GetBoardingServiceAvailabilitySettingRequest.newBuilder()
                                .setTenant(tenant)
                                .build());
        if (!resp.hasBoardingServiceAvailabilitySetting()) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR,
                    "Boarding service availability setting not found for tenant: " + JsonUtil.toJson(tenant));
        }
        return resp.getBoardingServiceAvailabilitySetting();
    }

    private void deleteAbandonRecord(CreateBookingRequestContext ctx, String phoneNumber) {
        abandonRecordApi.deleteRecordsWhenSubmitBookingRequest(
                (int) ctx.businessId(), (int) ctx.customerId, phoneNumber);
    }

    private BookingRequestModel mustGetBookingRequest(long bookingRequestId) {
        GetBookingRequestResponse resp = bookingRequestStub.getBookingRequest(GetBookingRequestRequest.newBuilder()
                .setId(bookingRequestId)
                .addAssociatedModels(BookingRequestAssociatedModel.SERVICE)
                .addAssociatedModels(BookingRequestAssociatedModel.ADD_ON)
                .build());
        if (!resp.hasBookingRequest()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Booking request not found: " + bookingRequestId);
        }
        return resp.getBookingRequest();
    }

    private MoeBusinessCustomerDTO mustGetCustomer(long customerId) {
        return Optional.ofNullable(customerApi.getCustomerWithDeleted((int) customerId))
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Customer not found: " + customerId));
    }

    private void createSession(CreateBookingRequestContext ctx, OBAnonymousParams anonymousParams) {
        CreateOBLoginTokenParams params = CreateOBLoginTokenParams.builder()
                .customerId((int) ctx.customerId())
                .ip(RequestUtils.getIP())
                .userAgent(RequestUtils.getUserAgent())
                .refererLink(RequestUtils.getReferer())
                .mainSession(OBSessionUtil.mustGetOBSession(anonymousParams))
                .build();
        customerOnlineBookingApi.genLoginToken(params);
    }

    private void saveCard(Customer customer, CreateBookingRequestContext ctx) {
        if (!customer.getHasStripeCard() && StringUtils.hasText(customer.getChargeToken())) {
            obApi.saveCard(IOBService.SaveCardParam.builder()
                    .businessId((int) ctx.businessId())
                    .customerId((int) ctx.customerId())
                    .chargeToken(customer.getChargeToken())
                    .isNewCustomer(ctx.isNewCustomer())
                    .build());
        }
    }

    private void addQuestionSave(
            SubmitBookingRequestParams request, CreateBookingRequestContext ctx, IdentityHashMap<Pet, Long> petToId) {
        BookOnlineQuestionSaveDTO customQuestion = new BookOnlineQuestionSaveDTO();
        customQuestion.setBusinessId((int) ctx.businessId());
        customQuestion.setCompanyId(ctx.companyId());
        customQuestion.setCustomerId((int) ctx.customerId());

        Map<String, Object> clientQuestionMap =
                JsonUtil.toBean(JsonUtil.toJson(request.getCustomer().getAnswersMapMap()), new TypeRef<>() {});
        customQuestion.setClientCustomQuestionMap(clientQuestionMap);

        Map<Integer, Map<String, Object>> petQuestionMap = new HashMap<>();
        for (var en : petToId.entrySet()) {
            Pet pet = en.getKey();
            Map<String, Object> petAnswers =
                    JsonUtil.toBean(JsonUtil.toJson(pet.getPetQuestionAnswersMap()), new TypeRef<>() {});
            petQuestionMap.put(toIntExact(petToId.get(pet)), petAnswers);
        }

        customQuestion.setPetCustomQuestionMap(petQuestionMap);

        // saveCustomerQuestionSave 会新增 customer_note 和 pet_note
        bookOnlineQuestionApi.saveCustomerQuestionSave(customQuestion);
    }

    private long addBookingRequest(
            SubmitBookingRequestParams request, CreateBookingRequestContext ctx, IdentityHashMap<Pet, Long> petToId) {
        // 将服务分为两组：带 waitlist 的和不带 waitlist 的
        Map<Boolean, List<PetServices>> partitionedServices = partitionServicesByWaitlist(request.getPetServicesList());
        List<PetServices> regularServices = partitionedServices.getOrDefault(false, List.of());
        List<PetServices> waitlistServices = partitionedServices.getOrDefault(true, List.of());

        if (waitlistServices.isEmpty() && regularServices.isEmpty()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "No services provided.");
        }

        // 如果既有带 waitlist 的服务，又有不带 waitlist 的服务，创建两个 booking request，status 分别为 SUBMITTED 和 WAIT_LIST
        Long regularBookingRequestId = doCreateBookingRequest(request, ctx, petToId, regularServices);

        // 创建带 waitlist 的 booking request
        Long waitlistBookingRequestId = doCreateBookingRequest(request, ctx, petToId, waitlistServices);
        // 触发 waitlist 通知
        if (regularBookingRequestId != null && waitlistBookingRequestId != null) {
            triggerBookingRequestAutoAccepted(waitlistBookingRequestId);
        }

        if (regularBookingRequestId == null && waitlistBookingRequestId == null) {
            throw bizException(Code.CODE_SERVER_ERROR, "Return id should not be null");
        }

        // 优先返回不带 waitlist 的 booking request ID 作为主 ID
        return regularBookingRequestId != null ? regularBookingRequestId : waitlistBookingRequestId;
    }

    @Nullable
    private Long doCreateBookingRequest(
            SubmitBookingRequestParams request,
            CreateBookingRequestContext ctx,
            IdentityHashMap<Pet, Long> petToId,
            List<PetServices> petServicesList) {

        if (petServicesList.isEmpty()) {
            return null;
        }

        CreateBookingRequestRequest.Builder builder = CreateBookingRequestRequest.newBuilder();
        builder.setCompanyId(ctx.companyId());
        builder.setBusinessId(ctx.businessId());
        builder.setCustomerId(ctx.customerId());
        builder.setStatus(BookingRequestStatus.SUBMITTED.getNumber());
        builder.setIsPrepaid(false);
        if (request.hasAdditionalNotes()) {
            builder.setAdditionalNote(request.getAdditionalNotes());
        }
        var instanceIdToServiceId = getGroupClassByInstanceId(petServicesList);
        builder.addAllServices(toServices(petServicesList, petToId, instanceIdToServiceId));
        builder.setAttr(BookingRequestModel.Attr.newBuilder()
                .setIsNewVisitor(ctx.isNewCustomer())
                .build());
        if (needPayment(request)) {
            if (isOrderDecouplingFlow(request)) {
                // 如果是新流程（先 submit 后 pay），状态设置为 WAITING
                // 老流程（先 pay 后 submit）会在 confirm PaymentIntent 回调里设置为 PROCESSING
                builder.setPaymentStatus(BookingRequestModel.PaymentStatus.WAITING);
            } else {
                // 注意：老流程（先 pay 后 submit）肯定已经是 confirmed PaymentIntent
                // 由于 confirm PaymentIntent 回调里修改为 PROCESSING 和创建 BookingRequest 这两个逻辑是异步的，
                // 不能确定是回调先执行还是 BookingRequest 先创建，所以这里必须手动将 PaymentStatus 设置为 PROCESSING
                builder.setPaymentStatus(BookingRequestModel.PaymentStatus.PROCESSING);
            }
        }

        builder.setSource(BookingRequestModel.Source.OB);

        return bookingRequestStub.createBookingRequest(builder.build()).getValue();
    }

    /**
     * Generated by augmentcode
     */
    private static Map<Boolean, List<PetServices>> partitionServicesByWaitlist(List<PetServices> petServicesList) {
        Map<Boolean, List<PetServices>> result = new HashMap<>();

        for (PetServices petServices : petServicesList) {
            Pet pet = petServices.getPet();

            // 将服务分为两组：带 waitlist 的和不带 waitlist 的
            List<Service> regularServices = new ArrayList<>();
            List<Service> waitlistServices = new ArrayList<>();

            for (Service service : petServices.getServicesList()) {

                boolean hasWaitlist =
                        switch (service.getServiceCase()) {
                            case BOARDING -> service.getBoarding().hasWaitlist();
                            case DAYCARE -> service.getDaycare().hasWaitlist();
                                // Add other service types here
                            default -> false;
                        };

                if (hasWaitlist) {
                    waitlistServices.add(service);
                } else {
                    regularServices.add(service);
                }
            }

            // 如果有常规服务，创建一个新的 PetServices 对象
            if (!regularServices.isEmpty()) {
                PetServices regularPetServices = PetServices.newBuilder()
                        .setPet(pet)
                        .addAllServices(regularServices)
                        .build();
                result.computeIfAbsent(false, k -> new ArrayList<>()).add(regularPetServices);
            }

            // 如果有 waitlist 服务，创建一个新的 PetServices 对象
            if (!waitlistServices.isEmpty()) {
                PetServices waitlistPetServices = PetServices.newBuilder()
                        .setPet(pet)
                        .addAllServices(waitlistServices)
                        .build();
                result.computeIfAbsent(true, k -> new ArrayList<>()).add(waitlistPetServices);
            }
        }

        return result;
    }

    private Map</* GroupClassInstanceID */ Long, /* ServiceID */ Long> getGroupClassByInstanceId(
            List<PetServices> petServicesList) {
        var groupClassInstanceIds = petServicesList.stream()
                .flatMap(petServices -> petServices.getServicesList().stream())
                .filter(Service::hasGroupClass)
                .map(Service::getGroupClass)
                .map(Service.GroupClass::getGroupClassInstanceId)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(groupClassInstanceIds)) {
            return Map.of();
        }
        return groupClassStub
                .listInstances(ListInstancesRequest.newBuilder()
                        .addAllIds(groupClassInstanceIds)
                        .build())
                .getGroupClassInstancesList()
                .stream()
                .collect(Collectors.toMap(GroupClassInstance::getId, GroupClassInstance::getGroupClassId));
    }

    private static List<CreateBookingRequestRequest.Service> toServices(
            List<PetServices> petServices, Map<Pet, Long> petToId, Map<Long, Long> instanceIdToServiceId) {
        List<CreateBookingRequestRequest.Service> result = new ArrayList<>();
        for (PetServices requestService : petServices) {
            Pet pet = requestService.getPet();
            for (Service service : requestService.getServicesList()) {
                switch (service.getServiceCase()) {
                    case GROOMING -> result.add(CreateBookingRequestRequest.Service.newBuilder()
                            .setGrooming(ServiceConverter.buildGroomingService(service.getGrooming(), petToId.get(pet)))
                            .build());
                    case BOARDING -> result.add(CreateBookingRequestRequest.Service.newBuilder()
                            .setBoarding(ServiceConverter.buildBoardingService(service.getBoarding(), petToId.get(pet)))
                            .build());
                    case DAYCARE -> result.add(CreateBookingRequestRequest.Service.newBuilder()
                            .setDaycare(ServiceConverter.buildDaycareService(service.getDaycare(), petToId.get(pet)))
                            .build());
                    case EVALUATION -> result.add(CreateBookingRequestRequest.Service.newBuilder()
                            .setEvaluation(
                                    ServiceConverter.buildEvaluationService(service.getEvaluation(), petToId.get(pet)))
                            .build());
                    case DOG_WALKING -> result.add(CreateBookingRequestRequest.Service.newBuilder()
                            .setDogWalking(
                                    ServiceConverter.buildDogWalkingService(service.getDogWalking(), petToId.get(pet)))
                            .build());
                    case GROUP_CLASS -> result.add(CreateBookingRequestRequest.Service.newBuilder()
                            .setGroupClass(ServiceConverter.buildGroupClassService(
                                    service.getGroupClass(),
                                    petToId.get(pet),
                                    instanceIdToServiceId.get(
                                            service.getGroupClass().getGroupClassInstanceId())))
                            .build());
                    default -> throw bizException(
                            Code.CODE_PARAMS_ERROR, "Unknown service type: " + service.getServiceCase());
                }
            }
        }
        return result;
    }

    private static CreateBookingRequestRequest.Service buildCreateRequest(Service service, long petId) {
        return switch (service.getServiceCase()) {
            case GROOMING -> CreateBookingRequestRequest.Service.newBuilder()
                    .setGrooming(ServiceConverter.buildGroomingService(service.getGrooming(), petId))
                    .build();
            case BOARDING -> CreateBookingRequestRequest.Service.newBuilder()
                    .setBoarding(ServiceConverter.buildBoardingService(service.getBoarding(), petId))
                    .build();
            case DAYCARE -> CreateBookingRequestRequest.Service.newBuilder()
                    .setDaycare(ServiceConverter.buildDaycareService(service.getDaycare(), petId))
                    .build();
            case EVALUATION -> CreateBookingRequestRequest.Service.newBuilder()
                    .setEvaluation(ServiceConverter.buildEvaluationService(service.getEvaluation(), petId))
                    .build();
            case DOG_WALKING -> CreateBookingRequestRequest.Service.newBuilder()
                    .setDogWalking(ServiceConverter.buildDogWalkingService(service.getDogWalking(), petId))
                    .build();
            default -> throw bizException(
                    Code.CODE_PARAMS_ERROR, "Unsupported service type: " + service.getServiceCase());
        };
    }

    private IdentityHashMap<Pet, Long> addPets(List<Pet> pets, CreateBookingRequestContext ctx) {
        // Protobuf 的 message 重写了 equals/hashCode，相同的对象不一定相等，所以用 IdentityHashMap
        var petToId = new IdentityHashMap<Pet, Long>();
        for (Pet pet : pets) {
            long id = isNormal(pet.getPetId()) ? pet.getPetId() : createPet(pet, ctx);
            petToId.put(pet, id);
        }
        return petToId;
    }

    private void addAgreements(List<Agreement> agreements, long bookingRequestId) {
        if (ObjectUtils.isEmpty(agreements)) return;

        var list = agreements.stream()
                .map(e -> IOBService.SaveAgreementForObSubmitParam.Agreement.builder()
                        .agreementId((int) e.getId())
                        .agreementConfirmed((byte) 1)
                        .signature(e.getSignature())
                        .agreementHeader("")
                        .agreementContent("")
                        .build())
                .toList();

        var param = IOBService.SaveAgreementForObSubmitParam.builder()
                .agreements(list)
                .bookingRequestId((int) bookingRequestId)
                .build();
        obApi.saveAgreementForObSubmit(param);
    }

    private long createPet(Pet pet, CreateBookingRequestContext ctx) {
        CustomerPetAddParams param = new CustomerPetAddParams();
        param.setCustomerId((int) ctx.customerId());
        param.setCompanyId(ctx.companyId());
        param.setBusinessId((int) ctx.businessId());
        param.setPetName(pet.getPetName());
        param.setPetTypeId(pet.getPetTypeId());
        param.setAvatarPath(pet.getAvatarPath());
        param.setBreed(pet.getBreed());
        param.setBreedMix((byte) pet.getBreedMix());
        param.setBirthday(pet.getBirthday());
        param.setGender((byte) pet.getGender());
        param.setHairLength(pet.getHairLength());
        param.setBehavior(pet.getBehavior());
        param.setWeight(pet.getWeight());
        param.setFixed(pet.getFixed());
        if (pet.hasExpiryNotification()) {
            param.setExpiryNotification((byte) pet.getExpiryNotification());
        }
        param.setVetName(pet.getVetName());
        param.setVetPhone(pet.getVetPhone());
        param.setVetAddress(pet.getVetAddress());
        param.setEmergencyContactName(pet.getEmergencyContactName());
        param.setEmergencyContactPhone(pet.getEmergencyContactPhone());
        param.setHealthIssues(pet.getHealthIssues());
        // C 端新增/更新 vaccine 的场景，都需要经过 B 端 review，这里 set 空，避免直接将 vaccine 写入到 B 端
        param.setVaccineList(List.of());

        var res = petApi.insertCustomerPet(ctx.companyId(), (int) ctx.businessId(), param);
        if (!Boolean.TRUE.equals(res.getSuccess())) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "Failed to create pet, return: %s, request param: %s",
                            JsonUtil.toJson(res), JsonUtil.toJson(param)));
        }

        var petId = res.getData();

        // 如果有疫苗信息，通过 profile request 机制处理，需要 B 端 review
        if (pet.getVaccineListCount() > 0) {
            updateProfileRequest(pet.getVaccineListList(), (int) ctx.businessId(), (int) ctx.customerId(), petId);
        }

        return petId;
    }

    private void updateProfileRequest(
            List<Pet.Vaccine> vaccines, Integer businessId, Integer customerId, Integer petId) {
        var obSetting = onlineBookingApi.getOBSetting(businessId);
        if (obSetting == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "OB setting not found, businessId: " + businessId);
        }

        boolean autoAcceptConflict =
                OBRequestSubmittedAutoTypeEnum.isAutoAcceptConflict(obSetting.getRequestSubmittedAutoType());

        var vaccineList = vaccines.stream()
                .map(e -> {
                    var vaccine = new VaccineBindingRecordDto();
                    if (isNormal(e.getVaccineBindingId())) {
                        vaccine.setVaccineBindingId((int) e.getVaccineBindingId());
                    }
                    vaccine.setVaccineId(e.getVaccineId());
                    vaccine.setExpirationDate(e.getExpirationDate());
                    vaccine.setDocumentUrls(e.getDocumentUrlsList());
                    return vaccine;
                })
                .toList();

        customerProfileRequestApi.updateCustomerAndProfileRequest(
                CustomerProfileRequestDTO.builder()
                        .businessId(businessId)
                        .companyId(obSetting.getCompanyId())
                        .customerId(customerId)
                        .pets(List.of(new CustomerProfileRequestDTO.PetProfileDTO()
                                .setPetId(petId)
                                .setVaccineList(vaccineList)))
                        .build(),
                autoAcceptConflict);
    }

    private long addCustomer(Customer customer, OBBusinessDTO biz) {
        SaveWithPetCustomerVo param = new SaveWithPetCustomerVo();
        param.setPreferredBusinessId(Long.valueOf(biz.getBusinessId()));
        param.setFirstName(customer.getFirstName());
        param.setLastName(customer.getLastName());
        param.setPhoneNumber(customer.getPhoneNumber());
        param.setAvatarPath("");
        param.setEmail(customer.getEmail());

        if (customer.hasBirthday()) {
            var birthday = customer.getBirthday();
            param.setBirthday(LocalDateTime.ofInstant(
                    Instant.ofEpochSecond(birthday.getSeconds(), birthday.getNanos()), ZoneId.systemDefault()));
        }

        if (customer.hasAddress()
                && customer.getAddress().hasLat()
                && customer.getAddress().hasLng()) {
            // create customer address
            Address address = customer.getAddress();
            param.setAddress1(address.getAddress1());
            param.setCity(address.getCity());
            param.setState(address.getState());
            param.setZipcode(address.getZipcode());
            param.setCountry(address.getCountry());
            param.setAddress2(address.getAddress2());
            param.setLat(address.getLat());
            param.setLng(address.getLng());
        }

        if (customer.hasAdditionalInfo()) {
            Customer.AdditionalInfo additionalInfo = customer.getAdditionalInfo();
            if (additionalInfo.hasReferralSourceId()) {
                param.setReferralSourceId(additionalInfo.getReferralSourceId());
            }
            if (additionalInfo.hasReferralSourceDesc()) {
                param.setReferralSourceDesc(additionalInfo.getReferralSourceDesc());
            }
            if (additionalInfo.hasPreferredGroomerId()) {
                param.setPreferredGroomerId(additionalInfo.getPreferredGroomerId());
            }
            if (additionalInfo.hasPreferredFrequencyDay()) {
                param.setPreferredFrequencyDay(additionalInfo.getPreferredFrequencyDay());
            }
            if (additionalInfo.hasPreferredFrequencyType()) {
                param.setPreferredFrequencyType((byte) additionalInfo.getPreferredFrequencyType());
            }
            if (additionalInfo.getPreferredDayCount() > 0) {
                param.setPreferredDay(additionalInfo.getPreferredDayList().toArray(Integer[]::new));
            }
            if (additionalInfo.getPreferredTimeCount() > 0) {
                param.setPreferredTime(additionalInfo.getPreferredTimeList().toArray(Integer[]::new));
            }
        }

        param.setEmergencyContact(convertToAdditionalContact(customer.getEmergencyContact()));
        param.setPickupContact(convertToAdditionalContact(customer.getPickupContact()));
        param.setPetList(List.of());
        param.setSource(ClientSourceEnum.SOURCE_ONLINE_BOOKING.getSource());

        SaveCustomerPetResultDto res =
                iCustomerComposeApi.createCustomerAndPets(biz.getCompanyId(), biz.getBusinessId(), 0, param);

        if (!Boolean.TRUE.equals(res.getResult())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Failed to create customer and pets.");
        }

        return res.getId();
    }

    private AdditionalContactDTO convertToAdditionalContact(Customer.Contact contact) {
        AdditionalContactDTO additionalContact = new AdditionalContactDTO();
        if (Objects.isNull(contact)) return additionalContact;

        additionalContact.setFirstName(contact.getFirstName());
        additionalContact.setLastName(contact.getLastName());
        additionalContact.setPhone(contact.getPhoneNumber());
        return additionalContact;
    }

    private record CreateBookingRequestContext(
            long companyId, long businessId, boolean isNewCustomer, long customerId) {}

    @Override
    @Auth(AuthType.ACCOUNT)
    public void rescheduleBookingRequest(
            RescheduleBookingRequestParams request, StreamObserver<RescheduleBookingRequestResult> responseObserver) {
        var bookingRequest = getAccountBelongsBookingRequest(request.getId());
        if (!isRescheduleAllowed(bookingRequest)) {
            throw bizException(Code.CODE_RESCHEDULE_NOT_ALLOWED);
        }

        // 1. Checks if auto-assign is needed
        String startDate = request.getGroomingReschedule().getStartDate();
        var autoAssignResult = needAutoAssign(request.getGroomingReschedule())
                ? bookingService.doAutoAssign(bookingRequest, request.getGroomingReschedule())
                : null;
        int startTime = Optional.ofNullable(autoAssignResult)
                .map(IOBService.AutoAssignResult::appointmentTime)
                .orElse(request.getGroomingReschedule().getStartTime());
        long staffId = Optional.ofNullable(autoAssignResult)
                .map(result -> result.staffId().longValue())
                .orElse(request.getGroomingReschedule().getStaffId());

        // 2. Calculate offset duration
        var duration = Duration.between(
                convertLocalDateTime(bookingRequest.getStartDate(), bookingRequest.getStartTime()),
                convertLocalDateTime(startDate, startTime));

        // 3. Reschedule booking request
        // 偏移 service 和 add-on 的 start date 和 start time
        List<UpdateBookingRequestRequest.Service> services = bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasGrooming)
                .map(service -> {
                    var grooming = service.getGrooming();

                    var builder = UpdateBookingRequestRequest.GroomingService.newBuilder()
                            .setService(buildService(grooming.getService(), staffId, duration))
                            .addAllAddons(buildAddOns(grooming.getAddonsList(), staffId, duration));
                    if (autoAssignResult != null) {
                        var assignBuilder = UpsertGroomingAutoAssignRequest.newBuilder()
                                .setBookingRequestId(bookingRequest.getId());
                        Optional.ofNullable(autoAssignResult.staffId()).ifPresent(assignBuilder::setStaffId);
                        Optional.ofNullable(autoAssignResult.appointmentTime()).ifPresent(assignBuilder::setStartTime);
                        builder.setAutoAssign(assignBuilder.build());
                    }
                    return UpdateBookingRequestRequest.Service.newBuilder()
                            .setGrooming(builder)
                            .build();
                })
                .toList();
        LocalDateTime newEndDateTime = convertLocalDateTime(bookingRequest.getEndDate(), bookingRequest.getEndTime())
                .plus(duration);

        boolean autoAcceptRequest = isAutoAcceptRequest(bookingRequest);

        var builder = UpdateBookingRequestRequest.newBuilder()
                .setId(request.getId())
                .setStartDate(startDate)
                .setStartTime(startTime)
                .setEndDate(newEndDateTime.toLocalDate().toString())
                .setEndTime(newEndDateTime.toLocalTime().toSecondOfDay() / 60)
                .addAllServices(services);
        if (autoAcceptRequest) {
            builder.setStatus(BookingRequestStatus.SCHEDULED);
        }
        bookingRequestStub.updateBookingRequest(builder.build());

        // 4. Reschedule appointment。相当于拖拽 booking request 整张卡片
        appointmentScheduleService.rescheduleGroomingService(RescheduleGroomingServiceRequest.newBuilder()
                .setAppointmentId(bookingRequest.getAppointmentId())
                .setId(bookingRequest.getAppointmentId())
                .setCardType(CalendarCardType.BOOKING_REQUEST)
                .setStaffId(staffId)
                .setStartDate(startDate)
                .setStartTime(startTime)
                .setCompanyId(bookingRequest.getCompanyId())
                .build());
        // 5. Update appointment status if auto accept
        if (autoAcceptRequest) {
            appointmentService.updateAppointmentSelective(UpdateAppointmentSelectiveRequest.newBuilder()
                    .setId(bookingRequest.getAppointmentId())
                    .setBookOnlineStatus(AppointmentBookOnlineStatus.NOT_BOOK_ONLINE)
                    .build());
        }

        // 6. Activity log
        ZoneId zoneId = companyService.getZoneId(bookingRequest.getCompanyId());
        activityLogService.createActivityLog(CreateActivityLogRequest.newBuilder()
                .setCompanyId(bookingRequest.getCompanyId())
                .setBusinessId(bookingRequest.getBusinessId())
                .setOperatorId(String.valueOf(bookingRequest.getCustomerId()))
                .setIsRoot(true)
                .setAction(AppointmentAction.RESCHEDULE)
                .setResourceType(Resource.Type.APPOINTMENT)
                .setResourceId(String.valueOf(bookingRequest.getAppointmentId()))
                .setDetails(JsonUtil.toJson(new ChangeTimeLogDTO(
                        DateConverter.INSTANCE.toTimestamp(
                                bookingRequest.getStartDate(), bookingRequest.getStartTime(), zoneId),
                        DateConverter.INSTANCE.toTimestamp(startDate, startTime, zoneId),
                        AppointmentUpdatedBy.BY_PET_PARENT_APP)))
                .build());

        responseObserver.onNext(RescheduleBookingRequestResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private boolean isAutoAcceptRequest(BookingRequestModel bookingRequest) {
        var businessId = toIntExact(bookingRequest.getBusinessId());
        var bookOnline = mustGetBookOnline(businessId);
        var hasRequestDTO = obApi.listCustomerHasRequestUpdate(
                        new CustomerIdsParams(businessId, List.of(toIntExact(bookingRequest.getCustomerId()))))
                .get(toIntExact(bookingRequest.getCustomerId()));
        return OBRequestSubmittedAutoTypeEnum.isAutoAcceptRequest(
                bookOnline.getRequestSubmittedAutoType(), hasRequestDTO.hasRequestUpdate());
    }

    private UpdateGroomingServiceDetailRequest buildService(
            GroomingServiceDetailModel service, long staffId, Duration duration) {
        var serviceStartDateTime = convertLocalDateTime(service.getStartDate(), service.getStartTime())
                .plus(duration);
        var serviceEndDateTime =
                convertLocalDateTime(service.getEndDate(), service.getEndTime()).plus(duration);
        return UpdateGroomingServiceDetailRequest.newBuilder()
                .setId(service.getId())
                .setStaffId(staffId)
                .setStartDate(serviceStartDateTime.toLocalDate().toString())
                .setStartTime(serviceStartDateTime.toLocalTime().toSecondOfDay() / 60)
                .setEndDate(serviceEndDateTime.toLocalDate().toString())
                .setEndTime(serviceEndDateTime.toLocalTime().toSecondOfDay() / 60)
                .build();
    }

    private List<UpdateGroomingAddOnDetailRequest> buildAddOns(
            List<GroomingAddOnDetailModel> addOns, long staffId, Duration duration) {
        return addOns.stream()
                .map(addon -> {
                    var addonStartDateTime = convertLocalDateTime(addon.getStartDate(), addon.getStartTime())
                            .plus(duration);
                    var addonEndDateTime = convertLocalDateTime(addon.getEndDate(), addon.getEndTime())
                            .plus(duration);
                    return UpdateGroomingAddOnDetailRequest.newBuilder()
                            .setId(addon.getId())
                            .setStaffId(staffId)
                            .setStartDate(addonStartDateTime.toLocalDate().toString())
                            .setStartTime(addonStartDateTime.toLocalTime().toSecondOfDay() / 60)
                            .setEndDate(addonEndDateTime.toLocalDate().toString())
                            .setEndTime(addonEndDateTime.toLocalTime().toSecondOfDay() / 60)
                            .build();
                })
                .toList();
    }

    private boolean needAutoAssign(GroomingRescheduleDef rescheduleDef) {
        return StringUtils.hasText(rescheduleDef.getStartDate())
                && (!rescheduleDef.hasStaffId() || !rescheduleDef.hasStartTime());
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void cancelBookingRequest(
            CancelBookingRequestParams request, StreamObserver<CancelBookingRequestResult> responseObserver) {
        var bookingRequest = getAccountBelongsBookingRequest(request.getId());
        if (!isCancellationAllowed(bookingRequest)) {
            throw bizException(Code.CODE_CANCELLATION_NOT_ALLOWED);
        }

        // 1. Update booking request status
        bookingRequestStub.updateBookingRequestStatus(UpdateBookingRequestStatusRequest.newBuilder()
                .setId(bookingRequest.getId())
                .setStatus(BookingRequestStatus.DECLINED)
                .build());

        // 2. Cancel appointment
        // 目前 BD 提交的 appt 和未来解耦后的 grooming 类型应该是创建一个 canceled appt，确保 canceled tab 可见
        var cancelAppointmentRequest = CancelAppointmentRequest.newBuilder()
                .setAppointmentId(bookingRequest.getAppointmentId())
                .setCancelByType(AppointmentUpdatedBy.BY_PET_PARENT_APP)
                .setCancelBy(bookingRequest.getCustomerId())
                .setCancelReason(CANCEL_REASON)
                .setNoShow(AppointmentNoShowStatus.NOT_NO_SHOW)
                .build();
        appointmentService.cancelAppointment(cancelAppointmentRequest);

        // 3. Refund booking fee if required
        refundBookingFeeIfRequired(bookingRequest);

        // 4. Activity log
        activityLogService.createActivityLog(CreateActivityLogRequest.newBuilder()
                .setCompanyId(bookingRequest.getCompanyId())
                .setBusinessId(bookingRequest.getBusinessId())
                .setOperatorId(String.valueOf(bookingRequest.getCustomerId()))
                .setIsRoot(true)
                .setAction(AppointmentAction.CANCEL)
                .setResourceType(Resource.Type.APPOINTMENT)
                .setResourceId(String.valueOf(bookingRequest.getAppointmentId()))
                .setDetails(JsonUtil.toJson(new CancelLogDTO(CANCEL_REASON, AppointmentUpdatedBy.BY_PET_PARENT_APP)))
                .build());

        responseObserver.onNext(CancelBookingRequestResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void updateBookingRequest(
            UpdateBookingRequestParams request, StreamObserver<UpdateBookingRequestResult> responseObserver) {

        var biz = mustGetBusiness(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        var builder = UpdateBookingRequestRequest.newBuilder();
        builder.setCompanyId(biz.getCompanyId());
        builder.setBusinessId(biz.getBusinessId());
        builder.setId(request.getBookingRequestId());
        builder.addAllServiceDetails(request.getServiceDetailsList());

        bookingRequestStub.updateBookingRequest(builder.build());

        ThreadPool.execute(() -> recordActivityLogForUpdateBookingRequest(request));

        responseObserver.onNext(UpdateBookingRequestResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private void recordActivityLogForUpdateBookingRequest(UpdateBookingRequestParams request) {
        var bookingRequest = mustGetBookingRequest(request.getBookingRequestId());

        activityLogService.createActivityLog(CreateActivityLogRequest.newBuilder()
                .setCompanyId(bookingRequest.getCompanyId())
                .setBusinessId(bookingRequest.getBusinessId())
                .setIsRoot(true)
                .setAction(AppointmentAction.CLIENT_UPDATE_OB)
                .setResourceType(Resource.Type.APPOINTMENT)
                .setResourceId(String.valueOf(bookingRequest.getId()))
                .setDetails(JsonUtil.toJson(request))
                .build());
    }

    private static List<UpdateBookingRequestRequest.ServiceDetail> buildServiceDetailsForAdd(PetServices petServices) {
        var pet = petServices.getPet();
        if (!pet.hasPetId()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Only support existing pet");
        }

        var services = petServices.getServicesList();

        return services.stream()
                .map(service -> buildServiceDetailForAdd(pet, service))
                .toList();
    }

    private static UpdateBookingRequestRequest.ServiceDetail buildServiceDetailForAdd(Pet pet, Service service) {
        return UpdateBookingRequestRequest.ServiceDetail.newBuilder()
                .setAdd(buildCreateRequest(service, pet.getPetId()))
                .build();
    }

    private void refundBookingFeeIfRequired(BookingRequestModel bookingRequest) {
        var deposit = obDepositHelper.getOBDeposit(bookingRequest.getBusinessId(), bookingRequest.getAppointmentId());
        if (deposit == null || !Objects.equals(deposit.getStatus(), BookOnlineDepositConst.REQUIRE_CAPTURE)) {
            return;
        }
        CreateRefundByPaymentIdParams params = new CreateRefundByPaymentIdParams();
        params.setPaymentId(deposit.getPaymentId());
        params.setReason(CANCEL_REASON);
        params.setBookingFee(deposit.getBookingFee());
        paymentRefundClient.createRefundByPaymentId(toIntExact(bookingRequest.getBusinessId()), params);
        // Cancel deposit
        MoeBookOnlineDepositVO depositVO = new MoeBookOnlineDepositVO();
        depositVO.setBusinessId(deposit.getId());
        depositVO.setPaymentId(deposit.getPaymentId());
        depositVO.setAmount(deposit.getAmount());
        depositVO.setBookingFee(deposit.getBookingFee());
        depositVO.setTipsAmount(deposit.getTipsAmount());
        depositVO.setStatus(BookOnlineDepositConst.CANCEL);
        bookOnlineDepositClient.updateOBDepositByPaymentId(depositVO);
    }

    private BookingRequestModel getAccountBelongsBookingRequest(long bookingRequestId) {
        var bookingRequest = bookingRequestStub
                .getBookingRequest(GetBookingRequestRequest.newBuilder()
                        .setId(bookingRequestId)
                        .addAllAssociatedModels(
                                List.of(BookingRequestAssociatedModel.SERVICE, BookingRequestAssociatedModel.ADD_ON))
                        .build())
                .getBookingRequest();
        if (customerService.noneMatch(AuthContext.get().accountId(), bookingRequest.getCustomerId())) {
            throw bizException(Code.CODE_APPOINTMENT_NOT_FOUND);
        }
        return bookingRequest;
    }

    private boolean isRescheduleAllowed(BookingRequestModel bookingRequest) {
        return Objects.equals(bookingRequest.getStatus(), BookingRequestStatus.SUBMITTED);
    }

    /**
     * Pending status, not prepaid or not pre-authorized
     *
     * @param bookingRequest booking request model
     * @return true if cancellation is allowed
     */
    private boolean isCancellationAllowed(BookingRequestModel bookingRequest) {
        return Objects.equals(bookingRequest.getStatus(), BookingRequestStatus.SUBMITTED);
    }

    private LocalDateTime convertLocalDateTime(String date, int startTime) {
        return LocalDate.parse(date).atTime(startTime / 60, startTime % 60);
    }

    @Override
    @Auth(AuthType.OB)
    public void calculateBookingRequest(
            CalculateBookingRequestParams request, StreamObserver<CalculateBookingRequestResult> responseObserver) {

        var obAnonymous = new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName());

        var biz = mustGetBusiness(obAnonymous);

        BigDecimal estimatedTotal = cashierService.getCustomizedServicePrice(
                biz.getCompanyId(), biz.getBusinessId(), request.getPetServicesList());

        responseObserver.onNext(CalculateBookingRequestResult.newBuilder()
                .setEstimatedTotalPrice(estimatedTotal.doubleValue())
                .build());
        responseObserver.onCompleted();
    }
}
