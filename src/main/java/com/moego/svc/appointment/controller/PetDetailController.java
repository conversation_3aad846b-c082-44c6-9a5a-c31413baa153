package com.moego.svc.appointment.controller;

import static com.moego.idl.models.offering.v1.ServiceOverrideType.SERVICE_OVERRIDE_TYPE_UNSPECIFIED;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.appointment.utils.PetDetailUtil.buildDateTime;

import com.google.common.collect.Lists;
import com.google.protobuf.Timestamp;
import com.google.type.Interval;
import com.moego.common.constant.CommonConstant;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.appointment.v1.StaffPetDetail;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.LocationStaffOverrideRule;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.StaffOverrideRule;
import com.moego.idl.service.appointment.v1.CreatePetDetailsForExtraOrderRequest;
import com.moego.idl.service.appointment.v1.CreatePetDetailsForExtraOrderResponse;
import com.moego.idl.service.appointment.v1.DeletePetDetailForExtraOrderRequest;
import com.moego.idl.service.appointment.v1.DeletePetDetailForExtraOrderResponse;
import com.moego.idl.service.appointment.v1.DeletePetEvaluationRequest;
import com.moego.idl.service.appointment.v1.DeletePetEvaluationResponse;
import com.moego.idl.service.appointment.v1.DeletePetRequest;
import com.moego.idl.service.appointment.v1.DeletePetResponse;
import com.moego.idl.service.appointment.v1.GetLastPetDetailRequest;
import com.moego.idl.service.appointment.v1.GetLastPetDetailResponse;
import com.moego.idl.service.appointment.v1.GetPetDetailListRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListResponse;
import com.moego.idl.service.appointment.v1.GetPetDetailRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailResponse;
import com.moego.idl.service.appointment.v1.GetStaffPetDetailsRequest;
import com.moego.idl.service.appointment.v1.GetStaffPetDetailsResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentsRequest;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.appointment.v1.SaveOrUpdatePetDetailsRequest;
import com.moego.idl.service.appointment.v1.SaveOrUpdatePetDetailsResponse;
import com.moego.idl.service.appointment.v1.UpdatePetDetailRequest;
import com.moego.idl.service.appointment.v1.UpdatePetDetailResponse;
import com.moego.idl.service.appointment.v1.UpdateUpcomingAppointmentsRequest;
import com.moego.idl.service.appointment.v1.UpdateUpcomingAppointmentsResponse;
import com.moego.idl.service.appointment.v1.UpdateUpcomingPetDetailsRequest;
import com.moego.idl.service.appointment.v1.UpdateUpcomingPetDetailsResponse;
import com.moego.idl.service.offering.v1.GetServiceDetailRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.organization.v1.DateTimeServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyCurrentDayAndTimeRequest;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import com.moego.svc.appointment.constant.AppointmentStatusSet;
import com.moego.svc.appointment.controller.validate.PetDetailValidator;
import com.moego.svc.appointment.converter.PageConverter;
import com.moego.svc.appointment.converter.PetDetailConverter;
import com.moego.svc.appointment.converter.ServiceOverrideConverter;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.MoeGroomingServiceOperation;
import com.moego.svc.appointment.dto.SaveOrUpdatePetDetailDTO;
import com.moego.svc.appointment.dto.ServiceOverrideDTO;
import com.moego.svc.appointment.listener.event.DeleteAppointmentPetDetailEvent;
import com.moego.svc.appointment.listener.event.UpdateUpcomingPetDetailEvent;
import com.moego.svc.appointment.service.AppointmentCompositeService;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import com.moego.svc.appointment.service.EvaluationServiceDetailService;
import com.moego.svc.appointment.service.PetDetailServiceProxy;
import com.moego.svc.appointment.service.PricingRuleRecordApplyService;
import com.moego.svc.appointment.service.RepeatAppointmentService;
import com.moego.svc.appointment.service.ServiceOperationService;
import com.moego.svc.appointment.service.remote.MetadataRemoteService;
import com.moego.svc.appointment.utils.PetDetailUtil;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/1/30
 */
@GrpcService
@RequiredArgsConstructor
public class PetDetailController extends PetDetailServiceGrpc.PetDetailServiceImplBase {

    private final AppointmentCompositeService appointmentCompositeService;
    private final AppointmentServiceProxy appointmentService;
    private final PetDetailServiceProxy petDetailService;
    private final EvaluationServiceDetailService petEvaluationService;
    private final ApplicationEventPublisher publisher;
    private final ServiceOverrideConverter serviceOverrideConverter;
    private final DateTimeServiceGrpc.DateTimeServiceBlockingStub dateTimeService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;
    private final RepeatAppointmentService repeatAppointmentService;
    private final PricingRuleRecordApplyService pricingRuleApplyService;
    private final ServiceOperationService serviceOperationService;
    private final EvaluationServiceDetailService evaluationServiceDetailService;
    private final MetadataRemoteService metadataRemoteService;

    private final PetDetailValidator petDetailValidator;

    private static List<PetDetailDef> buildPetDetails(SaveOrUpdatePetDetailsRequest request) {
        return request.getPetDetailsCount() != 0 ? request.getPetDetailsList() : List.of(request.getPetDetail());
    }

    @Override
    public void saveOrUpdatePetDetails(
            SaveOrUpdatePetDetailsRequest request, StreamObserver<SaveOrUpdatePetDetailsResponse> responseObserver) {
        var petDetailDefs = buildPetDetails(request);
        petDetailValidator.validatePetDetails(petDetailDefs);

        var saveOrUpdatePetDetailDTO = new SaveOrUpdatePetDetailDTO()
                .setAppointmentId(request.getAppointmentId())
                .setPetDetailDefs(petDetailDefs)
                .setTokenStaffId(request.getStaffId());

        appointmentCompositeService.saveOrUpdatePetDetails(saveOrUpdatePetDetailDTO);

        ThreadPool.execute(() -> repeatAppointmentService
                .listRepeatAppointment(request.getAppointmentId(), request.getRepeatAppointmentModifyScope())
                .forEach(repeatAppointment -> {
                    // NOTE：https://moego.atlassian.net/browse/MER-1213
                    var repeatPetDetails = petDetailService.getPetDetailList(
                            repeatAppointment.getId().longValue());
                    var repeatDTO = buildRepeatSaveOrUpdatePetDetailDTO(repeatAppointment, repeatPetDetails, request);

                    appointmentCompositeService.saveOrUpdatePetDetails(repeatDTO);
                }));

        responseObserver.onNext(SaveOrUpdatePetDetailsResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    static SaveOrUpdatePetDetailDTO buildRepeatSaveOrUpdatePetDetailDTO(
            MoeGroomingAppointment repeatAppointment,
            List<MoeGroomingPetDetail> repeatPetDetails,
            SaveOrUpdatePetDetailsRequest request) {

        var petDetailDefs = buildPetDetails(request);
        var repeatPetDetailDefs = buildRepeatPetDetailDefs(petDetailDefs, repeatAppointment, repeatPetDetails);

        return new SaveOrUpdatePetDetailDTO()
                .setAppointmentId(repeatAppointment.getId().longValue())
                .setPetDetailDefs(repeatPetDetailDefs)
                .setTokenStaffId(request.getStaffId())
                .setRepeat(true);
    }

    private static List<PetDetailDef> buildRepeatPetDetailDefs(
            List<PetDetailDef> petDetailDefs,
            MoeGroomingAppointment repeatAppointment,
            List<MoeGroomingPetDetail> repeatPetDetails) {

        var offsetMinutes = calculateOffsetMinutes(petDetailDefs, repeatAppointment, repeatPetDetails);

        return petDetailDefs.stream()
                .map(petDetailDef -> buildNewPetDetailDef(petDetailDef, offsetMinutes))
                .toList();
    }

    private static long calculateOffsetMinutes(
            List<PetDetailDef> petDetailDefs,
            MoeGroomingAppointment repeatAppointment,
            List<MoeGroomingPetDetail> repeatPetDetails) {
        var earliestModifyDateTime = getEarliestModifyDateTime(petDetailDefs);
        if (Objects.isNull(earliestModifyDateTime)) {
            return 0;
        }
        var petIds = petDetailDefs.stream().map(PetDetailDef::getPetId).collect(Collectors.toSet());
        return repeatPetDetails.stream()
                .filter(p -> petIds.contains(p.getPetId().longValue())
                        && !p.getStartDate().isEmpty())
                .min(Comparator.comparing(MoeGroomingPetDetail::getStartDate)
                        .thenComparing(MoeGroomingPetDetail::getStartTime))
                .map(earliestRepeatPetDetail -> {
                    // 替换：存在对应 pet 的情况下，以最早的 repeat pet detail 为准做偏移修改
                    var earliestRepeatDateTime = buildDateTime(
                            earliestRepeatPetDetail.getStartDate(),
                            earliestRepeatPetDetail.getStartTime().intValue());
                    return ChronoUnit.MINUTES.between(earliestModifyDateTime, earliestRepeatDateTime);
                })
                .orElseGet(() -> {
                    // 新增：不存在对应 pet 的情况下，以 repeat appointment 的 end datetime 为准做偏移修改
                    var appointmentEndDateTime = buildDateTime(
                            repeatAppointment.getAppointmentEndDate(), repeatAppointment.getAppointmentEndTime());
                    return ChronoUnit.MINUTES.between(earliestModifyDateTime, appointmentEndDateTime);
                });
    }

    private static LocalDateTime getEarliestModifyDateTime(List<PetDetailDef> petDetailDefs) {
        return petDetailDefs.stream()
                .flatMap(petDetailDef -> Stream.concat(
                        petDetailDef.getAddOnsList().stream()
                                .map(addOn -> {
                                    if (!addOn.hasStartDate() || !addOn.hasStartTime()) {
                                        return null;
                                    }
                                    return buildDateTime(addOn.getStartDate(), addOn.getStartTime());
                                })
                                .filter(Objects::nonNull),
                        petDetailDef.getServicesList().stream()
                                .map(service -> buildDateTime(service.getStartDate(), service.getStartTime()))))
                .min(Comparator.naturalOrder())
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Start date time is empty"));
    }

    private static PetDetailDef buildNewPetDetailDef(PetDetailDef petDetailDef, long offsetMinutes) {
        return PetDetailDef.newBuilder()
                .setPetId(petDetailDef.getPetId())
                .addAllServices(petDetailDef.getServicesList().stream()
                        .map(service -> offsetServiceDateTime(service, offsetMinutes))
                        .toList())
                .addAllAddOns(petDetailDef.getAddOnsList().stream()
                        .map(addOn -> offsetAddOnDateTime(addOn, offsetMinutes))
                        .toList())
                .build();
    }

    private static SelectedServiceDef offsetServiceDateTime(SelectedServiceDef service, long offsetMinutes) {
        var builder = service.toBuilder();

        if (service.hasStartTime()) {
            var newStartDateTime = PetDetailUtil.calculateEndDateAndEndTime(
                    service.getStartDate(), service.getStartTime(), Math.toIntExact(offsetMinutes));
            builder.setStartDate(newStartDateTime.first()).setStartTime(newStartDateTime.second());
        }
        if (service.hasEndDate() && service.hasEndTime()) {
            var newEndDateTime = PetDetailUtil.calculateEndDateAndEndTime(
                    service.getEndDate(), service.getEndTime(), Math.toIntExact(offsetMinutes));
            builder.setEndDate(newEndDateTime.first()).setEndTime(newEndDateTime.second());
        }

        return builder.build();
    }

    private static SelectedAddOnDef offsetAddOnDateTime(SelectedAddOnDef addOn, long offsetMinutes) {
        if (!addOn.hasStartDate() || !addOn.hasStartTime()) {
            return addOn;
        }
        var newStartDateTime = PetDetailUtil.calculateEndDateAndEndTime(
                addOn.getStartDate(), addOn.getStartTime(), Math.toIntExact(offsetMinutes));

        return addOn.toBuilder()
                .setStartDate(newStartDateTime.first())
                .setStartTime(newStartDateTime.second())
                .build();
    }

    @Override
    public void createPetDetailsForExtraOrder(
            CreatePetDetailsForExtraOrderRequest request,
            StreamObserver<CreatePetDetailsForExtraOrderResponse> responseObserver) {
        MoeGroomingAppointment beforeAppointment =
                appointmentService.getAppointment(request.getCompanyId(), request.getAppointmentId());
        List<MoeGroomingPetDetail> beforePetDetails = petDetailService.getPetDetailList(request.getAppointmentId());

        List<Long> newPetDetailIds = appointmentCompositeService.createPetDetails(
                beforeAppointment, beforePetDetails, request.getPetDetail(), request.getExtraOrderId());

        responseObserver.onNext(CreatePetDetailsForExtraOrderResponse.newBuilder()
                .addAllPetDetailIds(newPetDetailIds)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void deletePet(DeletePetRequest request, StreamObserver<DeletePetResponse> responseObserver) {

        MoeGroomingAppointment appointment = appointmentCompositeService.deletePet(
                request.getCompanyId(), request.getAppointmentId(), request.getPetId());

        publisher.publishEvent(new DeleteAppointmentPetDetailEvent(this)
                .setCompanyId(request.getCompanyId())
                .setBusinessId(request.getBusinessId())
                .setCustomerId(appointment.getCustomerId().longValue())
                .setStaffId(request.getStaffId())
                .setAppointmentId(request.getAppointmentId())
                .setPetId(request.getPetId())
                .setRepeatAppointmentModifyScope(request.getRepeatAppointmentModifyScope()));

        responseObserver.onNext(DeletePetResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void deletePetEvaluation(
            DeletePetEvaluationRequest request, StreamObserver<DeletePetEvaluationResponse> responseObserver) {
        var appointment = appointmentCompositeService.deletePetEvaluation(
                request.getCompanyId(), request.getAppointmentId(), request.getEvaluationServiceDetailId());

        ActivityLogRecorder.record(
                appointment.getBusinessId(),
                request.getTokenStaffId(),
                AppointmentAction.EDIT_PET_AND_SERVICES,
                ResourceType.APPOINTMENT,
                appointment.getId(),
                request);

        responseObserver.onNext(DeletePetEvaluationResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void updateUpcomingPetDetails(
            UpdateUpcomingPetDetailsRequest request,
            StreamObserver<UpdateUpcomingPetDetailsResponse> responseObserver) {
        var currentDayAndTime =
                dateTimeService.getCompanyCurrentDayAndTime(GetCompanyCurrentDayAndTimeRequest.newBuilder()
                        .setCompanyId(request.getCompanyId())
                        .build());

        request.getLocationOverrideRuleList().forEach(overrideRule -> {
            List<Integer> appointmentIds = appointmentService.listNotStartedAppointmentWithPetService(
                    request.getCompanyId(),
                    Math.toIntExact(overrideRule.getBusinessId()),
                    List.of(Math.toIntExact(request.getServiceId())),
                    null,
                    currentDayAndTime.getCurrentDate());
            if (CollectionUtils.isEmpty(appointmentIds)) {
                return;
            }
            appointmentIds.forEach(appointmentId -> {
                ServiceOverrideDTO serviceOverride = serviceOverrideConverter
                        .toDTO(overrideRule)
                        .setServiceId(Math.toIntExact(request.getServiceId()));
                appointmentCompositeService.updatePetDetailsWithServiceOverride(
                        request.getCompanyId(), appointmentId.longValue(), serviceOverride);

                publisher.publishEvent(new UpdateUpcomingPetDetailEvent(this)
                        .setCompanyId(request.getCompanyId())
                        .setBusinessId(overrideRule.getBusinessId())
                        .setStaffId(request.getStaffId())
                        .setAppointmentId(appointmentId.longValue())
                        .setServiceOverride(serviceOverride));
            });
        });

        responseObserver.onNext(UpdateUpcomingPetDetailsResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void getPetDetailList(
            GetPetDetailListRequest request, StreamObserver<GetPetDetailListResponse> responseObserver) {
        List<MoeGroomingPetDetail> petDetailList = petDetailService.getPetDetailList(request.getAppointmentIdsList());
        petDetailService.fixServiceType(request.getCompanyId(), petDetailList);
        List<EvaluationServiceDetail> petEvaluationList =
                petEvaluationService.getPetEvaluationList(request.getAppointmentIdsList());
        Map<Integer, PetDetailDateType> dateTypeMap = PetDetailUtil.getDateTypeMap(petDetailList);
        if (request.hasWithActualDates()) {
            petDetailList = PetDetailUtil.getWithActualDatesInfo(petDetailList);
        }

        responseObserver.onNext(GetPetDetailListResponse.newBuilder()
                .addAllPetDetails(PetDetailConverter.INSTANCE.toModel(petDetailList, dateTypeMap))
                .addAllPetEvaluations(petEvaluationService.toPetEvaluationModel(petEvaluationList))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getPetDetail(GetPetDetailRequest request, StreamObserver<GetPetDetailResponse> r) {

        var petDetail = petDetailService.get(request.getId());
        if (petDetail == null) {
            r.onNext(GetPetDetailResponse.getDefaultInstance());
            r.onCompleted();
            return;
        }
        petDetailService.fixServiceType(request.getCompanyId(), List.of(petDetail));

        var appointment = appointmentService.get(petDetail.getGroomingId());
        if (appointment == null || !canAccess(appointment.getCompanyId())) {
            r.onNext(GetPetDetailResponse.getDefaultInstance());
            r.onCompleted();
            return;
        }

        r.onNext(GetPetDetailResponse.newBuilder()
                .setRecord(PetDetailConverter.INSTANCE.toModel(
                        petDetail, PetDetailUtil.getDateTypeMap(List.of(petDetail))))
                .build());
        r.onCompleted();
    }

    @Override
    public void updatePetDetail(UpdatePetDetailRequest request, StreamObserver<UpdatePetDetailResponse> r) {

        var affectedCount = petDetailService.update(PetDetailConverter.INSTANCE.updateRequestToEntity(request));

        r.onNext(UpdatePetDetailResponse.newBuilder()
                .setAffectedCount(affectedCount)
                .build());
        r.onCompleted();
    }

    @Override
    public void deletePetDetailForExtraOrder(
            DeletePetDetailForExtraOrderRequest request, StreamObserver<DeletePetDetailForExtraOrderResponse> r) {
        MoeGroomingPetDetail petDetail = petDetailService.get(request.getPetDetailId());
        if (petDetail == null) {
            r.onNext(DeletePetDetailForExtraOrderResponse.newBuilder()
                    .setResult(false)
                    .build());
            r.onCompleted();
            return;
        }
        var affectedCount = petDetailService.delete(request.getPetDetailId());
        if (affectedCount > 0) {
            // refresh appointment after deleting a pet detail
            appointmentService.refreshAppointmentDateTime(appointmentService.mustGet(petDetail.getGroomingId()));
        }
        r.onNext(DeletePetDetailForExtraOrderResponse.newBuilder()
                .setResult(affectedCount > 0)
                .build());
        r.onCompleted();
    }

    @Override
    public void updateUpcomingAppointments(
            UpdateUpcomingAppointmentsRequest request,
            StreamObserver<UpdateUpcomingAppointmentsResponse> responseObserver) {

        if (request.getBusinessIdsList().isEmpty()) {
            responseObserver.onNext(UpdateUpcomingAppointmentsResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var serviceId = request.getOldService().getServiceId();
        var newService = mustGetService(serviceId);

        var date = mustGetCurrentDateForCompany(newService.getCompanyId());

        for (var businessId : request.getBusinessIdsList()) {
            updateAppointmentsForBusiness(businessId, newService, date);
        }

        responseObserver.onNext(UpdateUpcomingAppointmentsResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private String mustGetCurrentDateForCompany(long companyId) {
        var date = dateTimeService
                .getCompanyCurrentDayAndTime(GetCompanyCurrentDayAndTimeRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getCurrentDate();
        if (!StringUtils.hasText(date)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "current date of company %s is empty", companyId);
        }
        return date;
    }

    private void updateAppointmentsForBusiness(Long businessId, ServiceModel newService, String date) {

        List<Long> appointmentIds =
                listUpcomingAppointmentId(newService.getCompanyId(), businessId, newService.getServiceId(), date);

        for (var appointmentIdList : Lists.partition(appointmentIds, 100)) {

            var appointmentPetDetails = petDetailService.getPetDetailList(appointmentIdList).stream()
                    .collect(Collectors.groupingBy(MoeGroomingPetDetail::getGroomingId));
            appointmentPetDetails.forEach((appointmentId, petDetailList) -> {
                var updates = buildDetailsUpdateWithNewService(businessId, petDetailList, newService);
                var updateTime = Instant.now().getEpochSecond();
                for (var update : updates) {
                    update.setUpdateTime(updateTime);
                    // 每条 petDetail 更新后，都会发送一条更新事件，这里暂不考虑批量更新
                    petDetailService.update(update);
                }

                // 应用 pricing rule
                pricingRuleApplyService.applyPricingRuleByNewService(
                        appointmentId.longValue(), newService.getCompanyId(), businessId, newService);

                var appointment = appointmentService.get(appointmentId);
                if (Objects.isNull(appointment)) {
                    return;
                }
                pricingRuleApplyService.applyPricingRuleForGroomingService(
                        appointmentId.longValue(), newService.getCompanyId(), businessId, appointment.getCustomerId());
            });
        }
    }

    private List<Long> listUpcomingAppointmentId(long companyId, Long businessId, long serviceId, String date) {
        return appointmentService
                .listNotStartedAppointmentWithPetService(
                        companyId, Math.toIntExact(businessId), List.of(Math.toIntExact(serviceId)), null, date)
                .stream()
                .map(Integer::longValue)
                .toList();
    }

    @Nullable
    private static LocationStaffOverrideRule findLocationStaffOverrideRule(
            List<LocationStaffOverrideRule> overrideRuleList, Long businessId) {
        return overrideRuleList.stream()
                .filter(e -> Objects.equals(e.getLocationOverride().getBusinessId(), businessId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 该方法会改变入参 oldPetDetails 的内容
     *
     * @param petDetails 一个预约下所有 petDetails
     * @param newService 最新 service 信息
     * @return 需要更新的 petDetails
     */
    static List<MoeGroomingPetDetail> buildDetailsUpdateWithNewService(
            long businessId, List<MoeGroomingPetDetail> petDetails, ServiceModel newService) {
        Set<Integer> toUpdateIds = new HashSet<>();

        boolean isGroomingOnly = PetDetailUtil.isGroomingOnly(petDetails);
        boolean isAllPetStartAtSameTime = PetDetailUtil.getAllPetsStartAtSameTime(petDetails);

        for (var cur : petDetails) {
            if (cur.getServiceId().longValue() != newService.getServiceId()) {
                continue;
            }
            var newOverride = getServiceOverride(newService, businessId, cur);

            double price = newOverride.price();
            ServicePriceUnit priceUnit = newOverride.priceUnit();
            int duration = newOverride.duration();

            if (!shouldUpdatePetDetail(cur, price, priceUnit, duration)) {
                continue;
            }

            // 1. 偏移后续 pet detail
            int offset = duration - cur.getServiceTime();

            // 2. 获取需要偏移的 pet detail，注意不要放到 3 之后！
            List<MoeGroomingPetDetail> petDetailsFollow = petDetails.stream()
                    .filter(e -> !Objects.equals(e.getId(), cur.getId()))
                    .filter(e -> e.getServiceItemType().intValue() == ServiceItemType.GROOMING_VALUE)
                    .filter(e -> e.getStartTime() > cur.getStartTime())
                    .toList();
            // 非 grooming only 不需要更新其它 pet；
            // grooming only 且所有的 pet 同时开始服务，不需要更新其它 pet;只有串行时，需要更新后续 pet
            if (!isGroomingOnly || isAllPetStartAtSameTime) {
                petDetailsFollow = petDetailsFollow.stream()
                        .filter(e -> Objects.equals(e.getPetId(), cur.getPetId()))
                        .toList();
            }

            // 3. 修改当前 pet detail
            cur.setServicePrice(BigDecimal.valueOf(price));
            cur.setPriceUnit(priceUnit.getNumber());
            cur.setServiceTime(duration);
            cur.setEndTime(cur.getStartTime() + duration);
            cur.setPriceOverrideType(newOverride.priceOverrideType());
            cur.setDurationOverrideType(newOverride.durationOverrideType());
            toUpdateIds.add(cur.getId());

            // 4. 修改后续 pet detail
            for (var follow : petDetailsFollow) {
                follow.setStartTime(follow.getStartTime() + offset);
                follow.setEndTime(follow.getEndTime() + offset);
                toUpdateIds.add(follow.getId());
            }
        }
        return petDetails.stream()
                .filter(k -> toUpdateIds.contains(k.getId()))
                .map(k -> {
                    var updateBean = new MoeGroomingPetDetail();
                    updateBean.setId(k.getId());
                    updateBean.setServicePrice(k.getServicePrice());
                    updateBean.setPriceUnit(k.getPriceUnit());
                    updateBean.setServiceTime(k.getServiceTime());
                    updateBean.setStartTime(k.getStartTime());
                    updateBean.setEndTime(k.getEndTime());
                    updateBean.setPriceOverrideType(k.getPriceOverrideType());
                    updateBean.setDurationOverrideType(k.getDurationOverrideType());
                    return updateBean;
                })
                .toList();
    }

    private static boolean shouldUpdatePetDetail(
            MoeGroomingPetDetail petDetail, double price, ServicePriceUnit priceUnit, int duration) {
        var shouldUpdatePrice =
                price != petDetail.getServicePrice().doubleValue() || priceUnit.getNumber() != petDetail.getPriceUnit();
        var shouldUpdateDuration = !Objects.equals(duration, petDetail.getServiceTime());
        return shouldUpdatePrice || shouldUpdateDuration;
    }

    @Nullable
    private static StaffOverrideRule findStaffOverrideRule(
            @Nullable LocationStaffOverrideRule locationStaffOverrideRule, long staffId) {
        return Optional.ofNullable(locationStaffOverrideRule)
                .map(LocationStaffOverrideRule::getStaffOverrideListList)
                .stream()
                .flatMap(List::stream)
                .filter(e -> Objects.equals(e.getStaffId(), staffId))
                .findFirst()
                .orElse(null);
    }

    private ServiceModel mustGetService(long serviceId) {

        var builder = GetServiceDetailRequest.newBuilder();
        builder.setServiceId(serviceId);
        var serviceOpt = serviceStub.getServiceDetail(builder.build());
        if (!serviceOpt.hasService()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "service not found: " + serviceId);
        }

        return serviceOpt.getService();
    }

    private static boolean canAccess(Long companyId) {
        return isAnonymousRequest() || Objects.equals(getCompanyId(), companyId);
    }

    private static boolean isAnonymousRequest() {
        return getCompanyId() == null;
    }

    private static Long getCompanyId() {
        return AuthContext.get().companyId();
    }

    private static ServiceOverride getServiceOverride(
            ServiceModel service, long businessId, MoeGroomingPetDetail petDetail) {
        double price = service.getPrice();
        ServicePriceUnit priceUnit = service.getPriceUnit();
        int duration = service.getDuration();
        var priceOverrideType = SERVICE_OVERRIDE_TYPE_UNSPECIFIED;
        var durationOverrideType = SERVICE_OVERRIDE_TYPE_UNSPECIFIED;

        // override by location
        var locationStaffOverrideRule =
                findLocationStaffOverrideRule(service.getLocationStaffOverrideListList(), businessId);
        if (locationStaffOverrideRule != null) {
            if (locationStaffOverrideRule.getLocationOverride().hasPrice()) {
                price = locationStaffOverrideRule.getLocationOverride().getPrice();
                priceOverrideType = ServiceOverrideType.LOCATION;
            }
            if (locationStaffOverrideRule.getLocationOverride().hasDuration()) {
                duration = locationStaffOverrideRule.getLocationOverride().getDuration();
                durationOverrideType = ServiceOverrideType.LOCATION;
            }

            // override by staff
            var staffOverrideRule = findStaffOverrideRule(locationStaffOverrideRule, petDetail.getStaffId());
            if (staffOverrideRule != null) {
                if (staffOverrideRule.hasPrice()) {
                    price = staffOverrideRule.getPrice();
                    priceOverrideType = ServiceOverrideType.STAFF;
                }
                if (staffOverrideRule.hasDuration()) {
                    duration = staffOverrideRule.getDuration();
                    durationOverrideType = ServiceOverrideType.STAFF;
                }
            }
        }

        // daycare 的 duration 用 max duration 替代 :)
        if (Objects.equals(petDetail.getServiceItemType(), (byte) ServiceItemType.DAYCARE_VALUE)) {
            var locationOverride = service.getLocationStaffOverrideListList().stream()
                    .map(LocationStaffOverrideRule::getLocationOverride)
                    .filter(locationOverrideRule ->
                            locationOverrideRule.getBusinessId() == businessId && locationOverrideRule.hasMaxDuration())
                    .findFirst()
                    .orElse(null);
            if (locationOverride != null && locationOverride.hasMaxDuration()) {
                duration = locationOverride.getMaxDuration();
                durationOverrideType = ServiceOverrideType.LOCATION;
            } else {
                duration = service.getMaxDuration();
                durationOverrideType = SERVICE_OVERRIDE_TYPE_UNSPECIFIED;
            }
        }

        return new ServiceOverride(price, priceUnit, duration, priceOverrideType, durationOverrideType);
    }

    private record ServiceOverride(
            double price,
            ServicePriceUnit priceUnit,
            int duration,
            ServiceOverrideType priceOverrideType,
            ServiceOverrideType durationOverrideType) {}

    @Override
    public void getLastPetDetail(
            GetLastPetDetailRequest request, StreamObserver<GetLastPetDetailResponse> responseObserver) {
        List<MoeGroomingPetDetail> petDetails = petDetailService.getPetLastPetDetail(
                request.getCompanyId(), request.getCustomerIdList(), request.getPetIdList(), request.getFilter());

        if (CollectionUtils.isEmpty(petDetails)) {
            responseObserver.onNext(GetLastPetDetailResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        petDetailService.fixServiceType(request.getCompanyId(), petDetails);
        Map<Integer, PetDetailDateType> dateTypeMap = PetDetailUtil.getDateTypeMap(petDetails);

        responseObserver.onNext(GetLastPetDetailResponse.newBuilder()
                .addAllPetDetails(petDetails.stream()
                        .map(e -> PetDetailConverter.INSTANCE.toModel(e, dateTypeMap))
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    private static Interval buildIntersection(Interval startTimeRange, Interval endTimeRange) {
        long start = Math.max(
                startTimeRange.getStartTime().getSeconds(),
                endTimeRange.getStartTime().getSeconds());

        long end = Math.min(
                startTimeRange.getEndTime().getSeconds(),
                endTimeRange.getEndTime().getSeconds());

        if (start > end) {
            return Interval.getDefaultInstance();
        }

        return Interval.newBuilder()
                .setStartTime(Timestamp.newBuilder().setSeconds(start).build())
                .setEndTime(Timestamp.newBuilder().setSeconds(end).build())
                .build();
    }

    private static ListAppointmentsRequest.Filter buildGroomingFilter(Interval startTimeRange) {
        return ListAppointmentsRequest.Filter.newBuilder()
                .setStartTimeRange(startTimeRange)
                .setIncludeBlock(true)
                .setIsWaitingList(false)
                .addAllStatus(AppointmentStatusSet.ACTIVE_STATUS_SET)
                .build();
    }

    private static ListAppointmentsRequest.Filter buildBDFilter(GetStaffPetDetailsRequest request) {
        return ListAppointmentsRequest.Filter.newBuilder()
                .setStartTimeRange(request.getStartTimeRange())
                .setEndTimeRange(request.getEndTimeRange())
                .setIncludeBlock(true)
                .setIsWaitingList(false)
                .addAllStatus(AppointmentStatusSet.ACTIVE_STATUS_SET)
                .build();
    }

    /**
     * 非 BD 降级到只查 appointment 的 start_date 和 start_time
     *
     * @param companyId company id
     * @return true: 只查开始时间
     */
    private boolean onlyQueryStartTime(long companyId) {
        return !metadataRemoteService.isAllowBoardingAndDaycare(companyId);
    }

    @Override
    public void getStaffPetDetails(
            GetStaffPetDetailsRequest request, StreamObserver<GetStaffPetDetailsResponse> response) {
        ListAppointmentsRequest.Filter filter;
        if (onlyQueryStartTime(request.getCompanyId())) {
            var intersection = buildIntersection(request.getStartTimeRange(), request.getEndTimeRange());
            filter = buildGroomingFilter(intersection);
        } else {
            filter = buildBDFilter(request);
        }
        // 根据businessId, start_date和end_date查询appointment
        Map<Integer, MoeGroomingAppointment> appointmentsMap = appointmentService
                .listAppointments(
                        request.getCompanyId(),
                        List.of(request.getBusinessId()),
                        filter,
                        List.of(
                                OrderBy.newBuilder()
                                        .setFieldName("appointmentDate")
                                        .setAsc(true)
                                        .build(),
                                OrderBy.newBuilder()
                                        .setFieldName("appointmentStartTime")
                                        .setAsc(true)
                                        .build()),
                        PageConverter.INSTANCE.toPageInfo(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(3000)
                                .build()),
                        null)
                .first()
                .stream()
                .collect(Collectors.toMap(MoeGroomingAppointment::getId, e -> e));

        List<Long> appointmentIds =
                appointmentsMap.keySet().stream().map(Integer::longValue).toList();
        if (CollectionUtils.isEmpty(appointmentIds)) {
            response.onNext(GetStaffPetDetailsResponse.getDefaultInstance());
            response.onCompleted();
            return;
        }

        // 根据apptIds & dateRange 查询pet_detail表
        Map<Integer, MoeGroomingPetDetail> petDetailMap = petDetailService.getPetDetailList(appointmentIds).stream()
                .collect(Collectors.toMap(MoeGroomingPetDetail::getId, e -> e));
        List<Integer> petDetailIds = petDetailMap.keySet().stream().toList();

        // 内存过滤 处理数据
        GetStaffPetDetailsResponse.Builder builder = GetStaffPetDetailsResponse.newBuilder();
        List<StaffPetDetail> staffPetDetails = new ArrayList<>();

        // 处理 petDetail
        petDetailMap.values().stream()
                .filter(petDetail -> request.getStaffIdsList()
                                .contains(petDetail.getStaffId().longValue())
                        && !petDetail.getEnableOperation())
                .filter(petDetail -> appointmentsMap.containsKey(petDetail.getGroomingId()))
                .forEach(petDetail -> {
                    var appointment = appointmentsMap.get(petDetail.getGroomingId());
                    staffPetDetails.add(convertStaffPetDetail(petDetail, appointment));
                });

        // 处理 serviceOperation
        serviceOperationService.listServiceOperationByPetDetailIds(petDetailIds).stream()
                .filter(op -> request.getStaffIdsList().contains(op.getStaffId().longValue()))
                .filter(op -> appointmentsMap.containsKey(op.getGroomingId())
                        && petDetailMap.containsKey(op.getGroomingServiceId()))
                .forEach(op -> {
                    var appointment = appointmentsMap.get(op.getGroomingId());
                    var petDetail = petDetailMap.get(op.getGroomingServiceId());
                    staffPetDetails.add(convertStaffPetDetail(op, appointment, petDetail));
                });
        // 处理 evaluationServiceDetail
        evaluationServiceDetailService.getPetEvaluationList(appointmentIds).stream()
                .filter(e -> request.getStaffIdsList().contains(e.getStaffId()))
                .filter(e -> appointmentsMap.containsKey(e.getAppointmentId().intValue()))
                .forEach(e -> {
                    var appointment = appointmentsMap.get(e.getAppointmentId().intValue());
                    staffPetDetails.add(convertStaffPetDetail(e, appointment));
                });

        // 按 serviceDate 和 startTime 升序排序
        staffPetDetails.sort(
                Comparator.comparing(StaffPetDetail::getStartDate).thenComparing(StaffPetDetail::getStartTime));

        response.onNext(builder.addAllStaffPetDetails(staffPetDetails).build());
        response.onCompleted();
    }

    private StaffPetDetail convertStaffPetDetail(MoeGroomingPetDetail petDetail, MoeGroomingAppointment appointment) {
        return StaffPetDetail.newBuilder()
                .setId(petDetail.getId())
                .setAppointmentId(petDetail.getGroomingId())
                .setPetId(petDetail.getPetId())
                .setStaffId(petDetail.getStaffId())
                .setServiceId(petDetail.getServiceId())
                .setServiceTime(petDetail.getServiceTime())
                .setStartTime(petDetail.getStartTime().intValue())
                .setEndTime(petDetail.getStartTime().intValue() + petDetail.getServiceTime())
                .setCustomerId(appointment.getCustomerId())
                .setStartDate(petDetail.getStartDate())
                .setAppointmentDate(appointment.getAppointmentDate())
                .setServiceItemTypeValue(petDetail.getServiceItemType())
                .setIsBlock(appointment.getIsBlock().equals(CommonConstant.ENABLE))
                .setServicePrice(petDetail.getServicePrice().doubleValue())
                .build();
    }

    private StaffPetDetail convertStaffPetDetail(
            EvaluationServiceDetail evaluationServiceDetail, MoeGroomingAppointment appointment) {
        return StaffPetDetail.newBuilder()
                .setId(evaluationServiceDetail.getId().intValue())
                .setAppointmentId(evaluationServiceDetail.getAppointmentId().intValue())
                .setPetId(evaluationServiceDetail.getPetId())
                .setStaffId(evaluationServiceDetail.getStaffId())
                .setServiceId(evaluationServiceDetail.getServiceId())
                .setServiceTime(evaluationServiceDetail.getServiceTime())
                .setStartTime(evaluationServiceDetail.getStartTime())
                .setEndTime(evaluationServiceDetail.getStartTime() + evaluationServiceDetail.getServiceTime())
                .setCustomerId(appointment.getCustomerId())
                .setStartDate(evaluationServiceDetail.getStartDate().toString())
                .setAppointmentDate(appointment.getAppointmentDate())
                .setServiceItemType(ServiceItemType.EVALUATION)
                .setIsBlock(appointment.getIsBlock().equals(CommonConstant.ENABLE))
                .setServicePrice(evaluationServiceDetail.getServicePrice().doubleValue())
                .build();
    }

    private StaffPetDetail convertStaffPetDetail(
            MoeGroomingServiceOperation serviceOperation,
            MoeGroomingAppointment appointment,
            MoeGroomingPetDetail petDetail) {
        return StaffPetDetail.newBuilder()
                .setId(serviceOperation.getGroomingServiceId())
                .setAppointmentId(serviceOperation.getGroomingId())
                .setPetId(petDetail.getPetId())
                .setStaffId(serviceOperation.getStaffId())
                .setServiceId(petDetail.getServiceId())
                .setServiceTime(serviceOperation.getDuration())
                .setStartTime(serviceOperation.getStartTime())
                .setEndTime(serviceOperation.getStartTime() + serviceOperation.getDuration())
                .setCustomerId(appointment.getCustomerId())
                .setStartDate(petDetail.getStartDate())
                .setAppointmentDate(appointment.getAppointmentDate())
                .setServiceItemTypeValue(petDetail.getServiceItemType())
                .setIsBlock(appointment.getIsBlock().equals(CommonConstant.ENABLE))
                .setServicePrice(serviceOperation.getPrice().doubleValue())
                .build();
    }
}
