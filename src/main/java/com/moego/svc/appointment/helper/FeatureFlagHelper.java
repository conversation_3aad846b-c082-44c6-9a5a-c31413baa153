package com.moego.svc.appointment.helper;

import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/5/11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FeatureFlagHelper {

    private final FeatureFlagApi featureFlagApi;

    public boolean isNewOrderFlow(long companyId) {
        try {
            return featureFlagApi.isOn(
                    FeatureFlags.NEW_ORDER_FLOW,
                    FeatureFlagContext.builder().company(companyId).build());
        } catch (Exception e) {
            log.error("Failed to get new order flow feature flag: {}", companyId, e);
            return false;
        }
    }

    public boolean isCatsInTheCityCustomizedFeedback(long companyId) {
        try {
            return featureFlagApi.isOn(
                    FeatureFlags.CATS_IN_THE_CITY_CUSTOMIZED_FEEDBACK,
                    FeatureFlagContext.builder().company(companyId).build());
        } catch (Exception e) {
            log.error("Failed to get cats in the city customized feedback feature flag: {}", companyId, e);
            return false;
        }
    }

    public boolean enableVaryPricingByZone(Long businessId) {
        try {
            return featureFlagApi.isOn(
                    FeatureFlags.ENABLE_VARY_PRICING_BY_ZONE,
                    FeatureFlagContext.builder().business(businessId).build());
        } catch (Exception e) {
            log.error("Failed to get vary pricing by zone feature flag: {}", businessId, e);
            return false;
        }
    }
}
