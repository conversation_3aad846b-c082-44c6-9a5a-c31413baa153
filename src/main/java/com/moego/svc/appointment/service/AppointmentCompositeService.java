package com.moego.svc.appointment.service;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static java.lang.Math.toIntExact;

import com.moego.common.enums.ServiceEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.appointment.v1.AppointmentNoteType;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.service.appointment.v1.GetLastPetDetailRequest;
import com.moego.idl.service.map.v1.AutocompleteAddressRequest;
import com.moego.idl.service.map.v1.GetAddressRequest;
import com.moego.idl.service.map.v1.MapServiceGrpc;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.svc.appointment.client.BrandedAppClient;
import com.moego.svc.appointment.client.CustomerClient;
import com.moego.svc.appointment.converter.AppointmentTrackingConverter;
import com.moego.svc.appointment.converter.PetDetailConverter;
import com.moego.svc.appointment.domain.AppointmentTracking;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingNote;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.MoeGroomingServiceOperation;
import com.moego.svc.appointment.dto.AppointmentPetServiceDTO;
import com.moego.svc.appointment.dto.CancelAppointmentDTO;
import com.moego.svc.appointment.dto.CreateAppointmentResultDTO;
import com.moego.svc.appointment.dto.PetDetailDTO;
import com.moego.svc.appointment.dto.PetServiceOverrideDTO;
import com.moego.svc.appointment.dto.SaveOrUpdatePetDetailDTO;
import com.moego.svc.appointment.dto.ServiceOverrideDTO;
import com.moego.svc.appointment.helper.FeatureFlagHelper;
import com.moego.svc.appointment.listener.event.CancelAppointmentEvent;
import com.moego.svc.appointment.listener.event.SaveOrUpdatePetDetailEvent;
import com.moego.svc.appointment.service.params.UpdateExtraOrderByPetDetailParams;
import com.moego.svc.appointment.service.remote.ActiveMQService;
import com.moego.svc.appointment.service.remote.CompanyRemoteService;
import com.moego.svc.appointment.service.remote.LodgingRemoteService;
import com.moego.svc.appointment.service.remote.OfferingRemoteService;
import com.moego.svc.appointment.service.remote.OrderRemoteService;
import com.moego.svc.appointment.utils.Pair;
import com.moego.svc.appointment.utils.PetDetailUtil;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionOperations;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/3/5
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppointmentCompositeService {

    private final AppointmentExtraInfoService extraInfoService;
    private final AppointmentServiceProxy appointmentService;
    private final PetDetailServiceProxy petDetailService;
    private final ServiceOperationService serviceOperationService;
    private final EvaluationServiceDetailService evaluationService;
    private final OrderRemoteService orderRemoteService;
    private final NoteService noteService;
    private final ActiveMQService mqService;
    private final PricingRuleRecordApplyService pricingRuleApplyService;
    private final CompanyRemoteService companyRemoteService;
    private final OfferingRemoteService offeringRemoteService;
    private final LodgingRemoteService lodgingRemoteService;
    private final AppointmentTrackingService appointmentTrackingService;
    private final CustomerClient customerClient;
    private final BrandedAppClient brandedAppClient;

    private final ApplicationEventPublisher publisher;
    private final MapServiceGrpc.MapServiceBlockingStub mapService;

    private final TransactionOperations transaction;
    private final FeatureFlagHelper featureFlagHelper;
    private final ServiceChargeDetailService serviceChargeDetailService;

    /**
     * 创建 appointment, pet details, order
     * optional: service operations, feeding schedules, medication schedules
     *
     * @param appointment   appointment model
     * @param detailDTOList selected pet and service list
     * @return created appointment
     */
    @Transactional
    public CreateAppointmentResultDTO createAppointment(
            MoeGroomingAppointment appointment,
            List<PetDetailDTO> detailDTOList,
            List<EvaluationServiceDetail> evaluationList) {
        // 1. 创建 appointment
        var appointmentId = appointmentService.insertSelective(appointment);
        var isNewOrderFlow = featureFlagHelper.isNewOrderFlow(appointment.getCompanyId());
        if (isNewOrderFlow) {
            extraInfoService.insertNewOrderFlag(appointment.getId());
        }

        // 2. 创建 pet details, service operations, and feeding & medication schedules
        petDetailService.savePetDetails(appointment, detailDTOList);

        // 3. 创建 evaluation
        evaluationService.saveEvaluationDetails(appointment, evaluationList);

        // 4. Auto apply service charge
        if (isNewOrderFlow) {
            serviceChargeDetailService.autoApplyServiceCharge(appointment);
        }

        // 4. 保存 customer address
        ThreadPool.execute(() -> saveCustomerAddress(appointment, appointmentId, appointment.getCustomerId()));

        return new CreateAppointmentResultDTO(true, appointment.getId());
    }

    /**
     * 新增或更新 pet details 并刷新 appointment 时间，更新 order
     * 1. 如果指定 pet 不存在直接新增
     * 2. 如果指定 pet 存在则先删除再新增
     *
     * @param dto appointment id, pet detail definition, token staff id, is repeat
     * @return updated appointment
     */
    public MoeGroomingAppointment saveOrUpdatePetDetails(SaveOrUpdatePetDetailDTO dto) {
        var beforeAppointment = appointmentService.mustGet(dto.getAppointmentId());
        var beforePetDetails = petDetailService.getPetDetailList(dto.getAppointmentId());
        var petDetailDefs = dto.getPetDetailDefs();
        var beforeStaffIds = PetDetailUtil.getStaffIdList(beforePetDetails);

        var updateAppointment = transaction.execute(status -> {
            // 1. 更新 pet detail
            petDetailService.upsertAllInOnePetDetail(beforeAppointment, beforePetDetails, petDetailDefs);
            // 2. grooming only 偏移当前 pet 后续的 pet detail 和 service operations 的 start time
            if (PetDetailUtil.isGroomingOnly(beforePetDetails)) {
                offsetWhenModifyPetDetails(beforePetDetails, petDetailDefs);
            }
            // 3. 更新 pet evaluation detail
            evaluationService.upsertPetEvaluation(beforeAppointment, petDetailDefs);
            // 4. 刷新 appointment 时间
            appointmentService.refreshAppointmentDateTime(beforeAppointment);
            // 5. 应用 pricing rule
            pricingRuleApplyService.applyPricingRule(
                    beforeAppointment.getId().longValue(),
                    beforeAppointment.getCompanyId(),
                    beforeAppointment.getBusinessId().longValue(),
                    petDetailDefs);
            // 6. 更新 order
            var afterAppointment = appointmentService.getAppointment(
                    beforeAppointment.getCompanyId(), beforeAppointment.getId().longValue());
            orderRemoteService.updateOrder(afterAppointment);

            return afterAppointment;
        });
        // 7. Publish event
        publisher.publishEvent(new SaveOrUpdatePetDetailEvent(this)
                .setCompanyId(beforeAppointment.getCompanyId())
                .setBusinessId(beforeAppointment.getBusinessId().longValue())
                .setCustomerId(beforeAppointment.getCustomerId().longValue())
                .setAppointmentId(beforeAppointment.getId().longValue())
                .setPetDetailDefs(petDetailDefs)
                .setBeforeAppointmentDateTime(PetDetailUtil.buildDateTime(
                        beforeAppointment.getAppointmentDate(), beforeAppointment.getAppointmentStartTime()))
                .setBeforeStaffIds(beforeStaffIds)
                .setTokenStaffId(toIntExact(dto.getTokenStaffId())));
        return updateAppointment;
    }

    /**
     * 新增pet details 并刷新 appointment 时间
     *
     * @param appointment  appointment
     * @param petDetails   selected pet and service list
     * @param petDetailDef updated pet detail definition
     */
    public List<Long> createPetDetails(
            MoeGroomingAppointment appointment,
            List<MoeGroomingPetDetail> petDetails,
            PetDetailDef petDetailDef,
            Long extraOrderId) {
        // 1. 新增 pet detail
        List<Long> petDetailIds = petDetailService.insertAllInOnePetDetail(appointment, petDetails, petDetailDef);
        // 2. grooming only 偏移当前 pet 后续的 pet detail 和 service operations 的 start time
        if (PetDetailUtil.isGroomingOnly(petDetails)) {
            offsetWhenModifyPetDetails(petDetails, List.of(petDetailDef));
        }
        // 3. 更新 pet evaluation detail (extra order暂时不需要)
        // 4. 刷新 appointment 时间
        appointmentService.refreshAppointmentDateTime(appointment);
        // 5. 同步到 extra order 返回 新增 pet detail ids
        orderRemoteService.updateExtraOrder(UpdateExtraOrderByPetDetailParams.builder() // add multi pet details
                .extraOrderId(extraOrderId)
                .appointment(appointment)
                .addedPetDetailIds(petDetailIds)
                .build());
        return petDetailIds;
    }

    /**
     * 修改某只 pet 的 service 时
     * offsetMinutes = pet 修改后最大的 service end time - pet 修改前最大的 service end time (可正可负)
     * offsetStartTime = pet 修改前最大的 service end time
     *
     * @param petDetails    全量的 pet details
     * @param petDetailDefs pet 修改的 pet details
     */
    private void offsetWhenModifyPetDetails(List<MoeGroomingPetDetail> petDetails, List<PetDetailDef> petDetailDefs) {
        var petIds = petDetailDefs.stream().map(PetDetailDef::getPetId).collect(Collectors.toSet());
        var editPetDetails = petDetails.stream()
                .filter(petDetail -> petIds.contains(petDetail.getPetId().longValue()))
                .toList();
        // 新增 pet 时无需偏移
        if (CollectionUtils.isEmpty(editPetDetails)) {
            return;
        }

        // all pets start at same time 时无需偏移
        var allPetsStartAtSameTime = PetDetailUtil.getAllPetsStartAtSameTime(petDetails);
        if (allPetsStartAtSameTime) {
            return;
        }

        // 偏移前的最大结束时间
        var originMaxEndDateTime = editPetDetails.stream()
                .map(petDetail -> PetDetailUtil.buildDateTime(
                        petDetail.getEndDate(), petDetail.getEndTime().intValue()))
                .max(Comparator.naturalOrder())
                .get();
        // 偏移后的最大结束时间
        var newMaxEndDateTime = petDetailDefs.stream()
                .flatMap(petDetailDef -> Stream.concat(
                        petDetailDef.getServicesList().stream(),
                        petDetailDef.getAddOnsList().stream().map(PetDetailConverter.INSTANCE::addOnToService)))
                .filter(def -> def.hasStartTime() && def.hasServiceTime())
                .map(def -> PetDetailUtil.buildDateTime(def.getStartDate(), def.getStartTime() + def.getServiceTime()))
                .max(Comparator.naturalOrder())
                .orElse(null);

        if (newMaxEndDateTime == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Invalid pet details");
        }

        long offsetMinutes = PetDetailUtil.calculateOffsetMinute(originMaxEndDateTime, newMaxEndDateTime);

        petDetailService.offsetAfterPetDetails(petDetails, originMaxEndDateTime, offsetMinutes);
    }

    private MoeGroomingAppointment updateAppointmentAndOrder(MoeGroomingAppointment beforeAppointment) {
        // 2. 刷新 appointment 时间
        appointmentService.refreshAppointmentDateTime(beforeAppointment);
        // 3. 应用 pricing rule
        pricingRuleApplyService.applyPricingRule(
                beforeAppointment.getId().longValue(),
                beforeAppointment.getCompanyId(),
                beforeAppointment.getBusinessId().longValue());
        // 4. 更新 order
        MoeGroomingAppointment appointment = appointmentService.getAppointment(
                beforeAppointment.getCompanyId(), beforeAppointment.getId().longValue());
        orderRemoteService.updateOrder(appointment);
        return appointment;
    }

    private MoeGroomingAppointment updateAppointmentAndOrderWithGroomingPricingRule(
            MoeGroomingAppointment beforeAppointment) {
        // 2. 刷新 appointment 时间
        appointmentService.refreshAppointmentDateTime(beforeAppointment);
        // 3. 应用 pricing rule
        pricingRuleApplyService.applyPricingRule(
                beforeAppointment.getId().longValue(),
                beforeAppointment.getCompanyId(),
                beforeAppointment.getBusinessId().longValue());
        pricingRuleApplyService.applyPricingRuleForGroomingService(
                beforeAppointment.getId().longValue(),
                beforeAppointment.getCompanyId(),
                beforeAppointment.getBusinessId().longValue(),
                beforeAppointment.getCustomerId());
        // 4. 更新 order
        MoeGroomingAppointment appointment = appointmentService.getAppointment(
                beforeAppointment.getCompanyId(), beforeAppointment.getId().longValue());
        orderRemoteService.updateOrder(appointment);
        return appointment;
    }

    /**
     * 删除 pet 下的所有 pet details 并刷新 appointment 时间，更新 order
     *
     * @param companyId     company id
     * @param appointmentId appointment id
     * @param petId         pet id
     * @return updated appointment
     */
    public MoeGroomingAppointment deletePet(Long companyId, Long appointmentId, Long petId) {
        MoeGroomingAppointment beforeAppointment = appointmentService.getAppointment(companyId, appointmentId);
        List<MoeGroomingPetDetail> petDetails = petDetailService.getPetDetailList(appointmentId);
        List<EvaluationServiceDetail> petEvaluations = evaluationService.getPetEvaluationList(appointmentId);

        // bdeg 四种 type，可能有多个 petId 在不同的结构内，聚合在一起
        Set<Long> allPetIds = new HashSet<>(getPetIdsByGroomingPetDetail(petDetails));
        allPetIds.addAll(getPetIdsByEvaluationServiceDetail(petEvaluations));
        // 不能是最后一个 pet
        if (allPetIds.size() == 1 && allPetIds.contains(petId)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Can't delete the only pet");
        }
        // 1. 删除 pet detail
        petDetailService.deletePetDetails(companyId, appointmentId, List.of(petId));
        // 2. 删除 evaluation
        evaluationService.deleteByPetIds(appointmentId, List.of(petId));

        if (!CollectionUtils.isEmpty(petDetails) && PetDetailUtil.isGroomingOnly(petDetails)) {
            offsetWhenDeletePetDetails(petDetails, petId);
        }
        return updateAppointmentAndOrder(beforeAppointment);
    }

    /**
     * 删除 pet 下的所有 pet details 并刷新 appointment 时间，更新 order
     *
     * @param companyId                    companyId
     * @param appointmentId                apptId
     * @param petEvaluationServiceDetailId pet evaluation service detail id
     * @return
     */
    public MoeGroomingAppointment deletePetEvaluation(
            Long companyId, Long appointmentId, Long petEvaluationServiceDetailId) {
        MoeGroomingAppointment beforeAppointment = appointmentService.getAppointment(companyId, appointmentId);
        // 查询 petDetail && evaluation service detail
        List<MoeGroomingPetDetail> petDetails = petDetailService.getPetDetailList(appointmentId);
        List<EvaluationServiceDetail> petEvaluations = evaluationService.getPetEvaluationList(appointmentId);

        var deletedPetEvaluation = petEvaluations.stream()
                .filter(petEvaluation -> Objects.equals(petEvaluation.getId(), petEvaluationServiceDetailId))
                .findFirst()
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Pet evaluation not found"));

        // petDetail 为空的情况下，pet evaluation 不能是最后一个
        if (CollectionUtils.isEmpty(petDetails) && petEvaluations.size() == 1) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Can't delete the only pet");
        }

        // 删除 evaluation
        evaluationService.deleteById(appointmentId, deletedPetEvaluation.getId());

        return updateAppointmentAndOrder(beforeAppointment);
    }

    private static List<Long> getPetIdsByGroomingPetDetail(List<MoeGroomingPetDetail> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return List.of();
        }
        return petDetails.stream()
                .map(petDetail -> petDetail.getPetId().longValue())
                .filter(petId -> petId != 0L)
                .distinct()
                .toList();
    }

    private static List<Long> getPetIdsByEvaluationServiceDetail(List<EvaluationServiceDetail> petEvaluations) {
        if (CollectionUtils.isEmpty(petEvaluations)) {
            return List.of();
        }
        return petEvaluations.stream()
                .map(EvaluationServiceDetail::getPetId)
                .filter(petId -> petId != 0)
                .distinct()
                .toList();
    }

    /**
     * 删除某只 pet 的 service 时
     * offsetMinutes = pet 删除前最小的 service start time - pet 删除前最大的 service end time (负值)
     * offsetStartTime = pet 删除前最大的 service end time
     */
    private void offsetWhenDeletePetDetails(List<MoeGroomingPetDetail> petDetails, Long petId) {
        List<MoeGroomingPetDetail> deletePetDetails = petDetails.stream()
                .filter(petDetail -> Objects.equals(petDetail.getPetId(), petId.intValue()))
                .toList();
        // Long originMaxEndTime = deletePetDetails.stream()
        //         .map(MoeGroomingPetDetail::getEndTime)
        //         .max(Comparator.naturalOrder())
        //         .orElse(0L);
        // Long originMinStartTime = deletePetDetails.stream()
        //         .map(MoeGroomingPetDetail::getStartTime)
        //         .min(Comparator.naturalOrder())
        //         .orElse(0L);
        // long offsetMinutes = originMinStartTime - originMaxEndTime;
        // petDetailService.offsetAfterPetDetails(petDetails, originMaxEndTime, offsetMinutes);

        LocalDateTime originMaxEndDateTime = deletePetDetails.stream()
                .map(petDetail -> PetDetailUtil.buildDateTime(
                        petDetail.getEndDate(), petDetail.getEndTime().intValue()))
                .max(Comparator.naturalOrder())
                .orElse(null);
        LocalDateTime originMinStartDateTime = deletePetDetails.stream()
                .map(petDetail -> PetDetailUtil.buildDateTime(
                        petDetail.getStartDate(), petDetail.getStartTime().intValue()))
                .max(Comparator.naturalOrder())
                .orElse(null);

        if (originMaxEndDateTime == null || originMinStartDateTime == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Invalid pet details");
        }

        long offsetMinutes = PetDetailUtil.calculateOffsetMinute(originMaxEndDateTime, originMinStartDateTime);

        petDetailService.offsetAfterPetDetails(petDetails, originMaxEndDateTime, offsetMinutes);
    }

    /**
     * 将 service override 信息更新到对应 service 的 pet details 并刷新 appointment 时间，更新 order
     *
     * @param companyId       company id
     * @param appointmentId   appointment id
     * @param serviceOverride saved price and saved duration
     * @return updated appointment
     */
    public MoeGroomingAppointment updatePetDetailsWithServiceOverride(
            Long companyId, Long appointmentId, ServiceOverrideDTO serviceOverride) {
        MoeGroomingAppointment beforeAppointment = appointmentService.getAppointment(companyId, appointmentId);
        petDetailService.updatePetDetailsWithServiceOverride(appointmentId, serviceOverride);
        return updateAppointmentAndOrder(beforeAppointment);
    }

    /**
     * 将 service override 信息更新到对应 pet 的 pet details 并刷新 appointment 时间，更新 order
     *
     * @param companyId          company id
     * @param appointmentId      appointment id
     * @param petServiceOverride pet's saved price and saved duration
     * @return updated appointment
     */
    public MoeGroomingAppointment updatePetDetailsWithPetServiceOverride(
            Long companyId, Long appointmentId, PetServiceOverrideDTO petServiceOverride) {
        MoeGroomingAppointment beforeAppointment = appointmentService.getAppointment(companyId, appointmentId);
        petDetailService.updatePetDetailsWithPetServiceOverride(appointmentId, petServiceOverride);
        return updateAppointmentAndOrder(beforeAppointment);
    }

    /**
     * 更新到对应 service 的 pet details，更新 order
     *
     * @param companyId     company id
     * @param appointmentId appointment id
     * @return updated appointment
     */
    public MoeGroomingAppointment updatePetDetails(Long companyId, Long appointmentId) {
        MoeGroomingAppointment beforeAppointment = appointmentService.getAppointment(companyId, appointmentId);
        return updateAppointmentAndOrderWithGroomingPricingRule(beforeAppointment);
    }

    @Transactional
    public MoeGroomingAppointment createBlock(MoeGroomingAppointment appointment, MoeGroomingPetDetail detail) {
        // 1. 创建 appointment
        appointmentService.insertSelective(appointment);

        // 2. 创建 pet details
        detail.setGroomingId(appointment.getId());
        petDetailService.insert(detail);
        return appointment;
    }

    /**
     * 更新 pet details, service operations 并刷新 appointment 时间
     *
     * @param petDetails        update pet details
     * @param serviceOperations update service operations
     */
    @Transactional
    public void updateServiceDetails(
            List<MoeGroomingPetDetail> petDetails, List<MoeGroomingServiceOperation> serviceOperations) {
        Optional.ofNullable(petDetails).ifPresent(petDetailService::updatePetDetailById);
        Optional.ofNullable(serviceOperations).ifPresent(serviceOperationService::updateOperationById);
    }

    public void cancelAppointment(CancelAppointmentDTO dto) {
        // 1. Update appointment status
        var cancelAppointment = buildCancelAppointment(dto);
        appointmentService.update(cancelAppointment);
        // 2. Create cancel reason note
        if (dto.getCancelReason() != null) {
            var cancelReason = buildCancelReason(dto);
            noteService.insert(cancelReason);
        }
        // 3. Update order status
        orderRemoteService.cancelOrderByAppointment(dto.getAppointmentId());
        // 4. Release pre-auth
        var appointment = appointmentService.mustGet(dto.getAppointmentId());
        mqService.publishAppointmentCancelEvent(appointment);
        // 5. Publish cancel appointment event
        publisher.publishEvent(new CancelAppointmentEvent(this)
                .setCompanyId(appointment.getCompanyId())
                .setBusinessId(appointment.getBusinessId().longValue())
                .setAppointmentId(dto.getAppointmentId())
                .setCustomerId(appointment.getCustomerId().longValue())
                .setCancelByType(dto.getCancelByType())
                .setCancelBy(dto.getCancelBy())
                .setCancelReason(dto.getCancelReason())
                .setNoShow(dto.getNoShow())
                .setBookingRequest(isBookingRequest(appointment))
                .setRepeatEvent(dto.isRepeat())
                .setAppointment(appointment));
    }

    private boolean isBookingRequest(MoeGroomingAppointment appointment) {
        return Objects.equals(appointment.getBookOnlineStatus(), ServiceEnum.OB_NOT_CONFIRM);
    }

    private MoeGroomingAppointment buildCancelAppointment(CancelAppointmentDTO dto) {
        MoeGroomingAppointment update = new MoeGroomingAppointment();
        update.setId(toIntExact(dto.getAppointmentId()));
        update.setStatus((byte) AppointmentStatus.CANCELED_VALUE);
        update.setBookOnlineStatus(ServiceEnum.OB_DEFAULT_NORMAL);
        update.setIsWaitingList(ServiceEnum.IS_WAITING_LIST_FALSE);
        update.setNoShow((byte) dto.getNoShow().getNumber());
        update.setCancelBy(toIntExact(dto.getCancelBy()));
        update.setCancelByType((byte) dto.getCancelByType().getNumber());
        update.setUpdateTime(CommonUtil.get10Timestamp());
        update.setCanceledTime(CommonUtil.get10Timestamp());
        return update;
    }

    private MoeGroomingNote buildCancelReason(CancelAppointmentDTO dto) {
        MoeGroomingNote note = new MoeGroomingNote();
        note.setGroomingId(toIntExact(dto.getAppointmentId()));
        note.setType((byte) AppointmentNoteType.CANCEL_VALUE);
        note.setNote(dto.getCancelReason());
        return note;
    }

    @Transactional
    public void batchAddServiceAndCheckIn(
            long companyId, long businessId, Set<AppointmentPetServiceDTO> appointmentPetServices) {
        // 以当前时间开始
        var timezoneName = companyRemoteService.getTimezoneName(companyId);
        var now = LocalDateTime.now(ZoneId.of(timezoneName));
        var startTime = now.getHour() * 60 + now.getMinute();

        var petServiceIdsMap = appointmentPetServices.stream()
                .collect(Collectors.groupingBy(
                        AppointmentPetServiceDTO::getPetId,
                        Collectors.mapping(AppointmentPetServiceDTO::getServiceId, Collectors.toList())));
        var petIdToServiceMap = offeringRemoteService.listService(companyId, businessId, petServiceIdsMap);

        // get last lodging unit id
        Set<Long> petIds = petIdToServiceMap.keySet();
        List<Long> customerIds = appointmentPetServices.stream()
                .map(AppointmentPetServiceDTO::getCustomerId)
                .distinct()
                .toList();
        List<Long> serviceIds = petIdToServiceMap.values().stream()
                .map(Map::keySet)
                .flatMap(Set::stream)
                .distinct()
                .toList();
        Map<Pair<Long /* pet id */, Long /* service id */>, Long /* lodging unit id */> petLastLodginglMap =
                getLastLodgingUnitId(companyId, businessId, customerIds, petIds, serviceIds);

        // 按 appointmentId 分组，同一个预约的所有 pet services 一起处理
        var appointmentPetServicesMap = appointmentPetServices.stream()
                .collect(Collectors.groupingBy(AppointmentPetServiceDTO::getAppointmentId));

        // 不同预约之间并行处理，同一个预约内的所有 pet services 串行处理
        var addResults = appointmentPetServicesMap.entrySet().stream()
                .map(entry -> CompletableFuture.runAsync(
                        () -> {
                            Long appointmentId = entry.getKey();
                            List<AppointmentPetServiceDTO> appointmentPetServiceList = entry.getValue();

                            var beforeAppointment = appointmentService.mustGet(appointmentId);
                            List<PetDetailDTO> petDetailDTOList = new ArrayList<>();

                            // 为当前预约的所有 pet services 创建 pet details
                            for (AppointmentPetServiceDTO appointmentPetService : appointmentPetServiceList) {
                                var service = petIdToServiceMap
                                        .getOrDefault(appointmentPetService.getPetId(), Map.of())
                                        .get(appointmentPetService.getServiceId());
                                if (service == null) {
                                    log.warn("Service is not available for pet: {}", appointmentPetService);
                                    continue;
                                }

                                // 创建 pet detail
                                var selectedServiceDef = SelectedServiceDef.newBuilder()
                                        .setServiceId(appointmentPetService.getServiceId())
                                        .setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT)
                                        .setStartDate(appointmentPetService.getStartDate())
                                        .setStartTime(startTime)
                                        .build();

                                Pair<Long, Long> key =
                                        Pair.of(appointmentPetService.getPetId(), appointmentPetService.getServiceId());
                                if (CommonUtil.isNormal(petLastLodginglMap.get(key))) {
                                    selectedServiceDef = selectedServiceDef.toBuilder()
                                            .setLodgingId(petLastLodginglMap.get(key))
                                            .build();
                                }

                                var petDetailDTO = petDetailService.buildPetDetailDTO(
                                        beforeAppointment.getAppointmentDate(),
                                        beforeAppointment.getAppointmentEndDate(),
                                        appointmentPetService.getPetId(),
                                        selectedServiceDef,
                                        service);
                                petDetailDTOList.add(petDetailDTO);
                            }

                            // 批量保存当前预约的所有 pet details
                            if (!petDetailDTOList.isEmpty()) {
                                petDetailService.savePetDetails(beforeAppointment, petDetailDTOList);
                                // 只更新一次 appointment 和 order
                                updateAppointmentAndOrder(beforeAppointment);
                            }
                        },
                        ThreadPool.getExecuteExecutor()))
                .toList();

        CompletableFuture.allOf(addResults.toArray(CompletableFuture[]::new)).join();

        // 3. Batch check in
        appointmentService.batchCheckIn(appointmentPetServices.stream()
                .map(AppointmentPetServiceDTO::getAppointmentId)
                .collect(Collectors.toSet()));
    }

    public Map<Pair<Long /* pet id */, Long /* service id */>, Long /* lodging unit id */> getLastLodgingUnitId(
            long companyId,
            long businessId,
            Collection<Long> customerIds,
            Collection<Long> petIds,
            Collection<Long> serviceIds) {
        Map<Pair<Long, Long>, Long> petServiceLodgingUnitIdMap = petDetailService
                .getPetLastPetDetail(
                        companyId,
                        customerIds,
                        petIds,
                        GetLastPetDetailRequest.Filter.newBuilder()
                                .setBusinessId(businessId)
                                .addAllServiceIds(serviceIds)
                                .build())
                .stream()
                .filter(entry -> CommonUtil.isNormal(entry.getLodgingId()))
                .collect(Collectors.toMap(
                        entry -> Pair.of(
                                entry.getPetId().longValue(),
                                entry.getServiceId().longValue()),
                        MoeGroomingPetDetail::getLodgingId));

        List<Long> lodgingUnitIds =
                petServiceLodgingUnitIdMap.values().stream().distinct().toList();

        Set<Long> existUnitIds =
                lodgingRemoteService.getLodgingUnitByUnitIds(companyId, businessId, lodgingUnitIds).stream()
                        .map(LodgingUnitModel::getId)
                        .collect(Collectors.toSet());

        return petServiceLodgingUnitIdMap.entrySet().stream()
                .filter(entry -> existUnitIds.contains(entry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    public void saveCustomerAddress(MoeGroomingAppointment appointment, long appointmentId, long customerId) {
        var address = AppointmentTrackingConverter.INSTANCE.toAddress(customerClient.getPrimaryAddress(customerId));
        if (!address.hasCoordinate() && brandedAppClient.isBrandedAppUser(appointment.getCompanyId())) {
            if (!address.getAddress1().isEmpty()) {
                var suggestions = mapService
                        .autocompleteAddress(AutocompleteAddressRequest.newBuilder()
                                .setTerm(address.getAddress1())
                                .build())
                        .getSuggestionsList();
                if (!suggestions.isEmpty()) {
                    var suggestAddr = mapService
                            .getAddress(GetAddressRequest.newBuilder()
                                    .setAddressSource(suggestions.get(0).getAddressSource())
                                    .build())
                            .getAddress();
                    log.info("Fill Suggest address: {} for appointment {}", suggestAddr, appointmentId);
                    address = address.toBuilder()
                            .setCoordinate(suggestAddr.getCoordinate())
                            .build();
                }
            }
        }
        var record = new AppointmentTracking();
        record.setAppointmentId(appointmentId);
        record.setCustomerAddress(address);
        record.setCompanyId(appointment.getCompanyId());
        appointmentTrackingService.create(record);
    }
}
