package com.moego.svc.appointment.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.appointment.constant.AppointmentStatusSet.ACTIVE_STATUS_VALUE_SET;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.appointmentDate;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.appointmentEndDate;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.appointmentEndTime;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.appointmentStartTime;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.bookOnlineStatus;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.businessId;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.customerId;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.isBlock;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.isDeprecate;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.isWaitingList;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.moeGroomingAppointment;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.noStartTime;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.serviceTypeInclude;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailDynamicSqlSupport.groomingId;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailDynamicSqlSupport.moeGroomingPetDetail;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailDynamicSqlSupport.petId;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailDynamicSqlSupport.status;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailDynamicSqlSupport.updateTime;
import static com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailMapper.updateSelectiveColumns;
import static com.moego.svc.appointment.utils.PetDetailUtil.buildGroomingPetDetail;
import static com.moego.svc.appointment.utils.PetDetailUtil.buildPetDetail;
import static com.moego.svc.appointment.utils.PetDetailUtil.buildScopeTypeTime;
import static com.moego.svc.appointment.utils.PetDetailUtil.calculateEndDateAndEndTime;
import static org.mybatis.dynamic.sql.SqlBuilder.and;
import static org.mybatis.dynamic.sql.SqlBuilder.equalTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isFalse;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.on;
import static org.mybatis.dynamic.sql.SqlBuilder.or;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.PetDetailStatus;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.business_customer.v1.FeedingMedicationScheduleDateType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceScopeType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.service.appointment.v1.GetLastPetDetailRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryAddressRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.common.util.Tx;
import com.moego.svc.appointment.converter.BoardingSplitLodgingConverter;
import com.moego.svc.appointment.converter.GroomingServiceOperationConverter;
import com.moego.svc.appointment.converter.PetDetailConverter;
import com.moego.svc.appointment.converter.PetFeedingConverter;
import com.moego.svc.appointment.converter.PetMedicationConverter;
import com.moego.svc.appointment.converter.ServiceOverrideConverter;
import com.moego.svc.appointment.domain.AppointmentPetMedication;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.MoeGroomingServiceOperation;
import com.moego.svc.appointment.dto.GroomingOnlyDTO;
import com.moego.svc.appointment.dto.PetDetailDTO;
import com.moego.svc.appointment.dto.PetServiceOverrideDTO;
import com.moego.svc.appointment.dto.ServiceOverrideDTO;
import com.moego.svc.appointment.helper.FeatureFlagHelper;
import com.moego.svc.appointment.helper.ServiceHelper;
import com.moego.svc.appointment.helper.params.MustGetCustomizedServiceParam;
import com.moego.svc.appointment.listener.event.PetDetailEvent;
import com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport;
import com.moego.svc.appointment.mapper.mysql.MoeGroomingAppointmentMapper;
import com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailDynamicSqlSupport;
import com.moego.svc.appointment.mapper.mysql.MoeGroomingPetDetailMapper;
import com.moego.svc.appointment.service.remote.CompanyRemoteService;
import com.moego.svc.appointment.service.remote.OfferingRemoteService;
import com.moego.svc.appointment.utils.CriteriaUtils;
import com.moego.svc.appointment.utils.Pair;
import com.moego.svc.appointment.utils.PetDetailUtil;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.mybatis.dynamic.sql.AndOrCriteriaGroup;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.DerivedColumn;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * 所有新增操作必须走 {@link #insert(MoeGroomingPetDetail)}
 *
 * <p>
 * 所有更新操作必须走 {@link #update(MoeGroomingPetDetail)}
 *
 * <p>
 * 所有删除操作必须走 {@link #delete(long)}
 *
 * <p>
 * PetDetail 会依赖事件传播进行业务逻辑处理。
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PetDetailService {

    private final MoeGroomingPetDetailMapper petDetailMapper;
    private final MoeGroomingAppointmentMapper appointmentMapper;
    private final ServiceOperationService serviceOperationService;
    private final AppointmentPetFeedingService petFeedingService;
    private final AppointmentPetMedicationService petMedicationService;
    private final OfferingRemoteService offeringRemoteService;
    private final PetFeedingConverter petFeedingConverter;
    private final PetMedicationConverter petMedicationConverter;
    private final ServiceOverrideConverter serviceOverrideConverter;
    private final ApplicationEventPublisher publisher;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;
    private final CompanyRemoteService companyRemoteService;
    private final BoardingSplitLodgingService boardingSplitLodgingService;
    private final BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub customerAddressService;

    private static final int END_TIME_OF_DAY = 1439;
    private final FeatureFlagHelper featureFlagHelper;
    private final ServiceHelper serviceHelper;

    /**
     * Insert a PetDetail.
     *
     * @param petDetail pet detail
     * @return inserted id
     */
    @Transactional
    public long insert(MoeGroomingPetDetail petDetail) {

        check(petDetail);

        populatePetDetailForInsert(petDetail);

        petDetailMapper.insertSelective(petDetail);

        Tx.doAfterCommit(() -> publisher.publishEvent(new PetDetailEvent.Created(petDetail)));

        return petDetail.getId();
    }

    private void populatePetDetailForInsert(MoeGroomingPetDetail petDetail) {
        var appointment = mustGetAppointment(petDetail.getGroomingId());

        var builder = MustGetCustomizedServiceParam.builder();
        builder.companyId(appointment.getCompanyId());
        builder.businessId(Long.valueOf(appointment.getBusinessId()));
        builder.serviceId(petDetail.getServiceId());
        builder.petId(Long.valueOf(petDetail.getPetId()));
        if (isNormal(petDetail.getStaffId())) {
            builder.staffId(Long.valueOf(petDetail.getStaffId()));
        }

        if (featureFlagHelper.enableVaryPricingByZone(Long.valueOf(appointment.getBusinessId()))) {
            // get zipcode from the customer
            var response =
                    customerAddressService.getCustomerPrimaryAddress(GetCustomerPrimaryAddressRequest.newBuilder()
                            .setCustomerId(appointment.getCustomerId())
                            .build());
            if (response.hasAddress() && response.getAddress().hasCoordinate()) {
                builder.zipcode(response.getAddress().getZipcode())
                        .coordinate(response.getAddress().getCoordinate());
            }
        }

        var customizedService = serviceHelper.mustGetCustomizedService(builder.build());

        if (petDetail.getServiceType() == null) {
            petDetail.setServiceType(customizedService.getTypeValue());
        }
        if (petDetail.getServiceItemType() == null) {
            petDetail.setServiceItemType((byte) customizedService.getServiceItemTypeValue());
        }
        if (petDetail.getPriceUnit() == null) {
            petDetail.setPriceUnit(customizedService.getPriceUnitValue());
        }
        if (petDetail.getDurationOverrideType() == null) {
            petDetail.setDurationOverrideType(customizedService.getDurationOverrideType());
        }
        if (petDetail.getPriceOverrideType() == null) {
            petDetail.setPriceOverrideType(customizedService.getPriceOverrideType());
        }
    }

    public void populatePetDetailForUpdate(
            MoeGroomingPetDetail petDetail, Long companyId, Long businessId, Long customerId) {
        var builder = MustGetCustomizedServiceParam.builder();
        builder.companyId(companyId);
        builder.businessId(businessId);
        builder.serviceId(petDetail.getServiceId());
        builder.petId(Long.valueOf(petDetail.getPetId()));
        if (isNormal(petDetail.getStaffId())) {
            builder.staffId(Long.valueOf(petDetail.getStaffId()));
        }

        if (featureFlagHelper.enableVaryPricingByZone(businessId)) {
            // get zipcode from the customer
            var response =
                    customerAddressService.getCustomerPrimaryAddress(GetCustomerPrimaryAddressRequest.newBuilder()
                            .setCustomerId(customerId)
                            .build());
            if (response.hasAddress() && response.getAddress().hasCoordinate()) {
                builder.zipcode(response.getAddress().getZipcode())
                        .coordinate(response.getAddress().getCoordinate());
            }
        }

        var customizedService = serviceHelper.mustGetCustomizedService(builder.build());

        if (petDetail.getServiceType() == null) {
            petDetail.setServiceType(customizedService.getTypeValue());
        }
        if (petDetail.getServiceItemType() == null) {
            petDetail.setServiceItemType((byte) customizedService.getServiceItemTypeValue());
        }
        if (petDetail.getPriceUnit() == null) {
            petDetail.setPriceUnit(customizedService.getPriceUnitValue());
        }
        if (petDetail.getDurationOverrideType() == null) {
            petDetail.setDurationOverrideType(customizedService.getDurationOverrideType());
        }
        if (petDetail.getPriceOverrideType() == null) {
            petDetail.setPriceOverrideType(customizedService.getPriceOverrideType());
        }

        petDetail.setServicePrice(BigDecimal.valueOf(customizedService.getPrice()));
    }

    private static void check(MoeGroomingPetDetail petDetail) {
        if (!isNormal(petDetail.getGroomingId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "groomingId is required");
        }
        if (!isNormal(petDetail.getPetId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "petId is required");
        }
        if (!isNormal(petDetail.getServiceId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "serviceId is required");
        }
    }

    /**
     * Delete a PetDetail by id.
     *
     * @param id pet detail id
     * @return affected rows
     */
    @Transactional
    public int delete(long id, boolean shouldUpdateOrder) {
        var petDetail = get(id);
        if (petDetail == null) {
            return 0;
        }

        var affectedRows = petDetailMapper.update(c -> c.set(status)
                .equalTo((byte) PetDetailStatus.DELETED_VALUE)
                .set(updateTime)
                .equalTo(CommonUtil.get10Timestamp())
                .where(moeGroomingPetDetail.id, isEqualTo((int) id))
                .and(status, isNotEqualTo((byte) PetDetailStatus.DELETED_VALUE)));

        if (affectedRows > 0) {
            Tx.doAfterCommit(() -> publisher.publishEvent(new PetDetailEvent.Deleted(petDetail, shouldUpdateOrder)));
        }

        return affectedRows;
    }

    /**
     * Delete a PetDetail by id.
     *
     * @param id pet detail id
     * @return affected rows
     */
    @Transactional
    public int delete(long id) {
        return delete(id, true);
    }

    @Transactional
    public int deleteByPetIds(Long appointmentId, List<Long> petIds, boolean shouldUpdateOrder) {
        if (ObjectUtils.isEmpty(petIds)) {
            return 0;
        }

        var ssp = select(moeGroomingPetDetail.id)
                .from(moeGroomingPetDetail)
                .where(moeGroomingPetDetail.groomingId, isEqualTo(appointmentId.intValue()))
                .and(
                        moeGroomingPetDetail.petId,
                        isIn(petIds.stream().map(Long::intValue).collect(Collectors.toSet())))
                .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return petDetailMapper.selectMany(ssp).stream()
                .map(MoeGroomingPetDetail::getId)
                .map(Integer::longValue)
                .mapToInt(e -> delete(e, shouldUpdateOrder))
                .sum();
    }

    @Transactional
    public int deletePetDetails(Long companyId, Long appointmentId, List<Long> petIds) {
        return deletePetDetails(companyId, appointmentId, petIds, true);
    }

    @Transactional
    public int deletePetDetails(Long companyId, Long appointmentId, List<Long> petIds, boolean shouldUpdateOrder) {
        int affectedRows = this.deleteByPetIds(appointmentId, petIds, shouldUpdateOrder);
        serviceOperationService.deleteByPetIds(appointmentId, petIds);
        petFeedingService.deleteByPetIds(companyId, appointmentId, petIds);
        petMedicationService.deleteByPetIds(companyId, appointmentId, petIds);
        boardingSplitLodgingService.deleteByPetIds(appointmentId, petIds);
        return affectedRows;
    }

    private MoeGroomingAppointment mustGetAppointment(long appointmentId) {
        return appointmentMapper
                .selectOne(c -> c.where(moeGroomingAppointment.id, isEqualTo(Math.toIntExact(appointmentId))))
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "appointment not found: " + appointmentId));
    }

    /**
     * 构造 All-in-One 类型的 pet detail list，用户均可以任意指定服务时间
     *
     * @param petDetailDefs     selected pet and service details
     * @param petIdToUpdateTime pet_id -> update_time, pet detail update time 充当 pet
     *                          在 appointment 的排序
     * @return pet detail list with operations, feedings and medications
     */
    public List<PetDetailDTO> buildAllInOnePetDetailList(
            long companyId,
            long businessId,
            List<PetDetailDef> petDetailDefs,
            Map<Long, Map<Long, CustomizedServiceView>> petServiceMap,
            Map<Long, Long> petIdToUpdateTime) {

        // 根据 staffId + petId + serviceId 查询 customized service
        var customizedServiceList = listCustomizedService(companyId, businessId, petDetailDefs);

        var dateRange = getDateRange(petDetailDefs);
        if (Strings.isEmpty(dateRange.first()) || Strings.isEmpty(dateRange.second())) {
            log.error("dateRange is empty, petDetailDefs: {}", JsonUtil.toJson(petDetailDefs));
        }

        var result = petDetailDefs.stream()
                .flatMap(def -> {
                    long petId = def.getPetId();
                    Map<Long, CustomizedServiceView> serviceMap = petServiceMap.get(petId);
                    Stream<PetDetailDTO> serviceStream = def.getServicesList().stream()
                            .map(selectedServiceDef -> {
                                // NOTE：petServiceMap 没有考虑 staff override
                                var customizedService = Optional.ofNullable(getCustomizedService(
                                                customizedServiceList,
                                                selectedServiceDef.getServiceId(),
                                                petId,
                                                selectedServiceDef.getStaffId()))
                                        .orElseGet(() -> serviceMap.get(selectedServiceDef.getServiceId()));
                                return buildPetDetailDTO(
                                        dateRange.first(),
                                        dateRange.second(),
                                        petId,
                                        selectedServiceDef,
                                        customizedService);
                            });
                    Stream<PetDetailDTO> addOnSteam = def.getAddOnsList().stream()
                            .map(selectedAddOnDef -> {
                                CustomizedServiceView addOn = serviceMap.get(selectedAddOnDef.getAddOnId());
                                PetDetailDTO petDetailDTO = buildAddOnPetDetailDTO(
                                        dateRange.first(), dateRange.second(), petId, selectedAddOnDef, addOn);
                                if (selectedAddOnDef.hasAssociatedServiceId()
                                        && serviceMap.containsKey(selectedAddOnDef.getAssociatedServiceId())) {
                                    MoeGroomingPetDetail petDetail = petDetailDTO.getPetDetail();
                                    petDetail.setAssociatedServiceId(selectedAddOnDef.getAssociatedServiceId());
                                    petDetail.setServiceItemType((byte) serviceMap
                                            .get(selectedAddOnDef.getAssociatedServiceId())
                                            .getServiceItemType()
                                            .getNumber());
                                }
                                return petDetailDTO;
                            });
                    // 聚合 service 和 add on 并固定更新时间以确保在 appointment 中的排序
                    List<PetDetailDTO> dtos =
                            Stream.concat(serviceStream, addOnSteam).toList();
                    var updateTime = petIdToUpdateTime.getOrDefault(petId, DateUtil.get10Timestamp());
                    dtos.forEach(petDetailDTO -> petDetailDTO.getPetDetail().setUpdateTime(updateTime));
                    return dtos.stream();
                })
                .toList();

        if (featureFlagHelper.isNewOrderFlow(companyId)) {
            var petIdToServiceIdToDetail = PetDetailUtil.getPetServiceMap(
                    result.stream().map(PetDetailDTO::getPetDetail).toList());
            result.forEach(dto -> {
                var petDetail = dto.getPetDetail();
                var quantity = PetDetailUtil.getQuantity(petDetail, petIdToServiceIdToDetail);
                petDetail.setTotalPrice(petDetail.getServicePrice().multiply(BigDecimal.valueOf(quantity)));
                petDetail.setQuantity(quantity);
            });
        }

        return result;
    }

    private Pair<String, String> getDateRange(List<PetDetailDef> petDetailDefs) {
        String startDate = null;
        String endDate = null;
        for (var def : petDetailDefs) {
            for (var serviceDef : def.getServicesList()) {
                if (StringUtils.hasText(serviceDef.getStartDate())
                        && (startDate == null || startDate.compareTo(serviceDef.getStartDate()) > 0)) {
                    startDate = serviceDef.getStartDate();
                }
                if (StringUtils.hasText(serviceDef.getStartDate())
                        && (endDate == null || endDate.compareTo(serviceDef.getEndDate()) < 0)) {
                    endDate = serviceDef.getEndDate();
                }
            }

            for (var addOnDef : def.getAddOnsList()) {
                if (addOnDef.hasStartDate()
                        && (startDate == null || startDate.compareTo(addOnDef.getStartDate()) > 0)) {
                    startDate = addOnDef.getStartDate();
                }
            }

            for (var evaluationDef : def.getEvaluationsList()) {
                if (StringUtils.hasText(evaluationDef.getStartDate())
                        && (startDate == null || startDate.compareTo(evaluationDef.getStartDate()) > 0)) {
                    startDate = evaluationDef.getStartDate();
                }
            }
        }
        if (endDate == null) {
            endDate = startDate;
        }
        return Pair.of(startDate, endDate);
    }

    private List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> listCustomizedService(
            long companyId, long businessId, List<PetDetailDef> petDetailDefs) {
        var builder = BatchGetCustomizedServiceRequest.newBuilder();
        builder.setCompanyId(companyId);
        for (var def : petDetailDefs) {
            for (var serviceDef : def.getServicesList()) {
                var b = CustomizedServiceQueryCondition.newBuilder()
                        .setServiceId(serviceDef.getServiceId())
                        .setPetId(def.getPetId())
                        .setBusinessId(businessId);
                if (isNormal(serviceDef.getStaffId())) {
                    b.setStaffId(serviceDef.getStaffId());
                }
                builder.addQueryConditionList(b.build());
            }
        }
        return serviceStub.batchGetCustomizedService(builder.build()).getCustomizedServiceListList();
    }

    @Nullable
    private static CustomizedServiceView getCustomizedService(
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            long serviceId,
            long petId,
            long staffId) {
        return customizedServiceList.stream()
                .filter(service -> service.getQueryCondition().getServiceId() == serviceId
                        && service.getQueryCondition().getPetId() == petId
                        && service.getQueryCondition().getStaffId() == staffId)
                .findFirst()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .orElse(null);
    }

    public PetDetailDTO buildPetDetailDTO(
            String appointmentStartDate,
            String appointmentEndDate,
            Long petId,
            SelectedServiceDef selectedServiceDef,
            CustomizedServiceView service) {
        checkDateType(selectedServiceDef);
        MoeGroomingPetDetail petDetail =
                switch (service.getServiceItemType()) {
                    case BOARDING -> buildBoardingService(petId, selectedServiceDef, service);
                    case DAYCARE -> buildDaycareService(
                            appointmentStartDate, appointmentEndDate, petId, selectedServiceDef, service);
                    case GROOMING -> buildGroomingService(
                            appointmentStartDate, appointmentEndDate, petId, selectedServiceDef, service);
                    case DOG_WALKING -> buildDogWalkingService(petId, selectedServiceDef, service);
                    default -> throw bizException(Code.CODE_SERVICE_NOT_FOUND);
                };
        return new PetDetailDTO()
                .setPetId(petId)
                .setPetDetail(petDetail)
                .setOperations(GroomingServiceOperationConverter.INSTANCE.defToDomain(
                        selectedServiceDef.getOperationsList(), petDetail))
                .setFeedings(petFeedingConverter.defToDTO(selectedServiceDef.getFeedingsList()))
                .setMedications(petMedicationConverter.defToDTO(selectedServiceDef.getMedicationsList()))
                .setSplitLodgings(BoardingSplitLodgingConverter.INSTANCE.defToDTO(
                        selectedServiceDef.getSplitLodgingsList(), petId))
                .setService(service);
    }

    PetDetailDTO buildAddOnPetDetailDTO(
            String appointmentStartDate,
            String appointmentEndDate,
            Long petId,
            SelectedAddOnDef selectedAddOnDef,
            CustomizedServiceView service) {
        SelectedServiceDef selectedServiceDef = PetDetailConverter.INSTANCE.addOnToService(selectedAddOnDef);
        int serviceTime = selectedAddOnDef.hasServiceTime() ? selectedAddOnDef.getServiceTime() : service.getDuration();
        MoeGroomingPetDetail petDetail = getPetDetail(
                appointmentStartDate,
                appointmentEndDate,
                petId,
                selectedAddOnDef,
                service,
                selectedServiceDef,
                serviceTime);
        return new PetDetailDTO()
                .setPetId(petId)
                .setPetDetail(petDetail)
                .setOperations(GroomingServiceOperationConverter.INSTANCE.defToDomain(
                        selectedAddOnDef.getOperationsList(), petDetail))
                .setService(service);
    }

    private static MoeGroomingPetDetail getPetDetail(
            String appointmentStartDate,
            String appointmentEndDate,
            Long petId,
            SelectedAddOnDef selectedAddOnDef,
            CustomizedServiceView service,
            SelectedServiceDef selectedServiceDef,
            int serviceTime) {
        return switch (selectedAddOnDef.getAddonDateType()) {
                // Require staff to specify start date and start time
            case PET_DETAIL_DATE_DATE_POINT -> {
                MoeGroomingPetDetail petDetail = buildPetDetail(petId, service, selectedServiceDef);
                petDetail.setServiceTime(serviceTime);
                petDetail.setQuantityPerDay(PetDetailUtil.getQuantityPerDay(selectedAddOnDef));

                if (service.getRequireDedicatedStaff() || selectedAddOnDef.hasStaffId()) {
                    petDetail.setStaffId((int) selectedAddOnDef.getStaffId());
                    petDetail.setStartTime((long) selectedAddOnDef.getStartTime());
                    petDetail.setEndTime((long) selectedAddOnDef.getStartTime() + selectedAddOnDef.getServiceTime());
                }
                yield petDetail;
            }
                // No require staff
            case PET_DETAIL_DATE_SPECIFIC_DATE -> {
                MoeGroomingPetDetail petDetail = buildPetDetail(petId, service, selectedServiceDef);
                if (selectedAddOnDef.getSpecificDatesCount() == 0) {
                    throw bizException(Code.CODE_PARAMS_ERROR, "Specific date must be specified");
                }
                petDetail.setSpecificDates(JsonUtil.toJson(selectedAddOnDef.getSpecificDatesList()));
                petDetail.setServiceTime(serviceTime);
                petDetail.setQuantityPerDay(PetDetailUtil.getQuantityPerDay(selectedAddOnDef));

                if (service.getRequireDedicatedStaff()) {
                    petDetail.setStaffId((int) selectedAddOnDef.getStaffId());
                    petDetail.setStartTime((long) selectedAddOnDef.getStartTime());
                    petDetail.setEndTime((long) selectedAddOnDef.getStartTime() + selectedAddOnDef.getServiceTime());
                }
                yield petDetail;
            }
            case PET_DETAIL_DATE_EVERYDAY,
                    PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY,
                    PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY -> {
                MoeGroomingPetDetail everyDayPetDetail = buildPetDetail(petId, service, selectedServiceDef);
                everyDayPetDetail.setServiceTime(serviceTime);
                everyDayPetDetail.setQuantityPerDay(PetDetailUtil.getQuantityPerDay(selectedAddOnDef));

                if (service.getRequireDedicatedStaff()) {
                    everyDayPetDetail.setStaffId((int) selectedAddOnDef.getStaffId());
                    everyDayPetDetail.setStartTime((long) selectedAddOnDef.getStartTime());
                    everyDayPetDetail.setEndTime(
                            (long) selectedAddOnDef.getStartTime() + selectedAddOnDef.getServiceTime());
                }
                yield everyDayPetDetail;
            }
            case PET_DETAIL_DATE_FIRST_DAY -> {
                MoeGroomingPetDetail petDetail = buildPetDetail(petId, service, selectedServiceDef);
                petDetail.setStartDate(appointmentStartDate);
                petDetail.setEndDate(appointmentStartDate);
                petDetail.setServiceTime(selectedAddOnDef.getServiceTime());
                petDetail.setQuantityPerDay(PetDetailUtil.getQuantityPerDay(selectedAddOnDef));

                if (service.getRequireDedicatedStaff()) {
                    petDetail.setStaffId((int) selectedAddOnDef.getStaffId());
                    petDetail.setStartTime((long) selectedAddOnDef.getStartTime());
                    petDetail.setEndTime((long) selectedAddOnDef.getStartTime() + selectedAddOnDef.getServiceTime());
                }
                yield petDetail;
            }
            case PET_DETAIL_DATE_LAST_DAY -> {
                MoeGroomingPetDetail petDetail = buildPetDetail(petId, service, selectedServiceDef);
                petDetail.setStartDate(appointmentEndDate);
                petDetail.setEndDate(appointmentEndDate);
                petDetail.setServiceTime(selectedAddOnDef.getServiceTime());
                petDetail.setQuantityPerDay(PetDetailUtil.getQuantityPerDay(selectedAddOnDef));

                if (service.getRequireDedicatedStaff()) {
                    petDetail.setStaffId((int) selectedAddOnDef.getStaffId());
                    petDetail.setStartTime((long) selectedAddOnDef.getStartTime());
                    petDetail.setEndTime((long) selectedAddOnDef.getStartTime() + selectedAddOnDef.getServiceTime());
                }
                yield petDetail;
            }
            default -> throw bizException(Code.CODE_PARAMS_ERROR, "Date type not found");
        };
    }

    /**
     * Boarding 类型服务，一般持续时间为一段时间
     *
     * @param petId   pet id
     * @param def     selected boarding service def
     * @param service service detail
     * @return pet detail
     */
    private static MoeGroomingPetDetail buildBoardingService(
            Long petId, SelectedServiceDef def, CustomizedServiceView service) {
        if (!def.hasEndDate()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Boarding service must specify end date");
        }
        MoeGroomingPetDetail petDetail = buildPetDetail(petId, service, def);
        petDetail.setStartDate(def.getStartDate());
        petDetail.setEndDate(def.getEndDate());
        petDetail.setStartTime((long) def.getStartTime());
        petDetail.setEndTime((long) def.getEndTime());
        petDetail.setLodgingId(def.getLodgingId());
        petDetail.setServiceTime(Math.toIntExact(PetDetailUtil.calculateServiceTime(petDetail)));
        return petDetail;
    }

    /**
     * Daycare 类型服务，一般持续时间为 1 天。boarding 下的 daycare 服务，支持多天
     *
     * @param petId   pet id
     * @param def     selected daycare service def
     * @param service service detail
     * @return pet detail
     */
    private static MoeGroomingPetDetail buildDaycareService(
            String appointmentStartDate,
            String appointmentEndDate,
            Long petId,
            SelectedServiceDef def,
            CustomizedServiceView service) {
        MoeGroomingPetDetail petDetail = buildPetDetail(petId, service, def);
        switch (def.getDateType()) {
            case PET_DETAIL_DATE_SPECIFIC_DATE -> {
                if (def.getSpecificDatesCount() == 0) {
                    throw bizException(Code.CODE_PARAMS_ERROR, "Specific date must be specified");
                }
                petDetail.setSpecificDates(JsonUtil.toJson(def.getSpecificDatesList()));
            }
            case PET_DETAIL_DATE_DATE_POINT -> {
                petDetail.setStartDate(def.getStartDate());
                petDetail.setEndDate(def.getStartDate());
                if (def.hasEndDate()) {
                    petDetail.setEndDate(def.getEndDate());
                }
            }
            case PET_DETAIL_DATE_LAST_DAY -> {
                petDetail.setStartDate(appointmentEndDate);
                petDetail.setEndDate(appointmentEndDate);
            }
            case PET_DETAIL_DATE_FIRST_DAY -> {
                petDetail.setStartDate(appointmentStartDate);
                petDetail.setEndDate(appointmentStartDate);
            }
            default -> {
                // do nothing
            }
        }
        petDetail.setStartTime((long) def.getStartTime());
        if (def.hasEndTime() && def.getEndTime() > 0) {
            petDetail.setEndTime(PetDetailUtil.limitEndTime(def.getEndTime()).longValue());
        } else {
            petDetail.setEndTime(PetDetailUtil.limitEndTime(petDetail.getStartTime() + service.getMaxDuration())
                    .longValue());
        }
        petDetail.setServiceTime(Math.toIntExact(petDetail.getEndTime() - petDetail.getStartTime()));
        if (isNormal(def.getLodgingId())) {
            petDetail.setLodgingId(def.getLodgingId());
        }
        return petDetail;
    }

    /**
     * Grooming 类型服务，也可以设置每个 pet 开始时间不同 不同于 grooming only，可以任意指定时间
     *
     * @param petId   pet id
     * @param def     selected grooming service and operation def
     * @param service service detail
     * @return pet detail
     */
    private static MoeGroomingPetDetail buildGroomingService(
            String appointmentStartDate,
            String appointmentEndDate,
            Long petId,
            SelectedServiceDef def,
            CustomizedServiceView service) {
        checkRequireStaffParams(def, service);

        var petDetail = buildGroomingPetDetail(petId, service, def);
        var date = getGroomingDate(appointmentStartDate, appointmentEndDate, def);
        petDetail.setStartDate(date);
        petDetail.setEndDate(date);
        petDetail.setStartTime((long) def.getStartTime());

        var endPair = calculateEndDateAndEndTime(
                petDetail.getStartDate(), petDetail.getStartTime().intValue(), petDetail.getServiceTime());
        petDetail.setEndDate(endPair.first());
        petDetail.setEndTime(endPair.second().longValue());
        return petDetail;
    }

    private static String getGroomingDate(
            String appointmentStartDate, String appointmentEndDate, SelectedServiceDef def) {
        String date =
                switch (def.getDateType()) {
                    case PET_DETAIL_DATE_DATE_POINT -> def.getStartDate();
                    case PET_DETAIL_DATE_SPECIFIC_DATE -> def.getSpecificDatesList()
                            .get(0);
                    case PET_DETAIL_DATE_EVERYDAY,
                            PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY,
                            PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY -> ""; // grooming service 暂时没有这三种类型
                    case PET_DETAIL_DATE_LAST_DAY -> appointmentEndDate;
                    case PET_DETAIL_DATE_FIRST_DAY -> appointmentStartDate;
                    default -> def.getStartDate();
                };
        // 兼容一些奇奇怪怪的场景，做最后兜底，拿不到 date 的时候就用 Appointment 的 start date
        if (Objects.isNull(date) || date.isEmpty()) {
            return appointmentStartDate;
        }
        return date;
    }

    private static void checkRequireStaffParams(SelectedServiceDef def, CustomizedServiceView service) {
        if (!def.hasStartTime()) {
            switch (service.getType()) {
                case SERVICE -> throw bizException(Code.CODE_PARAMS_ERROR, "The service requires a start time.");
                case ADDON -> throw bizException(
                        Code.CODE_PARAMS_ERROR, "The require-staff add-on requires a start time.");
                default -> {}
            }
        }
        if (!def.hasStaffId()) {
            switch (service.getType()) {
                case SERVICE -> throw bizException(Code.CODE_PARAMS_ERROR, "The service requires a staff id.");
                case ADDON -> throw bizException(
                        Code.CODE_PARAMS_ERROR, "The require-staff add-on requires a staff id.");
                default -> {}
            }
        }
    }

    private static void checkDateType(SelectedServiceDef def) {
        switch (def.getDateType()) {
            case PET_DETAIL_DATE_DATE_POINT -> {
                if (!StringUtils.hasText(def.getStartDate())) {
                    throw bizException(Code.CODE_PARAMS_ERROR, "service with date point type must specify start date");
                }
            }
            case PET_DETAIL_DATE_SPECIFIC_DATE -> {
                if (def.getSpecificDatesCount() == 0) {
                    throw bizException(Code.CODE_PARAMS_ERROR, "Specific date must be specified");
                }
            }
            default -> {}
        }
    }

    /**
     * Dog Walking service 详情，需要有 staff 和 date time，不支持 multi-staff
     *
     * @param petId   pet id
     * @param def     selected dog walking service staff and date time
     * @param service service detail
     * @return dog walking pet detail
     */
    private static MoeGroomingPetDetail buildDogWalkingService(
            Long petId, SelectedServiceDef def, CustomizedServiceView service) {
        checkRequireStaffParams(def, service);

        var petDetail = buildPetDetail(petId, service, def);

        var scopeTypeTime = buildScopeTypeTime(def, service);
        petDetail.setScopeTypeTime(scopeTypeTime.getNumber());

        var duration = def.hasServiceTime() ? def.getServiceTime() : service.getDuration();

        petDetail.setServiceTime(duration);
        petDetail.setStaffId((int) def.getStaffId());
        petDetail.setStartDate(def.getStartDate());
        petDetail.setStartTime((long) def.getStartTime());

        var endDateTime = calculateEndDateAndEndTime(def.getStartDate(), def.getStartTime(), duration);
        petDetail.setEndDate(endDateTime.first());
        petDetail.setEndTime(endDateTime.second().longValue());

        return petDetail;
    }

    /**
     * Grooming only 的 appointment，add-on 和 service 处理逻辑相同
     *
     * @param petDetailDefs   selected pet and service details
     * @param petServiceMap   pet id -> service id -> service detail
     * @param groomingOnlyDTO grooming only dto
     * @return pet detail list with operations, feedings and medications
     * @deprecated use
     *             {@link PetDetailUtil#buildGroomingOnlyPetDetails(List, Map, LocalDateTime, boolean)}
     */
    @Deprecated
    public List<PetDetailDTO> buildGroomingOnlyPetDetailList(
            List<PetDetailDef> petDetailDefs,
            Map<Long, Map<Long, CustomizedServiceView>> petServiceMap,
            GroomingOnlyDTO groomingOnlyDTO) {
        Map<Long, Map<Long, Integer>> petServiceOrderMap = groomingOnlyDTO.getPetServiceOrderMap();
        return petDetailDefs.stream()
                .flatMap(def -> {
                    Map<Long, SelectedAddOnDef> addOnMap = def.getAddOnsList().stream()
                            .collect(Collectors.toMap(SelectedAddOnDef::getAddOnId, Function.identity(), (a, b) -> a));
                    Map<Long, Integer> serviceOrderMap = petServiceOrderMap.getOrDefault(def.getPetId(), Map.of());
                    Stream<PetDetailDTO> stream = Stream.concat(
                                    def.getServicesList().stream(),
                                    def.getAddOnsList().stream().map(PetDetailConverter.INSTANCE::addOnToService))
                            .sorted(Comparator.comparing(s -> serviceOrderMap.getOrDefault(s.getServiceId(), 0)))
                            .map(selectedServiceDef -> {
                                CustomizedServiceView service =
                                        petServiceMap.get(def.getPetId()).get(selectedServiceDef.getServiceId());
                                LocalDateTime petServiceDateTime = groomingOnlyDTO.getPetServiceDateTime();
                                MoeGroomingPetDetail petDetail = PetDetailUtil.buildGroomingOnlyService(
                                        def.getPetId(), selectedServiceDef, service, petServiceDateTime);
                                Optional.ofNullable(addOnMap.get(selectedServiceDef.getServiceId()))
                                        .ifPresent(addOn ->
                                                petDetail.setAssociatedServiceId(addOn.getAssociatedServiceId()));
                                groomingOnlyDTO.setPetServiceDateTime(
                                        petServiceDateTime.plusMinutes(petDetail.getServiceTime()));
                                return new PetDetailDTO()
                                        .setPetId(def.getPetId())
                                        .setPetDetail(petDetail)
                                        .setOperations(GroomingServiceOperationConverter.INSTANCE.defToDomain(
                                                selectedServiceDef.getOperationsList(), petDetail))
                                        .setService(service);
                            });
                    // all pets start at same time 时重置 pet start time
                    if (groomingOnlyDTO.isAllPetsStartAtSameTime()) {
                        groomingOnlyDTO.setPetServiceDateTime(groomingOnlyDTO.getServiceDateTime());
                    }
                    return stream;
                })
                .toList();
    }

    public List<MoeGroomingPetDetail> getPetDetailList(Long appointmentId) {
        return petDetailMapper.select(c -> c.where(groomingId, isEqualTo(appointmentId.intValue()))
                .and(status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE))
                .orderBy(updateTime));
    }

    public List<MoeGroomingPetDetail> getPetDetailList(long appointmentId, long petId) {
        return getPetDetailList(appointmentId, List.of(petId));
    }

    public List<MoeGroomingPetDetail> getPetDetailList(long appointmentId, List<Long> petIds) {
        if (CollectionUtils.isEmpty(petIds)) {
            return List.of();
        }
        return petDetailMapper.select(c -> c.where(moeGroomingPetDetail.groomingId, isEqualTo((int) appointmentId))
                .and(
                        moeGroomingPetDetail.petId,
                        isInWhenPresent(petIds.stream().map(Long::intValue).collect(Collectors.toSet())))
                .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE))
                .orderBy(moeGroomingPetDetail.updateTime));
    }

    public Map<Long, List<MoeGroomingPetDetail>> getPetDetailsByAppointmentIds(List<Long> appointmentIds) {
        return petDetailMapper
                .select(c -> c.where(
                                groomingId,
                                isIn(appointmentIds.stream().map(Long::intValue).collect(Collectors.toSet())))
                        .and(status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE)))
                .stream()
                .collect(Collectors.groupingBy(
                        petDetail -> petDetail.getGroomingId().longValue()));
    }

    public List<Integer> getStaffIds(Long appointmentId) {
        return petDetailMapper
                .select(c -> c.where(groomingId, isEqualTo(appointmentId.intValue()))
                        .and(status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE)))
                .stream()
                .map(MoeGroomingPetDetail::getStaffId)
                .distinct()
                .toList();
    }

    /**
     * 保存 pet details
     *
     * @param appointment   appointment detail
     * @param detailDTOList selected pet and service details
     */
    @Transactional
    public int savePetDetails(MoeGroomingAppointment appointment, List<PetDetailDTO> detailDTOList) {
        Long appointmentId = Long.valueOf(appointment.getId());
        List<Integer> petIds = detailDTOList.stream()
                .map(PetDetailDTO::getPetId)
                .map(Long::intValue)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(petIds)) {
            return 0;
        }
        // 1. 保存 pet detail 并回填 id
        detailDTOList.forEach(detailDTO -> {
            MoeGroomingPetDetail petDetail = detailDTO.getPetDetail();
            petDetail.setGroomingId(appointmentId.intValue());
            this.insert(detailDTO.getPetDetail());
            Optional.ofNullable(detailDTO.getOperations()).orElse(List.of()).forEach(operation -> {
                operation.setBusinessId(appointment.getBusinessId());
                operation.setCompanyId(appointment.getCompanyId());
                operation.setGroomingId(appointmentId.intValue());
                operation.setGroomingServiceId(petDetail.getId());
                operation.setPetId(petDetail.getPetId());
            });
            Optional.ofNullable(detailDTO.getFeedings()).orElse(List.of()).forEach(feedingScheduleDTO -> {
                feedingScheduleDTO.getFeeding().setCompanyId(appointment.getCompanyId());
                feedingScheduleDTO.getFeeding().setAppointmentId(appointmentId);
                feedingScheduleDTO.getFeeding().setPetDetailId(Long.valueOf(petDetail.getId()));
                feedingScheduleDTO.getFeeding().setPetId(Long.valueOf(petDetail.getPetId()));
                feedingScheduleDTO.getScheduleSettings().forEach(scheduleSetting -> {
                    scheduleSetting.setCompanyId(appointment.getCompanyId());
                    scheduleSetting.setAppointmentId(appointmentId);
                });
            });
            Optional.ofNullable(detailDTO.getMedications()).orElse(List.of()).forEach(medicationScheduleDTO -> {
                medicationScheduleDTO.getMedication().setCompanyId(appointment.getCompanyId());
                medicationScheduleDTO.getMedication().setAppointmentId(appointmentId);
                medicationScheduleDTO.getMedication().setPetDetailId(Long.valueOf(petDetail.getId()));
                medicationScheduleDTO.getMedication().setPetId(Long.valueOf(petDetail.getPetId()));
                medicationScheduleDTO.getScheduleSettings().forEach(scheduleSetting -> {
                    scheduleSetting.setCompanyId(appointment.getCompanyId());
                    scheduleSetting.setAppointmentId(appointmentId);
                });
            });
            Optional.ofNullable(detailDTO.getSplitLodgings()).orElse(List.of()).forEach(splitLodging -> {
                splitLodging.setAppointmentId(appointmentId);
                splitLodging.setPetDetailId(petDetail.getId().longValue());
                splitLodging.setPetId(petDetail.getPetId().longValue());
                splitLodging.setCreatedAt(LocalDateTime.now());
            });
        });

        // 2. Batch save service operations
        List<MoeGroomingServiceOperation> operations = detailDTOList.stream()
                .map(PetDetailDTO::getOperations)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .toList();
        if (!CollectionUtils.isEmpty(operations)) {
            serviceOperationService.insertMultiple(operations);
        }

        // 3. Batch save pet feeding and medication
        List<PetDetailDTO.PetFeedingScheduleDTO> feedings = detailDTOList.stream()
                .map(PetDetailDTO::getFeedings)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .toList();
        if (!CollectionUtils.isEmpty(feedings)) {
            petFeedingService.insertMultiple(appointment.getCompanyId(), feedings);
        }
        List<PetDetailDTO.PetMedicationScheduleDTO> medications = detailDTOList.stream()
                .map(PetDetailDTO::getMedications)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .toList();
        if (!CollectionUtils.isEmpty(medications)) {
            petMedicationService.insertMultiple(appointment.getCompanyId(), medications);
        }

        // 4. Batch saves split lodging
        var splitLodgings = detailDTOList.stream()
                .map(PetDetailDTO::getSplitLodgings)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .toList();
        boardingSplitLodgingService.saveBoardingSplitLodging(splitLodgings);

        return detailDTOList.size();
    }

    /**
     * Update a PetDetail by id.
     *
     * @param petDetail pet detail
     * @return affected rows
     */
    public int update(MoeGroomingPetDetail petDetail) {
        return update(petDetail, true);
    }

    /**
     * Update a PetDetail by id.
     *
     * @param petDetail         pet detail
     * @param shouldUpdateOrder should update order
     * @return affected rows
     */
    public int update(MoeGroomingPetDetail petDetail, boolean shouldUpdateOrder) {
        if (!isNormal(petDetail.getId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Pet detail id is required");
        }

        var before = get(petDetail.getId());
        if (before == null) {
            return 0;
        }

        int affectedRows =
                switch (getServiceItemType(before.getServiceItemType())) {
                    case SERVICE_ITEM_TYPE_UNSPECIFIED -> 0;
                    case BOARDING -> updateBoarding(petDetail, before, shouldUpdateOrder);
                    case DAYCARE, GROOMING, DOG_WALKING -> updatePetDetailById(petDetail);
                    default -> throw bizException(
                            Code.CODE_PARAMS_ERROR, "Unsupported service item type: " + before.getServiceItemType());
                };

        if (affectedRows > 0) {
            var after = get(petDetail.getId());
            Tx.doAfterCommit(
                    () -> publisher.publishEvent(new PetDetailEvent.Updated(before, after, shouldUpdateOrder)));
        }

        return affectedRows;
    }

    private static ServiceItemType getServiceItemType(Byte serviceItemType) {
        var result = ServiceItemType.forNumber(serviceItemType);
        if (result == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Service item type not found: " + serviceItemType);
        }
        return result;
    }

    private static PetDetailDateType getDateType(Integer dateType) {
        var result = PetDetailDateType.forNumber(dateType);
        if (result == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Date type not found: " + dateType);
        }
        return result;
    }

    private int updateBoarding(
            MoeGroomingPetDetail updateBean, MoeGroomingPetDetail before, boolean shouldUpdateOrder) {
        var affectedRows = updatePetDetailById(updateBean);
        if (affectedRows == 0) {
            return 0;
        }

        var after = mustGet(updateBean.getId());

        // 如果 boarding 的 start date 和 end date 没有变化，
        // 不需要更新挂在上面的 service/addon/medication 信息
        if (Objects.equals(before.getStartDate(), after.getStartDate())
                && Objects.equals(before.getEndDate(), after.getEndDate())) {
            return affectedRows;
        }

        // 如果是 add-on，不需要处理 child
        if (Objects.equals(before.getServiceType(), ServiceType.ADDON_VALUE)) {
            return affectedRows;
        }

        var newDates = LocalDate.parse(after.getStartDate())
                .datesUntil(LocalDate.parse(after.getEndDate()).plusDays(1))
                .toList();

        var children = listChildPetDetailForBoarding(after.getGroomingId(), after.getPetId());

        for (var petDetail : children) {
            switch (getServiceItemType(petDetail.getServiceItemType())) {
                case BOARDING -> {
                    if (Objects.equals(petDetail.getServiceType(), ServiceType.ADDON_VALUE)) {
                        processPetDetailForBoarding(petDetail, newDates, shouldUpdateOrder);
                    }
                }
                case DAYCARE -> {
                    processPetDetailForDaycare(petDetail, newDates, shouldUpdateOrder);
                }
                case GROOMING, DOG_WALKING -> {
                    switch (petDetail.getDateType()) {
                        case PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE -> {
                            if (!newDates.contains(LocalDate.parse(petDetail.getStartDate()))) {
                                delete(petDetail.getId(), shouldUpdateOrder);
                            }
                        }
                        case PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY_VALUE -> processSingleDayService(
                                petDetail, newDates.get(0), shouldUpdateOrder);
                        case PetDetailDateType.PET_DETAIL_DATE_LAST_DAY_VALUE -> processSingleDayService(
                                petDetail, newDates.get(newDates.size() - 1), shouldUpdateOrder);
                        default -> {}
                    }
                }
                default -> {}
            }
        }

        // 同步处理 DateType = SPECIFIC_DATE 的 medication
        List<AppointmentPetMedication> appointmentPetMedications =
                petMedicationService.listPetMedicationsByAppointmentId(after.getGroomingId());
        for (var petMedication : appointmentPetMedications) {
            processMedicationForBoarding(petMedication, newDates);
        }

        return affectedRows;
    }

    private List<MoeGroomingPetDetail> listChildPetDetailForBoarding(Integer groomingId, Integer petId) {

        var servicePetDetails =
                petDetailMapper.select(c -> c.where(moeGroomingPetDetail.groomingId, isEqualTo(groomingId))
                        .and(moeGroomingPetDetail.petId, isEqualTo(petId))
                        .and(
                                moeGroomingPetDetail.serviceItemType,
                                isIn((byte) ServiceItemType.DAYCARE_VALUE, (byte) ServiceItemType.GROOMING_VALUE, (byte)
                                        ServiceItemType.DOG_WALKING_VALUE))
                        .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE)));

        var boardingAddonPetDetails =
                petDetailMapper.select(c -> c.where(moeGroomingPetDetail.groomingId, isEqualTo(groomingId))
                        .and(moeGroomingPetDetail.petId, isEqualTo(petId))
                        .and(moeGroomingPetDetail.serviceItemType, isEqualTo((byte) ServiceItemType.BOARDING_VALUE))
                        .and(moeGroomingPetDetail.serviceType, isEqualTo(ServiceType.ADDON_VALUE))
                        .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE)));

        return Stream.concat(servicePetDetails.stream(), boardingAddonPetDetails.stream())
                .toList();
    }

    /* private */ void processPetDetailForDaycare(
            MoeGroomingPetDetail daycare, List<LocalDate> newDates, boolean shouldUpdateOrder) {

        if (getDateType(daycare.getDateType()) != PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE) {
            return;
        }

        var beforeDates = JsonUtil.toList(daycare.getSpecificDates(), LocalDate.class);

        var afterDates = beforeDates.stream().filter(newDates::contains).toList();

        if (Objects.equals(beforeDates, afterDates)) {
            return;
        }

        // 当 specificDates 为空时，删除 PetDetail
        // 当 specificDates 不为空时，更新 PetDetail
        // Fixes https://moego.atlassian.net/browse/MER-4241

        if (afterDates.isEmpty()) {
            delete(daycare.getId(), shouldUpdateOrder);
        } else {
            var update = new MoeGroomingPetDetail();
            update.setId(daycare.getId());
            update.setSpecificDates(JsonUtil.toJson(afterDates));
            update(update, shouldUpdateOrder);
        }
    }

    /* private */ void processPetDetailForBoarding(
            MoeGroomingPetDetail petDetail, List<LocalDate> newDates, boolean shouldUpdateOrder) {
        switch (getDateType(petDetail.getDateType())) {
            case PET_DETAIL_DATE_SPECIFIC_DATE -> processSpecificDateAddons(petDetail, newDates, shouldUpdateOrder);
            case PET_DETAIL_DATE_FIRST_DAY -> processSingleDayService(petDetail, newDates.get(0), shouldUpdateOrder);
            case PET_DETAIL_DATE_LAST_DAY -> processSingleDayService(
                    petDetail, newDates.get(newDates.size() - 1), shouldUpdateOrder);
            default -> {}
        }
    }

    /* private */ void processSpecificDateAddons(
            MoeGroomingPetDetail petDetail, List<LocalDate> newDates, boolean shouldUpdateOrder) {

        if (getDateType(petDetail.getDateType()) != PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE) {
            return;
        }

        var beforeDates = JsonUtil.toList(petDetail.getSpecificDates(), LocalDate.class);

        var afterDates = beforeDates.stream().filter(newDates::contains).toList();

        if (Objects.equals(beforeDates, afterDates)) {
            return;
        }

        // 当 specificDates 为空时，删除 PetDetail
        // 当 specificDates 不为空时，更新 PetDetail
        // Fixes https://moego.atlassian.net/browse/MER-4241

        if (afterDates.isEmpty()) {
            delete(petDetail.getId(), shouldUpdateOrder);
        } else {
            var update = new MoeGroomingPetDetail();
            update.setId(petDetail.getId());
            update.setSpecificDates(JsonUtil.toJson(afterDates));
            update(update, shouldUpdateOrder);
        }
    }

    /* private */ void processSingleDayService(
            MoeGroomingPetDetail petDetail, LocalDate date, boolean shouldUpdateOrder) {
        if (petDetail.getStartDate() != null && petDetail.getStartDate().equals(date.toString())) {
            return;
        }

        var update = new MoeGroomingPetDetail();
        update.setId(petDetail.getId());
        update.setStartDate(date.toString());
        update.setEndDate(date.toString());
        update(update, shouldUpdateOrder);
    }
    /* private */ void processMedicationForBoarding(AppointmentPetMedication petMedication, List<LocalDate> newDates) {

        if (!petMedication.getDateType().equals(FeedingMedicationScheduleDateType.SPECIFIC_DATE)) {
            return;
        }

        var beforeDates = JsonUtil.toList(petMedication.getSpecificDates(), LocalDate.class);

        var afterDates = beforeDates.stream().filter(newDates::contains).toList();

        if (Objects.equals(beforeDates, afterDates)) {
            return;
        }

        // 当 specificDates 为空时，删除 petMedication
        // 当 specificDates 不为空时，更新 petMedication
        if (afterDates.isEmpty()) {
            petMedicationService.delete(petMedication.getId());
        } else {
            var update = new AppointmentPetMedication();
            update.setId(petMedication.getId());
            update.setSpecificDates(JsonUtil.toJson(afterDates));
            petMedicationService.update(update);
        }
    }

    private int updatePetDetailById(MoeGroomingPetDetail updateBean) {
        if (updateBean.getUpdatedAt() == null) {
            updateBean.setUpdatedAt(new Date());
        }
        return petDetailMapper.update(c -> updateSelectiveColumns(updateBean, c)
                .where(moeGroomingPetDetail.id, isEqualTo(updateBean.getId()))
                .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE)));
    }

    @Transactional
    public int updatePetDetailById(List<MoeGroomingPetDetail> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return 0;
        }
        return petDetails.stream().mapToInt(this::update).sum();
    }

    public List<MoeGroomingPetDetail> getPetDetailList(List<Long> appointmentIdList) {
        if (CollectionUtils.isEmpty(appointmentIdList)) {
            return new ArrayList<>();
        }

        return petDetailMapper.select(c -> c.where(
                        groomingId,
                        isIn(appointmentIdList.stream().map(Long::intValue).toList()))
                .and(status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE)));
    }

    @Transactional
    public int updateLodging(List<Integer> ids, Long targetLodgingId) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }

        var ssp = select(moeGroomingPetDetail.id)
                .from(moeGroomingPetDetail)
                .where(moeGroomingPetDetail.id, isIn(ids))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return petDetailMapper.selectMany(ssp).stream()
                .map(MoeGroomingPetDetail::getId)
                .mapToInt(id -> {
                    var updateBean = new MoeGroomingPetDetail();
                    updateBean.setId(id);
                    updateBean.setLodgingId(targetLodgingId);
                    return update(updateBean);
                })
                .sum();
    }

    private int updateAssociatedServiceId(List<Integer> ids, Long targetAssociatedServiceId) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }

        var ssp = select(moeGroomingPetDetail.id)
                .from(moeGroomingPetDetail)
                .where(moeGroomingPetDetail.id, isIn(ids))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return petDetailMapper.selectMany(ssp).stream()
                .map(MoeGroomingPetDetail::getId)
                .mapToInt(id -> {
                    var updateBean = new MoeGroomingPetDetail();
                    updateBean.setId(id);
                    updateBean.setAssociatedServiceId(targetAssociatedServiceId);
                    return petDetailMapper.updateByPrimaryKeySelective(updateBean);
                })
                .sum();
    }

    static Map<Long, Long> getPetIdToUpdateTime(List<MoeGroomingPetDetail> beforePetDetails, List<Long> petIds) {
        var ids = new HashSet<>(petIds);
        return beforePetDetails.stream()
                .filter(p -> ids.contains(p.getPetId().longValue()))
                .collect(Collectors.groupingBy(
                        petDetail -> petDetail.getPetId().longValue(),
                        Collectors.collectingAndThen(
                                Collectors.mapping(
                                        MoeGroomingPetDetail::getUpdateTime,
                                        Collectors.minBy(Comparator.naturalOrder())),
                                optional -> optional.orElse(DateUtil.get10Timestamp()))));
    }

    @Transactional
    public void upsertAllInOnePetDetail(
            MoeGroomingAppointment beforeAppointment,
            List<MoeGroomingPetDetail> beforePetDetails,
            List<PetDetailDef> petDetailDefs) {
        var petIds = petDetailDefs.stream().map(PetDetailDef::getPetId).toList();
        // 1. 获取 pet 在 appointment 下的顺序
        var petIdToUpdateTime = getPetIdToUpdateTime(beforePetDetails, petIds);
        // 2. 构造 All-in-One 类型的 pet detail list
        var petServiceMap = offeringRemoteService.listService(
                beforeAppointment.getCompanyId(),
                beforeAppointment.getBusinessId().longValue(),
                petDetailDefs);
        var detailDTOList = buildAllInOnePetDetailList(
                beforeAppointment.getCompanyId(),
                beforeAppointment.getBusinessId(),
                petDetailDefs,
                petServiceMap,
                petIdToUpdateTime);
        // 3. 删除 pet 的 pet details 及关联的 service operations, feedings, medications
        deletePetDetails(beforeAppointment.getCompanyId(), Long.valueOf(beforeAppointment.getId()), petIds, false);
        // 4. 保存新的 pet details
        savePetDetails(beforeAppointment, detailDTOList);
    }

    public List<Long> insertAllInOnePetDetail(
            MoeGroomingAppointment appointment, List<MoeGroomingPetDetail> petDetails, PetDetailDef petDetailDef) {
        // 检查 pet_id+service_id 的唯一性
        // 收集 petDetails 中已添加的 service id，检查 petDetailDef中的 services 和 addOns
        List<Long> serviceIds = petDetails.stream()
                .filter(petDetail -> Objects.equals(petDetail.getPetId().longValue(), petDetailDef.getPetId()))
                .map(MoeGroomingPetDetail::getServiceId)
                .map(Long::valueOf)
                .toList();
        petDetailDef.getServicesList().stream()
                .map(SelectedServiceDef::getServiceId)
                .filter(serviceIds::contains)
                .findAny()
                .ifPresent(serviceId -> {
                    throw bizException(Code.CODE_PARAMS_ERROR, "Service id already exists");
                });
        petDetailDef.getAddOnsList().stream()
                .map(SelectedAddOnDef::getAddOnId)
                .filter(serviceIds::contains)
                .findAny()
                .ifPresent(serviceId -> {
                    throw bizException(Code.CODE_PARAMS_ERROR, "Add on service id already exists");
                });
        // 1. 获取当前 pet 在 appointment 下的顺序
        var petIdToUpdateTime = getPetIdToUpdateTime(petDetails, List.of(petDetailDef.getPetId()));
        // 2. 构造 All-in-One 类型的 pet detail list: <petId, <serviceId, service>>
        Map<Long, Map<Long, CustomizedServiceView>> petServiceMap = offeringRemoteService.listService(
                appointment.getCompanyId(), appointment.getBusinessId().longValue(), List.of(petDetailDef));
        List<PetDetailDTO> detailDTOList = buildAllInOnePetDetailList(
                appointment.getCompanyId(),
                appointment.getBusinessId(),
                List.of(petDetailDef),
                petServiceMap,
                petIdToUpdateTime);
        // 4. 保存新的 pet details
        savePetDetails(appointment, detailDTOList);
        // 5. 返回新增 pet detail id
        return detailDTOList.stream()
                .map(PetDetailDTO::getPetDetail)
                .map(MoeGroomingPetDetail::getId)
                .map(Long::valueOf)
                .toList();
    }

    /**
     * Grooming only 情况下，更新其中一个 pet details 的 date&time 时，后续的 pet details 和 service
     * operations 需要做相应的偏移
     *
     * @param groomingPetDetails  所有的 grooming service 详情
     * @param offsetStartDateTime 开始偏移的日期时间
     * @param offsetMinutes       偏移的分钟数
     */
    public void offsetAfterPetDetails(
            List<MoeGroomingPetDetail> groomingPetDetails, LocalDateTime offsetStartDateTime, Long offsetMinutes) {
        if (CollectionUtils.isEmpty(groomingPetDetails)) {
            return;
        }
        Integer groomingId = groomingPetDetails.get(0).getGroomingId();
        Map<Integer, List<MoeGroomingServiceOperation>> serviceOperationsMap =
                serviceOperationService.getServiceOperationList(groomingId.longValue()).stream()
                        .collect(Collectors.groupingBy(MoeGroomingServiceOperation::getGroomingServiceId));
        groomingPetDetails.stream()
                .filter(petDetail -> !PetDetailUtil.buildDateTime(
                                petDetail.getStartDate(),
                                petDetail.getStartTime().intValue())
                        .isBefore(offsetStartDateTime))
                .forEach(petDetail -> {
                    // 偏移 pet detail
                    MoeGroomingPetDetail update = new MoeGroomingPetDetail();
                    update.setId(petDetail.getId());
                    Pair<String, Integer> startDateTime = calculateEndDateAndEndTime(
                            petDetail.getStartDate(), petDetail.getStartTime().intValue(), offsetMinutes.intValue());
                    update.setStartDate(startDateTime.first());
                    update.setStartTime(startDateTime.second().longValue());
                    Pair<String, Integer> endDateTime = calculateEndDateAndEndTime(
                            update.getStartDate(), update.getStartTime().intValue(), petDetail.getServiceTime());
                    update.setEndDate(endDateTime.first());
                    update.setEndTime(endDateTime.second().longValue());
                    update.setUpdateTime(DateUtil.get10Timestamp());
                    update(update);
                    // 偏移 service operation
                    Optional.ofNullable(serviceOperationsMap.get(petDetail.getId()))
                            .ifPresent(operations -> operations.forEach(operation -> {
                                MoeGroomingServiceOperation updateOperation = new MoeGroomingServiceOperation();
                                updateOperation.setId(operation.getId());
                                updateOperation.setStartTime(operation.getStartTime() + offsetMinutes.intValue());
                                updateOperation.setUpdateTime(new Date());
                                serviceOperationService.update(updateOperation);
                            }));
                });
    }

    /**
     * 重新计算 appointment 下指定 pet 的 service 的 price 和 duration 信息
     *
     * @param petDetails            pet details
     * @param serviceOverride       pet service override price and duration
     * @param petDetailOperationMap pet detail operation map
     */
    private void recalculatePetDetailService(
            List<MoeGroomingPetDetail> petDetails,
            PetServiceOverrideDTO serviceOverride,
            Map<Integer, List<MoeGroomingServiceOperation>> petDetailOperationMap) {
        petDetails.stream()
                .filter(petDetail -> {
                    boolean filterService = petDetail.getServiceId().equals(serviceOverride.getServiceId());
                    if (serviceOverride.getPetId() != null) {
                        boolean filterPet = petDetail
                                .getPetId()
                                .equals(serviceOverride.getPetId().intValue());
                        return filterService && filterPet;
                    }
                    return filterService;
                })
                .forEach(petDetail -> {
                    boolean updateDuration = serviceOverride.getDuration() != null
                            && !Objects.equals(petDetail.getServiceTime(), serviceOverride.getDuration());
                    if (updateDuration) {
                        petDetail.setServiceTime(serviceOverride.getDuration());
                        serviceOperationService.reassignOperationTime(
                                serviceOverride.getDuration(),
                                petDetail.getServiceTime(),
                                petDetailOperationMap.get(petDetail.getId()));
                    }
                    boolean updatePrice = serviceOverride.getPrice() != null
                            && !Objects.equals(petDetail.getServicePrice().doubleValue(), serviceOverride.getPrice());
                    if (updatePrice) {
                        petDetail.setServicePrice(BigDecimal.valueOf(serviceOverride.getPrice()));
                        serviceOperationService.reassignOperationPrice(
                                BigDecimal.valueOf(serviceOverride.getPrice()),
                                petDetail.getServicePrice(),
                                petDetailOperationMap.get(petDetail.getId()));
                    }
                });
    }

    private static boolean needUpdateService(ServiceScopeType scopeType) {
        return Objects.equals(scopeType, ServiceScopeType.THIS_AND_FUTURE)
                || Objects.equals(scopeType, ServiceScopeType.ALL_UPCOMING);
    }

    /**
     * 更新 appointment 下指定 pet 指定 service 的 price、duration、start time 和 end time 信息
     *
     * @param appointmentId   appointment id
     * @param serviceOverride pet service override price and duration
     */
    @Transactional
    public void updatePetDetailsWithPetServiceOverride(Long appointmentId, PetServiceOverrideDTO serviceOverride) {
        List<MoeGroomingPetDetail> petDetails = getPetDetailList(appointmentId);
        Map<Integer, List<MoeGroomingServiceOperation>> petDetailOperationMap =
                serviceOperationService.getServiceOperationList(appointmentId).stream()
                        .collect(Collectors.groupingBy(MoeGroomingServiceOperation::getGroomingServiceId));
        // 1. 根据 service override 更新 pet detail 和 operations 的 service time 和 price
        recalculatePetDetailService(petDetails, serviceOverride, petDetailOperationMap);
        // 2. 按照更新后的 service time 重新计算 pet detail 的 start time 和 end time
        recalculatePetDetailStartTime(petDetails);
        // 3. 更新 pet details
        petDetails.forEach(petDetail -> {
            MoeGroomingPetDetail update = new MoeGroomingPetDetail();
            update.setId(petDetail.getId());
            update.setStartTime(petDetail.getStartTime());
            update.setEndTime(petDetail.getEndTime());
            update.setUpdateTime(DateUtil.get10Timestamp());
            update.setServicePrice(petDetail.getServicePrice());
            update.setServiceTime(petDetail.getServiceTime());
            if (needUpdateService(serviceOverride.getPriceScopeType())) {
                update.setScopeTypePrice(ServiceScopeType.ONLY_THIS_VALUE);
            }
            if (needUpdateService(serviceOverride.getDurationScopeType())) {
                update.setScopeTypeTime(ServiceScopeType.ONLY_THIS_VALUE);
            }
            if (serviceOverride.getDurationOverrideType() != null) {
                update.setDurationOverrideType(serviceOverride.getDurationOverrideType());
            }
            if (serviceOverride.getPriceOverrideType() != null) {
                update.setPriceOverrideType(serviceOverride.getPriceOverrideType());
            }
            update(update);
        });
        // 4. 更新 service operations
        petDetailOperationMap.values().stream().flatMap(List::stream).forEach(operation -> {
            MoeGroomingServiceOperation update = new MoeGroomingServiceOperation();
            update.setId(operation.getId());
            update.setPrice(operation.getPrice());
            update.setPriceRatio(operation.getPriceRatio());
            update.setDuration(operation.getDuration());
            update.setStartTime(operation.getStartTime());
            serviceOperationService.update(update);
        });
    }

    /**
     * 更新 appointment 下指定 service 的 price 和 duration 信息
     *
     * @param appointmentId   appointment id
     * @param serviceOverride service override price and duration
     */
    @Transactional
    public void updatePetDetailsWithServiceOverride(Long appointmentId, ServiceOverrideDTO serviceOverride) {
        updatePetDetailsWithPetServiceOverride(appointmentId, serviceOverrideConverter.toDTO(serviceOverride));
    }

    private void recalculatePetDetailStartTime(List<MoeGroomingPetDetail> petDetails) {
        if (PetDetailUtil.isGroomingOnly(petDetails)) {
            GroomingOnlyDTO groomingOnlyDTO = PetDetailUtil.buildGroomingOnlyDTO(petDetails);
            recalculateGroomingOnlyPetDetails(petDetails, groomingOnlyDTO);
        } else {
            petDetails.forEach(petDetail -> {
                Pair<String, Integer> endPair = calculateEndDateAndEndTime(
                        petDetail.getStartDate(), petDetail.getStartTime().intValue(), petDetail.getServiceTime());
                petDetail.setEndDate(endPair.first());
                petDetail.setEndTime(endPair.second().longValue());
            });
        }
    }

    @Transactional
    public void recalculatePetDetailStartTime(Long appointmentId, boolean allPetsStartAtSameTime) {
        List<MoeGroomingPetDetail> petDetails = getPetDetailList(appointmentId);
        GroomingOnlyDTO groomingOnlyDTO = PetDetailUtil.buildGroomingOnlyDTO(petDetails);
        groomingOnlyDTO.setAllPetsStartAtSameTime(allPetsStartAtSameTime);
        // 1. 重新计算 pet detail 的 start time 和 end time
        recalculateGroomingOnlyPetDetails(petDetails, groomingOnlyDTO);
        // 2. 更新 pet details
        updatePetDetailById(petDetails);
        // 3. 更新 service operations
        serviceOperationService.recalculateOperationStartTime(appointmentId, petDetails);
    }

    /**
     * Get PetDetail by id.
     *
     * @param id pet detail id
     * @return pet detail
     */
    @Nullable
    public MoeGroomingPetDetail get(long id) {
        return petDetailMapper
                .selectOne(c -> c.where(moeGroomingPetDetail.id, isEqualTo((int) id))
                        .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE)))
                .orElse(null);
    }

    public List<MoeGroomingPetDetail> batchGet(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return petDetailMapper.select(c -> c.where(moeGroomingPetDetail.id, isIn(ids))
                .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE)));
    }

    public MoeGroomingPetDetail mustGet(long id) {
        return petDetailMapper
                .selectOne(c -> c.where(moeGroomingPetDetail.id, isEqualTo((int) id))
                        .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE)))
                .orElseThrow(() -> bizException(Code.CODE_APPOINTMENT_PET_NOT_EXIST));
    }

    public List<MoeGroomingPetDetail> getByAssociatedId(int appointmentId, long associatedId) {
        return petDetailMapper.select(c -> c.where(moeGroomingPetDetail.groomingId, isEqualTo(appointmentId))
                .and(moeGroomingPetDetail.associatedServiceId, isEqualTo(associatedId))
                .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE)));
    }

    @Transactional
    public void autoRolloverServiceUpdate(
            CustomizedServiceView targetService,
            MoeGroomingPetDetail servicePetDetail,
            List<MoeGroomingPetDetail> associatedAddOns) {
        var updateBean = new MoeGroomingPetDetail();
        updateBean.setId(Math.toIntExact(servicePetDetail.getId()));
        updateBean.setServiceId(Math.toIntExact(targetService.getId()));
        updateBean.setServicePrice(BigDecimal.valueOf(targetService.getPrice()));
        int serviceTime = targetService.getMaxDuration();
        // 结束时间不能超过 23:59
        if (servicePetDetail.getStartTime() + serviceTime > END_TIME_OF_DAY) {
            serviceTime = END_TIME_OF_DAY - servicePetDetail.getStartTime().intValue();
        }
        updateBean.setServiceTime(serviceTime);
        updateBean.setEndTime(servicePetDetail.getStartTime() + serviceTime);

        update(updateBean);

        updateAssociatedServiceId(
                associatedAddOns.stream().map(MoeGroomingPetDetail::getId).toList(), targetService.getId());
    }

    // 修复缺失的 service type 字段
    public void fixServiceType(Long companyId, Collection<MoeGroomingPetDetail> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return;
        }
        List<Long> toFixServiceIds = petDetails.stream()
                .filter(Objects::nonNull)
                .filter(petDetail -> !PetDetailUtil.isServiceTypeNormal(petDetail.getServiceType()))
                .map(MoeGroomingPetDetail::getServiceId)
                .filter(Objects::nonNull)
                .distinct()
                .map(Integer::longValue)
                .toList();
        if (CollectionUtils.isEmpty(toFixServiceIds)) {
            return;
        }
        Map<Integer, ServiceBriefView> serviceInfo =
                offeringRemoteService.getServiceModels(companyId, toFixServiceIds).stream()
                        .collect(Collectors.toMap(k -> (int) k.getId(), Function.identity(), (p1, p2) -> p1));

        for (MoeGroomingPetDetail petDetail : petDetails) {
            if (!PetDetailUtil.isServiceTypeNormal(petDetail.getServiceType())) {
                if (serviceInfo.containsKey(petDetail.getServiceId())) {
                    petDetail.setServiceType(
                            serviceInfo.get(petDetail.getServiceId()).getType().getNumber());
                }
            }
        }
    }

    /**
     * 计算逻辑： Boarding: startDate + startTime -> endDate + endTime Daycare: 单天
     * startDate + startTime -> startDate + endTime；多天 minStartDate + startTime ->
     * maxStartDate + endTime； Grooming: startDate + startTime -> startDate +
     * endTime Evaluation: startDate + startTime -> startDate + endTime
     * MoeGroomingPetDetail 参与计算的字段有：
     * startDate、specificDates、endDate、startTime、endTime、petId、serviceType、serviceId、associatedServiceId、staffId
     * EvaluationServiceDetail 参与计算的字段有： startDate、endDate、startTime、endTime
     *
     * @param petDetails selected pet and service details
     * @return appointment start and end datetime period
     */
    public Pair<LocalDateTime, LocalDateTime> calculatePeriod(
            Long companyId, List<MoeGroomingPetDetail> petDetails, List<EvaluationServiceDetail> evaluations) {
        fixServiceType(companyId, petDetails);

        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;
        if (!CollectionUtils.isEmpty(petDetails)) {
            for (MoeGroomingPetDetail petDetail : petDetails) {
                if (isAttachedService(petDetail)) {
                    continue;
                }
                var minMaxDate = calculateMinMaxDate(petDetail);
                String startDate = minMaxDate.first();
                if (StringUtils.hasText(startDate) && petDetail.getStartTime() != null) {
                    LocalDateTime cur = PetDetailUtil.buildDateTime(
                            startDate, petDetail.getStartTime().intValue());
                    if (startDateTime == null || startDateTime.isAfter(cur)) {
                        startDateTime = cur;
                    }
                }
                String endDate = minMaxDate.second();
                if (StringUtils.hasText(endDate) && petDetail.getEndTime() != null) {
                    LocalDateTime cur = PetDetailUtil.buildDateTime(
                            endDate, petDetail.getEndTime().intValue());
                    if (endDateTime == null || endDateTime.isBefore(cur)) {
                        endDateTime = cur;
                    }
                }
            }
        }

        if (!CollectionUtils.isEmpty(evaluations)) {
            for (EvaluationServiceDetail evaluationServiceDetail : evaluations) {
                LocalDateTime cur = PetDetailUtil.buildDateTime(
                        evaluationServiceDetail.getStartDate().toString(), evaluationServiceDetail.getStartTime());
                if (startDateTime == null || startDateTime.isAfter(cur)) {
                    startDateTime = cur;
                }
                cur = PetDetailUtil.buildDateTime(
                        evaluationServiceDetail.getEndDate().toString(), evaluationServiceDetail.getEndTime());
                if (endDateTime == null || endDateTime.isBefore(cur)) {
                    endDateTime = cur;
                }
            }
        }

        if (startDateTime == null || endDateTime == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Pet details must be specified");
        }
        return Pair.of(startDateTime, endDateTime);
    }

    /*
     * 判断是否是附属的服务：
     * 1. serviceType 为 add on，且没有 staff id
     * 2. date type 为 everyday、everyday except checkin day、everyday include checkout day
     */
    private static boolean isAttachedService(MoeGroomingPetDetail petDetail) {
        if (Objects.equals(ServiceType.ADDON_VALUE, petDetail.getServiceType())
                && (petDetail.getStaffId() == null || petDetail.getStaffId() == 0)) {
            return true;
        }

        if (Objects.equals(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE, petDetail.getDateType())
                || Objects.equals(
                        PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY_VALUE, petDetail.getDateType())
                || Objects.equals(
                        PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY_VALUE,
                        petDetail.getDateType())) {
            return true;
        }

        return false;
    }

    private Pair<String, String> calculateMinMaxDate(MoeGroomingPetDetail petDetail) {
        // date point 类型，直接返回 startDate 和 endDate
        if (Objects.equals(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE, petDetail.getDateType())) {
            return Pair.of(petDetail.getStartDate(), petDetail.getEndDate());
        }

        // specific date 类型，返回 startDate 和 endDate 中的最小值和最大值
        String minDate = petDetail.getStartDate();
        String maxDate = petDetail.getEndDate();
        PetDetailDateType dateType = PetDetailUtil.getDateType(petDetail);
        if (PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE.equals(dateType)) {
            for (String date : JsonUtil.toList(petDetail.getSpecificDates(), String.class)) {
                minDate = !(StringUtils.hasText(minDate)) || minDate.compareTo(date) > 0 ? date : minDate;
                maxDate = !(StringUtils.hasText(maxDate)) || maxDate.compareTo(date) < 0 ? date : maxDate;
            }
        }
        return Pair.of(minDate, maxDate);
    }

    /**
     * 将 pet detail 的 everyday 解析为静态的startDate 和 endDate
     *
     * @param petDetails 一个预约下所有的 pet details. serviceType 字段需要被正确赋值
     * @return 入参的拷贝，返回参数的编辑不影响入参 petDetails
     */
    public List<MoeGroomingPetDetail> getWithActualDatesInfo(List<MoeGroomingPetDetail> petDetails) {
        if (petDetails == null) {
            return null;
        }
        List<MoeGroomingPetDetail> result = new ArrayList<>();
        Map<Integer, List<MoeGroomingPetDetail>> appointmentPetDetailsMap =
                petDetails.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getGroomingId));
        appointmentPetDetailsMap.forEach((appointmentId, appointmentPetDetails) -> {
            Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap =
                    PetDetailUtil.getPetServiceMap(appointmentPetDetails);
            appointmentPetDetails.forEach(curPetDetail -> {
                MoeGroomingPetDetail petDetailWithActualDatesInfo =
                        PetDetailConverter.INSTANCE.entityToEntity(curPetDetail);
                PetDetailDateType dateType = PetDetailUtil.getDateType(curPetDetail);
                if (Objects.equals(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT, dateType)
                        || Objects.equals(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE, dateType)) {
                    result.add(petDetailWithActualDatesInfo);
                    return;
                }

                MoeGroomingPetDetail associatedService = PetDetailUtil.getAssociatedService(
                        curPetDetail, petServiceMap.getOrDefault(curPetDetail.getPetId(), Map.of()));
                if (associatedService == null) {
                    log.error("Associated service not found, appointment id: [{}]", curPetDetail.getGroomingId());
                    throw bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
                }
                petDetailWithActualDatesInfo.setStartDate(associatedService.getStartDate());
                petDetailWithActualDatesInfo.setEndDate(associatedService.getEndDate());
                if (PetDetailDateType.PET_DETAIL_DATE_EVERYDAY.equals(dateType)) {
                    // when exclude checkout day, end date should be one day before
                    var endDate = LocalDate.parse(associatedService.getEndDate());
                    petDetailWithActualDatesInfo.setEndDate(endDate.minusDays(1).toString());
                }
                result.add(petDetailWithActualDatesInfo);
            });
        });

        return result;
    }

    /**
     * 重新计算 grooming only 的 appointment pet details 的 start time 和 end time
     *
     * @param petDetails      grooming only pet details
     * @param groomingOnlyDTO grooming only params
     */
    private static void recalculateGroomingOnlyPetDetails(
            List<MoeGroomingPetDetail> petDetails, GroomingOnlyDTO groomingOnlyDTO) {
        petDetails.stream()
                .collect(Collectors.groupingBy(MoeGroomingPetDetail::getPetId))
                .forEach((petId, petDetailList) -> {
                    petDetailList.stream()
                            .sorted(Comparator.comparing(p -> PetDetailUtil.buildDateTime(
                                    p.getStartDate(), p.getStartTime().intValue())))
                            .forEach(petDetail -> {
                                LocalDateTime start = groomingOnlyDTO.getPetServiceDateTime();
                                LocalDateTime end = recalculateGroomingOnlyPetDetail(petDetail, start);
                                groomingOnlyDTO.setPetServiceDateTime(end);
                            });
                    // all pets start at same time 时重置 pet start time
                    if (groomingOnlyDTO.isAllPetsStartAtSameTime()) {
                        groomingOnlyDTO.setPetServiceDateTime(groomingOnlyDTO.getServiceDateTime());
                    }
                });
    }

    private static LocalDateTime recalculateGroomingOnlyPetDetail(MoeGroomingPetDetail petDetail, LocalDateTime start) {
        petDetail.setStartDate(start.toLocalDate().toString());
        petDetail.setStartTime((long) (start.toLocalTime().toSecondOfDay() / 60));
        LocalDateTime end = start.plusMinutes(petDetail.getServiceTime());
        petDetail.setEndDate(end.toLocalDate().toString());
        petDetail.setEndTime((long) (end.toLocalTime().toSecondOfDay() / 60));
        return end;
    }

    public List<MoeGroomingPetDetail> getPetLastPetDetail(
            long companyId,
            Collection<Long> customerIds,
            Collection<Long> petIds,
            GetLastPetDetailRequest.Filter filter) {
        if (CollectionUtils.isEmpty(customerIds) || CollectionUtils.isEmpty(petIds)) {
            return List.of();
        }

        /*
         * select * from (select pd.*, row_number() over (partition by pd.pet_id,
         * pd.service_id order by appointment_end_date DESC, appointment_end_time DESC)
         * as rn from moe_grooming_appointment a left join moe_grooming_pet_detail pd on
         * a.id = pd.grooming_id where company_id = ... and is_waiting_list = 0 and
         * is_deprecate = false and is_block = 2 and a.status in (...) and customer_id
         * in (...) and (appointment_end_date < '...' or (appointment_end_date = '...'
         * and appointment_end_time <= ...)) and pd.status = 1 and pd.pet_id in (...)
         * and pd.service_id in (...) ) temp where temp.rn = 1;
         */

        List<AndOrCriteriaGroup> andOrCriteriaGroups = buildFilterClause(companyId, filter);

        DerivedColumn<Integer> rowNum = DerivedColumn.of(
                "row_number() over (partition by pd.pet_id order by appointment_end_date DESC, appointment_end_time DESC)");
        if (filter.getServiceIdsCount() > 0) {
            rowNum = DerivedColumn.of(
                    "row_number() over (partition by pd.pet_id, pd.service_id order by appointment_end_date DESC, appointment_end_time DESC)");
        }
        BasicColumn[] selectList = Stream.concat(
                        Stream.of(rowNum.as("rn")), Stream.of(MoeGroomingPetDetailMapper.selectList))
                .toArray(BasicColumn[]::new);

        SelectStatementProvider selectStatement = select(MoeGroomingPetDetailMapper.selectList)
                .from(
                        select(selectList)
                                .from(moeGroomingAppointment)
                                .join(
                                        moeGroomingPetDetail,
                                        "pd",
                                        on(moeGroomingAppointment.id, equalTo(moeGroomingPetDetail.groomingId)))
                                .where(MoeGroomingAppointmentDynamicSqlSupport.companyId, isEqualTo(companyId))
                                .and(isWaitingList, isEqualTo(CommonConstant.DISABLE))
                                .and(isDeprecate, isFalse())
                                .and(isBlock, isEqualTo(CommonConstant.DELETED))
                                .and(
                                        customerId,
                                        isIn(customerIds.stream()
                                                .map(Long::intValue)
                                                .toList()))
                                .and(
                                        petId,
                                        isIn(petIds.stream().map(Long::intValue).toList()))
                                .and(status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE))
                                .and(andOrCriteriaGroups),
                        "temp")
                .where(DerivedColumn.of("rn"), isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return petDetailMapper.selectMany(selectStatement);
    }

    private List<AndOrCriteriaGroup> buildFilterClause(Long companyId, GetLastPetDetailRequest.Filter filter) {
        List<AndOrCriteriaGroup> filters = new ArrayList<>();

        if (filter.hasBusinessId()) {
            filters.add(and(businessId, isEqualTo(Math.toIntExact(filter.getBusinessId()))));
        }

        List<AppointmentStatus> statusList = filter.getStatusList();
        if (CollectionUtils.isEmpty(statusList)) {
            filters.add(and(MoeGroomingAppointmentDynamicSqlSupport.status, isIn(ACTIVE_STATUS_VALUE_SET)));
        } else {
            filters.add(and(
                    MoeGroomingAppointmentDynamicSqlSupport.status,
                    isIn(statusList.stream()
                            .map(AppointmentStatus::getNumber)
                            .map(Integer::byteValue)
                            .toList())));
        }

        String timezoneName = companyRemoteService.getTimezoneName(companyId);
        String date = DateUtil.convertLocalDateToDateString(LocalDateTime.now(), timezoneName, "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(timezoneName);
        if (filter.hasStartTimeRange() || filter.hasEndTimeRange()) {
            var startTimeFilter = CriteriaUtils.buildDateTimeFilter(
                    appointmentDate, appointmentStartTime, timezoneName, filter.getStartTimeRange());
            filters.addAll(startTimeFilter);
            var endTimeFilter = CriteriaUtils.buildDateTimeFilter(
                    appointmentEndDate, appointmentEndTime, timezoneName, filter.getEndTimeRange());
            filters.addAll(endTimeFilter);
        } else {
            filters.add(and(
                    appointmentEndDate,
                    isLessThan(date),
                    or(appointmentEndDate, isEqualTo(date), and(appointmentEndTime, isLessThanOrEqualTo(nowMinutes)))));
        }

        if (!CollectionUtils.isEmpty(filter.getServiceItemTypesList())) {
            filters.add(
                    and(serviceTypeInclude, isIn(serviceItemTypesToBitValueList(filter.getServiceItemTypesList()))));
        } else {
            filters.add(and(
                    serviceTypeInclude,
                    isIn(serviceItemTypesToBitValueList(List.of(
                            ServiceItemType.GROOMING,
                            ServiceItemType.BOARDING,
                            ServiceItemType.DAYCARE,
                            ServiceItemType.DOG_WALKING)))));
        }

        if (filter.hasFilterNoStartTime() && filter.getFilterNoStartTime()) {
            filters.add(and(noStartTime, isFalse()));
        }

        if (filter.hasFilterBookingRequest() && filter.getFilterBookingRequest()) {
            filters.add(and(bookOnlineStatus, isEqualTo(CommonConstant.DISABLE)));
        }

        List<Long> serviceIds = filter.getServiceIdsList();
        if (!CollectionUtils.isEmpty(serviceIds)) {
            filters.add(and(
                    MoeGroomingPetDetailDynamicSqlSupport.serviceId,
                    isIn(serviceIds.stream().map(Long::intValue).toList())));
        }

        return filters;
    }

    public List<Integer> serviceItemTypesToBitValueList(List<ServiceItemType> serviceItemTypesList) {
        List<Integer> serviceItemList =
                serviceItemTypesList.stream().map(ServiceItemType::getNumber).toList();
        return ServiceItemEnum.convertServiceItemListToBitValueList(serviceItemList);
    }

    public boolean isBelongsToAppointment(long appointmentId, List<Integer> petDetailIds) {
        if (CollectionUtils.isEmpty(petDetailIds)) {
            return true;
        }
        var existPetDetailIds = getPetDetailList(appointmentId).stream()
                .map(MoeGroomingPetDetail::getId)
                .collect(Collectors.toSet());
        return existPetDetailIds.containsAll(petDetailIds);
    }

    /**
     * Batch update pet details by id.
     *
     * @param petDetails pet details
     * @return affected rows
     */
    public int batchUpdate(List<MoeGroomingPetDetail> petDetails) {
        if (ObjectUtils.isEmpty(petDetails)) {
            return 0;
        }

        var petDetailIds = petDetails.stream()
                .map(MoeGroomingPetDetail::getId)
                .filter(CommonUtil::isNormal)
                .collect(Collectors.toSet());
        if (petDetailIds.isEmpty()) {
            return 0;
        }

        var result = 0;
        for (var petDetail : sortPetDetailsForUpdate(petDetails, listByIds(petDetailIds))) {
            result += update(petDetail, false);
        }

        return result;
    }

    private List<MoeGroomingPetDetail> listByIds(Collection<Integer> petDetailIds) {
        if (ObjectUtils.isEmpty(petDetailIds)) {
            return List.of();
        }
        return petDetailMapper.select(c -> c.where(moeGroomingPetDetail.id, isIn(petDetailIds))
                .and(moeGroomingPetDetail.status, isEqualTo((byte) PetDetailStatus.NORMAL_VALUE)));
    }

    private static List<MoeGroomingPetDetail> sortPetDetailsForUpdate(
            List<MoeGroomingPetDetail> updatePetDetails, List<MoeGroomingPetDetail> beforePetDetails) {
        var petDetailMap = beforePetDetails.stream()
                .collect(Collectors.toMap(MoeGroomingPetDetail::getId, Function.identity(), (a, b) -> a));

        return updatePetDetails.stream()
                .sorted(Comparator.comparingInt(updatePetDetail -> {
                    var petDetail = petDetailMap.get(updatePetDetail.getId());
                    return petDetail != null ? getSortScoreForPetDetail(petDetail) : 0;
                }))
                .toList();
    }

    private static int getSortScoreForPetDetail(MoeGroomingPetDetail petDetail) {
        return getSortScoreForServiceItemType(getServiceItemType(petDetail.getServiceItemType()));
    }

    private static int getSortScoreForServiceItemType(ServiceItemType serviceItemType) {
        // 在批量更新 pet detail 时，时间跨度小的要先更新，因为在更新 boarding 时，可能会把挂在上面的 service 删掉。
        // 示例：
        // before: BOARDING(0101-0105)、GROOMING(0105)
        // after: BOARDING(0111-0115)、GROOMING(0115)
        // 这种情况下必须先更新 grooming，否则 grooming 会被删掉
        return switch (serviceItemType) {
            case SERVICE_ITEM_TYPE_UNSPECIFIED, UNRECOGNIZED -> 0; // should not happen
            case GROOMING, GROUP_CLASS, DOG_WALKING, EVALUATION -> 10;
            case DAYCARE -> 20;
            case BOARDING -> 30;
        };
    }
}
