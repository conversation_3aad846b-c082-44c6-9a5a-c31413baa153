package com.moego.svc.appointment.service;

import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.lib.common.auth.AuthContext;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.dto.GroomingOnlyDTO;
import com.moego.svc.appointment.dto.PetDetailDTO;
import com.moego.svc.appointment.dto.PetServiceOverrideDTO;
import com.moego.svc.appointment.dto.ServiceOverrideDTO;
import com.moego.svc.appointment.utils.Pair;
import jakarta.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * PetDetailServiceProxy - 代理类用于白名单和非白名单商家的分流调用
 * 白名单商家使用 fulfillment 远程调用，非白名单商家使用原有的 service 调用
 *
 * <AUTHOR>
 * @since 2025/7/11
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PetDetailServiceProxy {

    private final PetDetailService petDetailService;
    // TODO: 注入 fulfillment 远程服务
    // private final FulfillmentPetDetailService fulfillmentPetDetailService;

    /**
     * 判断是否为白名单商家，使用新的 fulfillment flow
     * 直接从 AuthContext 获取 companyId
     * TODO: 实现具体的白名单判断逻辑
     *
     * @return true if company is in fulfillment flow whitelist
     */
    private boolean isFulfillmentFlow() {
        Long companyId = AuthContext.get().companyId();
        if (companyId == null) {
            log.warn("CompanyId is null in AuthContext, using original service");
            return false;
        }
        // TODO: 实现白名单判断逻辑
        return false;
    }

    // ==================== 基础方法 ====================

    public long insert(MoeGroomingPetDetail petDetail) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for insert, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.insert(petDetail);
    }

    public int delete(long id, boolean shouldUpdateOrder) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for delete, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.delete(id, shouldUpdateOrder);
    }

    public int delete(long id) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for delete, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.delete(id);
    }

    public int deleteByPetIds(Long appointmentId, List<Long> petIds, boolean shouldUpdateOrder) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for deleteByPetIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.deleteByPetIds(appointmentId, petIds, shouldUpdateOrder);
    }

    public int deletePetDetails(Long companyId, Long appointmentId, List<Long> petIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for deletePetDetails, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.deletePetDetails(companyId, appointmentId, petIds);
    }

    public List<PetDetailDTO> buildAllInOnePetDetailList(
            long companyId,
            long businessId,
            List<PetDetailDef> petDetailDefs,
            Map<Long, Map<Long, CustomizedServiceView>> petServiceMap,
            Map<Long, Long> petIdToUpdateTime) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for buildAllInOnePetDetailList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.buildAllInOnePetDetailList(
                companyId, businessId, petDetailDefs, petServiceMap, petIdToUpdateTime);
    }

    public PetDetailDTO buildPetDetailDTO(
            String appointmentStartDate,
            String appointmentEndDate,
            Long petId,
            SelectedServiceDef selectedServiceDef,
            CustomizedServiceView service) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for buildPetDetailDTO, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.buildPetDetailDTO(
                appointmentStartDate, appointmentEndDate, petId, selectedServiceDef, service);
    }

    public PetDetailDTO buildAddOnPetDetailDTO(
            String appointmentStartDate,
            String appointmentEndDate,
            Long petId,
            SelectedAddOnDef selectedAddOnDef,
            CustomizedServiceView service) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for buildAddOnPetDetailDTO, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.buildAddOnPetDetailDTO(
                appointmentStartDate, appointmentEndDate, petId, selectedAddOnDef, service);
    }

    @Deprecated
    public List<PetDetailDTO> buildGroomingOnlyPetDetailList(
            List<PetDetailDef> petDetailDefs,
            Map<Long, Map<Long, CustomizedServiceView>> petServiceMap,
            GroomingOnlyDTO groomingOnlyDTO) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for buildGroomingOnlyPetDetailList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.buildGroomingOnlyPetDetailList(petDetailDefs, petServiceMap, groomingOnlyDTO);
    }

    public List<MoeGroomingPetDetail> getPetDetailList(Long appointmentId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getPetDetailList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.getPetDetailList(appointmentId);
    }

    public List<MoeGroomingPetDetail> getPetDetailList(long appointmentId, long petId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getPetDetailList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.getPetDetailList(appointmentId, petId);
    }

    public List<MoeGroomingPetDetail> getPetDetailList(long appointmentId, List<Long> petIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getPetDetailList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.getPetDetailList(appointmentId, petIds);
    }

    public Map<Long, List<MoeGroomingPetDetail>> getPetDetailsByAppointmentIds(List<Long> appointmentIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getPetDetailsByAppointmentIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.getPetDetailsByAppointmentIds(appointmentIds);
    }

    public List<Integer> getStaffIds(Long appointmentId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getStaffIds, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.getStaffIds(appointmentId);
    }

    public int savePetDetails(MoeGroomingAppointment appointment, List<PetDetailDTO> detailDTOList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for savePetDetails, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.savePetDetails(appointment, detailDTOList);
    }

    public int update(MoeGroomingPetDetail petDetail) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for update, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.update(petDetail);
    }

    public int batchUpdate(List<MoeGroomingPetDetail> petDetails) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for batchUpdate, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.batchUpdate(petDetails);
    }

    public int update(MoeGroomingPetDetail petDetail, boolean shouldUpdateOrder) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for update, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.update(petDetail, shouldUpdateOrder);
    }

    public int updatePetDetailById(List<MoeGroomingPetDetail> petDetails) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for updatePetDetailById, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.updatePetDetailById(petDetails);
    }

    public List<MoeGroomingPetDetail> getPetDetailList(List<Long> appointmentIdList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getPetDetailList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.getPetDetailList(appointmentIdList);
    }

    public int updateLodging(List<Integer> ids, Long targetLodgingId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for updateLodging, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.updateLodging(ids, targetLodgingId);
    }

    public void upsertAllInOnePetDetail(
            MoeGroomingAppointment beforeAppointment,
            List<MoeGroomingPetDetail> beforePetDetails,
            List<PetDetailDef> petDetailDefs) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for upsertAllInOnePetDetail, companyId: {}",
                    AuthContext.get().companyId());
            return;
        }
        petDetailService.upsertAllInOnePetDetail(beforeAppointment, beforePetDetails, petDetailDefs);
    }

    public List<Long> insertAllInOnePetDetail(
            MoeGroomingAppointment appointment, List<MoeGroomingPetDetail> petDetails, PetDetailDef petDetailDef) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for insertAllInOnePetDetail, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.insertAllInOnePetDetail(appointment, petDetails, petDetailDef);
    }

    public void offsetAfterPetDetails(
            List<MoeGroomingPetDetail> groomingPetDetails, LocalDateTime offsetStartDateTime, Long offsetMinutes) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for offsetAfterPetDetails, companyId: {}",
                    AuthContext.get().companyId());
            return;
        }
        petDetailService.offsetAfterPetDetails(groomingPetDetails, offsetStartDateTime, offsetMinutes);
    }

    public void updatePetDetailsWithPetServiceOverride(Long appointmentId, PetServiceOverrideDTO serviceOverride) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for updatePetDetailsWithPetServiceOverride, companyId: {}",
                    AuthContext.get().companyId());
            return;
        }
        petDetailService.updatePetDetailsWithPetServiceOverride(appointmentId, serviceOverride);
    }

    public void updatePetDetailsWithServiceOverride(Long appointmentId, ServiceOverrideDTO serviceOverride) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for updatePetDetailsWithServiceOverride, companyId: {}",
                    AuthContext.get().companyId());
            return;
        }
        petDetailService.updatePetDetailsWithServiceOverride(appointmentId, serviceOverride);
    }

    public void recalculatePetDetailStartTime(Long appointmentId, boolean allPetsStartAtSameTime) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for recalculatePetDetailStartTime, companyId: {}",
                    AuthContext.get().companyId());
            return;
        }
        petDetailService.recalculatePetDetailStartTime(appointmentId, allPetsStartAtSameTime);
    }

    @Nullable
    public MoeGroomingPetDetail get(long id) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for get, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.get(id);
    }

    public List<MoeGroomingPetDetail> batchGet(Collection<Integer> ids) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for batchGet, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.batchGet(ids);
    }

    public MoeGroomingPetDetail mustGet(long id) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for mustGet, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.mustGet(id);
    }

    public List<MoeGroomingPetDetail> getByAssociatedId(int appointmentId, long associatedId) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getByAssociatedId, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.getByAssociatedId(appointmentId, associatedId);
    }

    public void autoRolloverServiceUpdate(
            CustomizedServiceView targetService,
            MoeGroomingPetDetail servicePetDetail,
            List<MoeGroomingPetDetail> associatedAddOns) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for autoRolloverServiceUpdate, companyId: {}",
                    AuthContext.get().companyId());
            return;
        }
        petDetailService.autoRolloverServiceUpdate(targetService, servicePetDetail, associatedAddOns);
    }

    public void fixServiceType(Long companyId, Collection<MoeGroomingPetDetail> petDetails) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for fixServiceType, companyId: {}",
                    AuthContext.get().companyId());
            return;
        }
        petDetailService.fixServiceType(companyId, petDetails);
    }

    public Pair<LocalDateTime, LocalDateTime> calculatePeriod(
            Long companyId,
            List<MoeGroomingPetDetail> petDetails,
            List<com.moego.svc.appointment.domain.EvaluationServiceDetail> evaluations) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for calculatePeriod, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.calculatePeriod(companyId, petDetails, evaluations);
    }

    public List<MoeGroomingPetDetail> getWithActualDatesInfo(List<MoeGroomingPetDetail> petDetails) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getWithActualDatesInfo, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.getWithActualDatesInfo(petDetails);
    }

    public List<MoeGroomingPetDetail> getPetLastPetDetail(
            long companyId,
            Collection<Long> customerIds,
            Collection<Long> petIds,
            com.moego.idl.service.appointment.v1.GetLastPetDetailRequest.Filter filter) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for getPetLastPetDetail, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.getPetLastPetDetail(companyId, customerIds, petIds, filter);
    }

    public List<Integer> serviceItemTypesToBitValueList(List<ServiceItemType> serviceItemTypesList) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for serviceItemTypesToBitValueList, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.serviceItemTypesToBitValueList(serviceItemTypesList);
    }

    public boolean isBelongsToAppointment(long appointmentId, List<Integer> petDetailIds) {
        if (isFulfillmentFlow()) {
            // TODO: 调用 fulfillment 远程服务
            log.info(
                    "Using fulfillment flow for isBelongsToAppointment, companyId: {}",
                    AuthContext.get().companyId());
        }
        return petDetailService.isBelongsToAppointment(appointmentId, petDetailIds);
    }
}
