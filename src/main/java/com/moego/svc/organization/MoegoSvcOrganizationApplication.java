package com.moego.svc.organization;

import static java.time.ZoneOffset.UTC;

import java.util.TimeZone;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableFeignClients({
    "com.moego.server.grooming.client",
    "com.moego.server.retail.client",
    "com.moego.server.message.client",
    "com.moego.server.payment.client",
    "com.moego.server.business.client",
    "com.moego.api.thirdparty",
})
@SpringBootApplication
@MapperScan("com.moego.svc.organization.mapper")
@ConfigurationPropertiesScan
public class MoegoSvcOrganizationApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone(UTC));

        SpringApplication.run(MoegoSvcOrganizationApplication.class, args);
    }
}
