spring:
  application:
    name: moego-svc-organization
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:local}
  freemarker:
    prefer-file-system-access: false

mybatis:
  mapper-locations:
    - classpath:mapper/*.xml
    - classpath:mapper/base/*.xml
  type-aliases-package: com.moego.svc.organization.entity
  configuration:
    cache-enabled: false
    local-cache-scope: statement

log:
  path: ${LOG_PATH:./logs}

business:
  register:
    inform:
      email: <EMAIL>

moego:
  grpc:
    server:
      empty-server-enabled: true
      debug-enabled: true
      port: 9090
    client:
      stubs:
        - service: moego.service.account.v1.*
          authority: moego-svc-account:9090
        - service: moego.service.sms.v1.*
          authority: moego-svc-sms:9090
        - service: moego.service.business_customer.v1.*
          authority: moego-svc-business-customer:9090
        - service: moego.service.metadata.v1.*
          authority: moego-svc-metadata:9090
        - service: moego.service.permission.v1.*
          authority: moego-svc-permission:9090
        - service: moego.service.order.v1.*
          authority: moego-svc-order:9090
        - service: moego.service.agreement.v1.*
          authority: moego-svc-agreement:9090
        - service: moego.service.offering.v1.*
          authority: moego-svc-offering:9090
        - service: moego.service.message.v1.**
          authority: moego-svc-message:9090
        - service: moego.service.auto_message.v1.**
          authority: moego-svc-auto-message:9090
        - service: moego.service.enterprise.v1.**
          authority: moego-svc-enterprise:9090
        - service: moego.service.organization.**
          authority: moego-svc-organization:9090
        - service: moego.service.file.v2.*
          authority: moego-svc-file:9090
        - service: moego.service.file.v2.*
          authority: moego-svc-file:9090
        - service: moego.service.online_booking.v1.*
          authority: moego-svc-online-booking:9090
  server:
    url:
      grooming: ${GROOMING_SERVER_URL:http://moego-service-grooming:9206}
      retail: ${RETAIL_SERVER_URL:http://moego-service-retail:9207}
      message: ${MESSAGE_SERVER_URL:http://moego-service-message:9205}
      payment: ${PAYMENT_SERVER_URL:http://moego-service-payment:9204}
      business: moego-service-business:9203
  session:
    company-is-migrate-metadata-key-name: new_account_structure

pagehelper:
  reasonable: false
  defaultCount: true

hubspot:
  authToken: ********************************************
