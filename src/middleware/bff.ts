import {
  createFileClient,
  createLeadsClient,
  createOrderClient,
  createBookClient,
  createOpenPlatformClient,
  createSalesClient,
  createPaymentClient,
  type ZodError,
  createFulfillmentClient,
  createTimeslotClient,
  createTempClient,
  createDataMigrationClient,
} from '@moego/bff-openapi';
import { http } from './api';
import { captureException } from '@sentry/browser';

export const BFFFileClient = createFileClient(http.bff);

// for test
export const BFFBookClient = createBookClient(http.bff);

// BFF validation error handlers
const createBFFErrorHandler = (clientName: string) => ({
  onValidateRequestError: (error: ZodError) => {
    if (__PRO__) {
      // 生产环境：放行但上报 Sentry
      captureException(new Error(`BFF ${clientName} Request Validation Error`), {
        tags: { component: 'bff-client', client: clientName, type: 'request-validation' },
        extra: { error, timestamp: new Date().toISOString() },
      });
      return true; // 放行
    } else {
      // 开发环境：不放行
      console.error(`🚨 BFF ${clientName} Request Validation Error:`, error);
      return false; // 不放行
    }
  },
  onValidateResponseError: (error: ZodError) => {
    if (__PRO__) {
      // 生产环境：放行但上报 Sentry
      captureException(new Error(`BFF ${clientName} Response Validation Error`), {
        tags: { component: 'bff-client', client: clientName, type: 'response-validation' },
        extra: { error, timestamp: new Date().toISOString() },
      });
      return true; // 放行
    } else {
      // 开发环境：不放行
      console.error(`🚨 BFF ${clientName} Response Validation Error:`, error);
      return false; // 不放行
    }
  },
});

export const BffLeadsClient = createLeadsClient(http.bff, createBFFErrorHandler('Leads'));

export const BFFOrderClient = createOrderClient(http.bff, createBFFErrorHandler('Order'));

export const BFFOpenPlatformClient = createOpenPlatformClient(http.bff, createBFFErrorHandler('OpenPlatform'));

export const BFFPaymentClient = createPaymentClient(http.bff);

export const BFFSalesClient = createSalesClient(http.bff, createBFFErrorHandler('Sales'));

export const BFFFulfillmentClient = createFulfillmentClient(http.bff, {
  onValidateRequestError: () => true,
  onValidateResponseError: () => true,
});

export const BffCheckTimeslotClient = createTimeslotClient(http.bff, createBFFErrorHandler('Timeslot'));
export const BFFTempClient = createTempClient(http.bff, {
  onValidateRequestError: () => true,
  onValidateResponseError: () => true,
});

export const BFFDataMigrationClient = createDataMigrationClient(http.bff, {
  onValidateRequestError: () => true,
  onValidateResponseError: () => true,
});
