import { GroomingServiceAvailabilityModel } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_models';
import { BffCheckTimeslotClient } from '../../middleware/bff';
import { currentBusinessIdBox } from '../../store/business/business.boxes';
import { calendarConfigMapBox } from '../../store/calendarLatest/calendar.boxes';
import { onlineBookPaymentSupportAcceptClientMapBox } from '../../store/onlineBooking/onlineBooking.boxes';
import {
  onlineBookingLatestPreferenceMapBox,
  onlineBookingPreferenceMapBox,
} from '../../store/onlineBooking/onlineBookingPreference.boxes';
import { onlineBookingGroomingServiceAvailabilityBox } from '../../store/onlineBooking/settings/onlineBookingSettings.boxes';
import { createQueryOptions } from '../utils/createQueryOptions';
import { OpenApiDefinitions } from '../../openApi/schema';
import { OnlineBookingPreferenceModel } from '../../store/onlineBooking/models/OnlineBookingPreference';
import { useStore } from 'amos';
import { useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useEnableMultiPetBySlotFeature } from '../../container/Calendar/latest/components/SlotCalendar/hooks/useSlotCalendarFeature';
import { isNormal } from '../../store/utils/identifier';

export const queryTimeslot = {
  checkTimeslotAvailability: createQueryOptions({
    queryKey: (params) => ['timeslot', 'checkTimeslotAvailability', params],
    queryFn: (params: { businessId: number }) => {
      return BffCheckTimeslotClient.checkTimeslotAvailability({
        businessId: params.businessId,
      });
    },
  }),
};

export const useCheckTimeslotAvailability = () => {
  const store = useStore();
  const businessId = store.select(currentBusinessIdBox);
  const isEnableCalendarSlot = useEnableMultiPetBySlotFeature();

  const options = queryTimeslot.checkTimeslotAvailability({ businessId });

  const { data, isLoading } = useQuery({
    ...options,
    enabled: isNormal(businessId) && isEnableCalendarSlot,
  });

  useEffect(() => {
    if (data && !isLoading) {
      const { bookOnlineInfo, bookOnlineNotification, paymentSetting, isMerged, isNew, acceptCustomerType } =
        data.onlineBookingPreference;

      store.dispatch([
        onlineBookingPreferenceMapBox.mergeItem(businessId, {
          ...bookOnlineInfo,
          ...bookOnlineNotification,
          paymentSetting,
          businessId,
        } as OnlineBookingPreferenceModel),
        onlineBookingLatestPreferenceMapBox.mergeItem(businessId, {
          bookingEnable: Number(bookOnlineInfo.isEnable) > 0,
          bookOnlineName: String(bookOnlineInfo.bookOnlineName),
          isMerged,
          isNew,
        }),
        onlineBookPaymentSupportAcceptClientMapBox.mergeItem(businessId, {
          acceptCustomerType:
            acceptCustomerType as OpenApiDefinitions['grooming']['com.moego.server.grooming.web.dto.ob.SettingInfoDto']['acceptCustomerType'],
          businessId,
        }),
        onlineBookingGroomingServiceAvailabilityBox.mergeItem(
          String(businessId),
          data.groomingServiceAvailability as unknown as Partial<
            GroomingServiceAvailabilityModel & { businessId: string }
          >,
        ),
        calendarConfigMapBox.mergeItem(businessId, data.calendarConfig),
      ]);
    }
  }, [data, isLoading, businessId]);

  return { data, isLoading };
};
