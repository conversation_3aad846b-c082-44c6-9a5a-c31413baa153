import { getBookingRequestListForRedDot } from '../onlineBooking/actions/private/onlineBooking.actions';
import { MoeGoNotification, type NotificationType } from './notification.boxes';
import { getFinanceRedDotStatus } from './red-dot.actions';

/**
 * 特定通知类型对应的 RedDot 额外状态刷新函数
 */
export const RefreshRedDotsNotificationTypeMap = new Map<NotificationType, any>([
  [MoeGoNotification.FINANCE_CAPITAL_OFFER_APPROVED, getFinanceRedDotStatus],
  [MoeGoNotification.OB_REQUEST, getBookingRequestListForRedDot],
  [MoeGoNotification.OB_REQUEST_CANCEL, getBookingRequestListForRedDot],
]) satisfies Map<NotificationType, unknown>;
