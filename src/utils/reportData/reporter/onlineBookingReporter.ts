import { apptInfoMapBox } from '../../../container/Appt/store/appt.boxes';
import { selectMainServiceInAppt } from '../../../container/Appt/store/appt.selectors';
import { store } from '../../../provider';
import { type AccountRecord } from '../../../store/account/account.boxes';
import { selectCurrentAccount } from '../../../store/account/account.selectors';
import { type BusinessRecord } from '../../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { calendarQuickAddApptFields } from '../../../store/calendarLatest/calendar.boxes';
import { selectIsEnableSlotCalender } from '../../../store/calendarLatest/calendar.selectors';
import { BookOnlineStatusMap } from '../../../store/onlineBooking/onlineBooking.boxes';
import { reportData } from '../../tracker';
import { GroomingReportActionName } from '../grooming/grooming';

type GroomingBySlotEvent =
  | 'homepageBanner'
  | 'bySlotGuidePopover'
  | 'guideSyncModal'
  | 'timeToBySlotModal'
  | 'willWarnSyncSMModal'
  | 'smPopover';

export function getOBReportApptData(ticketId: number) {
  return {
    care_type: store.select(selectMainServiceInAppt(String(ticketId))).serviceItemType,
    appt_source:
      store.select(apptInfoMapBox.mustGetItem(String(ticketId))).appointment?.bookOnlineStatus ===
      BookOnlineStatusMap.Appointment
        ? 'business side'
        : 'online booking',
    appt_mode: store.select(selectIsEnableSlotCalender) ? 'by slot' : 'by time',
    source: store.select(calendarQuickAddApptFields).source,
  };
}

export class OnlineBookingReporter {
  private accountInfo: AccountRecord;
  private businessInfo: BusinessRecord;

  constructor() {
    const accountInfo = store.select(selectCurrentAccount);
    const businessInfo = store.select(selectCurrentBusiness);
    this.accountInfo = accountInfo;
    this.businessInfo = businessInfo;
  }

  private reportGroomingBySlot(action: string, event: GroomingBySlotEvent) {
    reportData(GroomingReportActionName.GroomingBySlot, {
      action,
      event,
      biz: this.businessInfo.id,
      accountEmail: this.accountInfo.email,
    });
  }

  public reportGroomingBySlotHomepageBanner() {
    this.reportGroomingBySlot('display', 'homepageBanner');
  }

  public reportGroomingBySlotHomepageBannerClose() {
    this.reportGroomingBySlot('close', 'homepageBanner');
  }

  public reportGroomingBySlotHomepageBannerSetUpNow() {
    this.reportGroomingBySlot('setUpNow', 'homepageBanner');
  }

  public reportGroomingBySlotGuidePopover() {
    this.reportGroomingBySlot('display', 'bySlotGuidePopover');
  }

  public reportGroomingBySlotGuidePopoverClose() {
    this.reportGroomingBySlot('close', 'bySlotGuidePopover');
  }

  public reportGroomingBySlotGuidePopoverNotInterested() {
    this.reportGroomingBySlot('notInterested', 'bySlotGuidePopover');
  }

  public reportGroomingBySlotGuidePopoverSetUpNow() {
    this.reportGroomingBySlot('setUpNow', 'bySlotGuidePopover');
  }

  public reportGroomingBySlotGuidePopoverWikiLinkClick() {
    this.reportGroomingBySlot('wikiLinkClick', 'bySlotGuidePopover');
  }

  public reportGroomingBySlotGuideSyncModal() {
    this.reportGroomingBySlot('display', 'guideSyncModal');
  }

  public reportGroomingBySlotGuideSyncModalSaveAndSync() {
    this.reportGroomingBySlot('saveAndSync', 'guideSyncModal');
  }

  public reportGroomingBySlotGuideSyncModalSaveWithoutSync() {
    this.reportGroomingBySlot('saveWithoutSync', 'guideSyncModal');
  }

  public reportGroomingByTimeToBySlotModal() {
    this.reportGroomingBySlot('display', 'timeToBySlotModal');
  }

  public reportGroomingByTimeToBySlotModalConfirm() {
    this.reportGroomingBySlot('confirm', 'timeToBySlotModal');
  }

  public reportGroomingByTimeToBySlotModalCancel() {
    this.reportGroomingBySlot('cancel', 'timeToBySlotModal');
  }

  public reportGroomingWillWarnSyncSMModal() {
    this.reportGroomingBySlot('display', 'willWarnSyncSMModal');
  }

  public reportGroomingWillWarnSyncSMModalConfirm() {
    this.reportGroomingBySlot('confirm', 'willWarnSyncSMModal');
  }

  public reportGroomingWillWarnSyncSMModalCancel() {
    this.reportGroomingBySlot('cancel', 'willWarnSyncSMModal');
  }

  public reportGroomingSMPopover() {
    this.reportGroomingBySlot('display', 'smPopover');
  }

  public reportGroomingSMPopoverClose() {
    this.reportGroomingBySlot('close', 'smPopover');
  }

  public reportGroomingSMPopoverNotInterested() {
    this.reportGroomingBySlot('notInterested', 'smPopover');
  }

  public reportGroomingSMPopoverSetUpNow() {
    this.reportGroomingBySlot('setUpNow', 'smPopover');
  }

  static reportApptClickCheckIn(ticketId: number) {
    reportData(GroomingReportActionName.ApptClickCheckIn, getOBReportApptData(ticketId));
  }

  static reportApptClickCheckOut(ticketId: number) {
    reportData(GroomingReportActionName.ApptClickCheckOut, getOBReportApptData(ticketId));
  }
}
